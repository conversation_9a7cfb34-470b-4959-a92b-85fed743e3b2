class DatabaseManager {
    constructor() {
        this.dbName = 'AIExperienceChat';
        this.dbVersion = 2; // 升级版本以支持PDF文件存储
        this.db = null;
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                const oldVersion = event.oldVersion;
                const newVersion = event.newVersion;

                // 版本1: 创建基础表结构
                if (oldVersion < 1) {
                    // 文档存储
                    if (!db.objectStoreNames.contains('documents')) {
                        const docStore = db.createObjectStore('documents', {
                            keyPath: 'id',
                            autoIncrement: true
                        });
                        docStore.createIndex('filename', 'filename', { unique: false });
                        docStore.createIndex('type', 'type', { unique: false });
                        docStore.createIndex('uploadTime', 'uploadTime', { unique: false });
                    }

                    // 文档片段存储（用于RAG检索）
                    if (!db.objectStoreNames.contains('chunks')) {
                        const chunkStore = db.createObjectStore('chunks', {
                            keyPath: 'id',
                            autoIncrement: true
                        });
                        chunkStore.createIndex('docId', 'docId', { unique: false });
                        chunkStore.createIndex('content', 'content', { unique: false });
                    }

                    // 对话历史存储
                    if (!db.objectStoreNames.contains('conversations')) {
                        const convStore = db.createObjectStore('conversations', {
                            keyPath: 'id',
                            autoIncrement: true
                        });
                        convStore.createIndex('timestamp', 'timestamp', { unique: false });
                    }
                }

                // 版本2: 添加PDF文件存储和位置信息
                if (oldVersion < 2) {
                    // 为chunks表添加新的索引（页码和文本位置）
                    if (db.objectStoreNames.contains('chunks')) {
                        const chunkStore = event.target.transaction.objectStore('chunks');

                        // 添加页码索引
                        if (!chunkStore.indexNames.contains('pageNumber')) {
                            chunkStore.createIndex('pageNumber', 'pageNumber', { unique: false });
                        }

                        // 添加文本位置索引
                        if (!chunkStore.indexNames.contains('textPosition')) {
                            chunkStore.createIndex('textPosition', 'textPosition', { unique: false });
                        }
                    }
                }
            };
        });
    }

    // 文档管理
    async saveDocument(file, content, chunks, fileData = null) {
        const transaction = this.db.transaction(['documents', 'chunks'], 'readwrite');
        const docStore = transaction.objectStore('documents');
        const chunkStore = transaction.objectStore('chunks');

        const document = {
            filename: file.name,
            type: file.type,
            size: file.size,
            content: content,
            fileData: fileData, // 存储原始文件数据（ArrayBuffer）用于PDF渲染
            uploadTime: new Date(),
            lastModified: file.lastModified ? new Date(file.lastModified) : new Date()
        };

        try {
            const docResult = await this.promisifyRequest(docStore.add(document));
            const docId = docResult;

            // 保存文档片段
            for (const chunk of chunks) {
                await this.promisifyRequest(chunkStore.add({
                    docId: docId,
                    content: chunk.content,
                    page: chunk.page || 1,
                    position: chunk.position || 0,
                    pageNumber: chunk.pageNumber || chunk.page || 1, // PDF页码
                    textPosition: chunk.textPosition || null // PDF中的文本位置坐标 {x, y, width, height}
                }));
            }

            return docId;
        } catch (error) {
            throw new Error(`保存文档失败: ${error.message}`);
        }
    }

    async getAllDocuments() {
        const transaction = this.db.transaction(['documents'], 'readonly');
        const store = transaction.objectStore('documents');
        return await this.promisifyRequest(store.getAll());
    }

    async getDocument(id) {
        const transaction = this.db.transaction(['documents'], 'readonly');
        const store = transaction.objectStore('documents');
        return await this.promisifyRequest(store.get(id));
    }

    // 获取文档的原始文件数据（用于PDF渲染）
    async getDocumentFileData(id) {
        const document = await this.getDocument(id);
        return document?.fileData || null;
    }

    // 根据页码获取chunks
    async getChunksByPage(docId, pageNumber) {
        const transaction = this.db.transaction(['chunks'], 'readonly');
        const store = transaction.objectStore('chunks');
        const chunks = await this.promisifyRequest(
            store.index('docId').getAll(docId)
        );
        return chunks.filter(chunk => chunk.pageNumber === pageNumber);
    }

    // 更新文档的fileData（用于重新上传PDF文件数据）
    async updateDocumentFileData(docId, fileData) {
        const transaction = this.db.transaction(['documents'], 'readwrite');
        const store = transaction.objectStore('documents');

        try {
            // 获取现有文档
            const document = await this.promisifyRequest(store.get(docId));
            if (!document) {
                throw new Error('文档不存在');
            }

            // 更新fileData
            document.fileData = fileData;

            // 保存更新
            await this.promisifyRequest(store.put(document));
            return true;
        } catch (error) {
            throw new Error(`更新文档数据失败: ${error.message}`);
        }
    }

    async deleteDocument(id) {
        const transaction = this.db.transaction(['documents', 'chunks'], 'readwrite');
        const docStore = transaction.objectStore('documents');
        const chunkStore = transaction.objectStore('chunks');

        try {
            // 删除文档
            await this.promisifyRequest(docStore.delete(id));

            // 删除相关片段
            const chunks = await this.promisifyRequest(
                chunkStore.index('docId').getAll(id)
            );
            for (const chunk of chunks) {
                await this.promisifyRequest(chunkStore.delete(chunk.id));
            }

            return true;
        } catch (error) {
            throw new Error(`删除文档失败: ${error.message}`);
        }
    }

    // 片段管理（用于RAG检索）
    async getChunksByDocId(docId) {
        const transaction = this.db.transaction(['chunks'], 'readonly');
        const store = transaction.objectStore('chunks');
        return await this.promisifyRequest(
            store.index('docId').getAll(docId)
        );
    }

    async getAllChunks() {
        const transaction = this.db.transaction(['chunks'], 'readonly');
        const store = transaction.objectStore('chunks');
        return await this.promisifyRequest(store.getAll());
    }

    async searchChunks(query, limit = 10) {
        const chunks = await this.getAllChunks();
        const queryLower = query.toLowerCase();

        // 简单的关键词匹配搜索
        const matches = chunks
            .filter(chunk =>
                chunk.content.toLowerCase().includes(queryLower)
            )
            .map(chunk => ({
                ...chunk,
                score: this.calculateRelevanceScore(chunk.content, query)
            }))
            .sort((a, b) => b.score - a.score)
            .slice(0, limit);

        // 获取文档信息
        const results = [];
        for (const match of matches) {
            const doc = await this.getDocument(match.docId);
            results.push({
                ...match,
                filename: doc?.filename || '未知文档'
            });
        }

        return results;
    }

    calculateRelevanceScore(content, query) {
        const contentLower = content.toLowerCase();
        const queryLower = query.toLowerCase();
        const words = queryLower.split(/\s+/);

        let score = 0;

        // 完整查询匹配
        if (contentLower.includes(queryLower)) {
            score += 10;
        }

        // 单词匹配
        words.forEach(word => {
            if (word.length > 2) {
                const count = (contentLower.match(new RegExp(word, 'g')) || []).length;
                score += count * 2;
            }
        });

        return score;
    }

    // 对话历史管理
    async saveConversation(messages) {
        const transaction = this.db.transaction(['conversations'], 'readwrite');
        const store = transaction.objectStore('conversations');

        const conversation = {
            messages: messages,
            timestamp: new Date(),
            messageCount: messages.length
        };

        return await this.promisifyRequest(store.add(conversation));
    }

    async getConversationHistory(limit = 10) {
        const transaction = this.db.transaction(['conversations'], 'readonly');
        const store = transaction.objectStore('conversations');
        const index = store.index('timestamp');

        return await this.promisifyRequest(
            index.getAll(null, limit)
        );
    }

    async clearConversationHistory() {
        const transaction = this.db.transaction(['conversations'], 'readwrite');
        const store = transaction.objectStore('conversations');
        return await this.promisifyRequest(store.clear());
    }

    // 工具方法
    promisifyRequest(request) {
        return new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async clearAllData() {
        const stores = ['documents', 'chunks', 'conversations'];
        const transaction = this.db.transaction(stores, 'readwrite');

        try {
            for (const storeName of stores) {
                const store = transaction.objectStore(storeName);
                await this.promisifyRequest(store.clear());
            }
            return true;
        } catch (error) {
            throw new Error(`清除数据失败: ${error.message}`);
        }
    }

    async getStorageInfo() {
        try {
            const estimate = await navigator.storage.estimate();
            const documents = await this.getAllDocuments();
            const chunks = await this.getAllChunks();

            return {
                used: estimate.usage || 0,
                quota: estimate.quota || 0,
                documentsCount: documents.length,
                chunksCount: chunks.length,
                usagePercent: estimate.quota ?
                    Math.round((estimate.usage / estimate.quota) * 100) : 0
            };
        } catch (error) {
            return {
                used: 0,
                quota: 0,
                documentsCount: 0,
                chunksCount: 0,
                usagePercent: 0
            };
        }
    }
}

// 全局实例
window.dbManager = new DatabaseManager();