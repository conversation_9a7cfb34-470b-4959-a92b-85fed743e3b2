/**
 * 直接创建Excel文件的Node.js脚本
 * 解决浏览器下载问题的替代方案
 */

const fs = require('fs');
const XLSX = require('xlsx');

// 模拟ImagesMetadata的数据结构
const sampleData = [
    {
        path: '示例图片/MLS iOS 13.png',
        description: '这是一个体育比赛管理应用的界面，显示了足球赛事的详细信息。界面采用深色主题设计，展示比赛时间、队伍信息和相关操作按钮。',
        tags: ['体育应用', 'MLS', '足球', '赛事管理', '深色主题'],
        classifications: {
            appCategories: [{ category: '体育', score: 0.9 }, { category: '娱乐', score: 0.7 }],
            pageTypes: [{ category: '详情页', score: 0.8 }, { category: '列表页', score: 0.6 }],
            controlTypes: [{ category: '按钮', score: 0.9 }, { category: '卡片', score: 0.8 }]
        },
        lastUpdated: new Date().toISOString(),
        indexed: true
    },
    {
        path: '示例图片/Fuse iOS 15.png',
        description: '这是一个金融应用的主界面，显示账户余额和交易功能。界面包含多个操作按钮和数据可视化元素。',
        tags: ['金融应用', '数字钱包', '账户管理', '交易功能', '数据可视化'],
        classifications: {
            appCategories: [{ category: '金融', score: 0.95 }, { category: '工具', score: 0.6 }],
            pageTypes: [{ category: '主页', score: 0.9 }, { category: '仪表板', score: 0.8 }],
            controlTypes: [{ category: '按钮', score: 0.9 }, { category: '图表', score: 0.7 }]
        },
        lastUpdated: new Date().toISOString(),
        indexed: true
    }
];

function extractTopCategories(categories) {
    if (!categories || !Array.isArray(categories)) return '';
    return categories
        .sort((a, b) => b.score - a.score)
        .slice(0, 3)
        .map(c => c.category)
        .join(';');
}

function createExcelFile() {
    console.log('📊 开始创建Excel文件...');

    // 准备表格头部
    const headers = [
        '路径',
        '描述', 
        '标签',
        'APP分类',
        '页面类型',
        '控件类型',
        '最后更新时间',
        '状态'
    ];

    // 准备数据行
    const data = [headers];
    
    sampleData.forEach(item => {
        const row = [
            item.path,
            item.description || '',
            (item.tags || []).join(';'),
            extractTopCategories(item.classifications?.appCategories),
            extractTopCategories(item.classifications?.pageTypes),
            extractTopCategories(item.classifications?.controlTypes),
            item.lastUpdated || '',
            item.indexed ? '已索引' : '未索引'
        ];
        data.push(row);
    });

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(data);
    
    // 设置列宽
    const colWidths = [
        { wch: 40 }, // 路径
        { wch: 50 }, // 描述
        { wch: 30 }, // 标签
        { wch: 20 }, // APP分类
        { wch: 20 }, // 页面类型
        { wch: 20 }, // 控件类型
        { wch: 20 }, // 更新时间
        { wch: 10 }  // 状态
    ];
    worksheet['!cols'] = colWidths;
    
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Images');
    
    // 生成文件名
    const filename = `images_index_${new Date().toISOString().slice(0,10).replace(/-/g,'')}.xlsx`;
    
    // 写入文件
    XLSX.writeFile(workbook, filename);
    
    console.log(`✅ Excel文件创建成功: ${filename}`);
    console.log(`📂 文件位置: ${process.cwd()}/${filename}`);
    
    return filename;
}

// 如果直接运行此脚本
if (require.main === module) {
    try {
        createExcelFile();
    } catch (error) {
        console.error('❌ 创建Excel文件失败:', error);
        process.exit(1);
    }
}

module.exports = { createExcelFile, sampleData };