# 体验网站启动器使用说明

## 启动方式

### 方式一：双击应用程序（推荐）
双击 `体验网站启动器.app` 即可自动：
- 启动本地HTTP服务器（端口：8080）
- 自动打开默认浏览器并访问网站首页
- 在终端中显示服务器状态

### 方式二：直接运行脚本
在终端中执行：
```bash
bash /Users/<USER>/Documents/体验网站项目/start_server.sh
```

## 功能特点

✅ **一键启动**：双击即可启动整个网站
✅ **自动打开浏览器**：无需手动输入网址
✅ **端口冲突检测**：如果8080端口被占用会自动释放
✅ **优雅关闭**：按 Ctrl+C 可以安全关闭服务器
✅ **错误提示**：启动失败时会显示详细错误信息

## 访问信息

- **本地地址**：http://localhost:8000
- **服务器端口**：8000 （标准端口，确保数据一致性）
- **项目目录**：/Users/<USER>/Documents/体验网站项目

## 💾 数据保护

您的所有数据（图片索引、API配置、设置等）都保存在浏览器的本地存储中。启动器使用标准端口 8000，保证与之前的数据完全兼容。

### 数据管理工具
- 🔧 **备份和恢复**：访问 [http://localhost:8000/backup_restore_data.html](http://localhost:8000/backup_restore_data.html) 对数据进行备份、恢复和管理
- 📊 **数据统计**：查看当前存储的图片数量和API配置状态

## 系统要求

- macOS 10.9 或更高版本
- Python 3.x 或 Python 2.x（用于HTTP服务器）
- curl 命令（用于检测服务器状态）

## 故障排除

### 问题1：应用程序无法启动
- 确保脚本有执行权限：`chmod +x start_server.sh`
- 检查Python是否已安装：`python3 --version`

### 问题2：端口8080被占用
启动器会自动检测并释放被占用的端口，如果仍有问题：
```bash
# 手动查看端口占用
lsof -i :8080
# 手动终止进程
kill -9 <进程ID>
```

### 问题3：浏览器没有自动打开
手动访问：http://localhost:8000

### 问题4：之前的数据（图片索引、API配置）丢失
这可能是端口不一致导致的。解决方案：
1. 确保使用正确的端口 8000访问
2. 如果依然没有数据，访问数据管理工具进行恢复
3. 检查浏览器是否禁用了localStorage

## 自定义配置

可以编辑 `start_server.sh` 文件来修改：
- 服务器端口（默认：8000，请谨慎修改以避免数据丢失）
- 项目目录路径
- 浏览器打开行为

**重要提示：** 如果修改了端口，请先备份您的数据，因为不同端口的localStorage数据是隔离的。

---

**提示**：第一次运行时，macOS可能会询问是否允许运行该应用程序，请点击"打开"确认。