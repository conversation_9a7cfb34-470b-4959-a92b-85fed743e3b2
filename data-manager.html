<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据管理工具 - 文件存储版</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .data-manager {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .manager-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .manager-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        .section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }
        .section-icon {
            width: 24px;
            height: 24px;
        }
        .btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px 0;
            width: 100%;
            transition: background 0.2s;
        }
        .btn:hover { background: #0056CC; }
        .btn.secondary { background: #6c757d; }
        .btn.secondary:hover { background: #545b62; }
        .btn.success { background: #28a745; }
        .btn.success:hover { background: #1e7e34; }
        .btn.danger { background: #dc3545; }
        .btn.danger:hover { background: #c82333; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007AFF;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        
        .status-bar {
            margin: 15px 0;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            display: none;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-info {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #b3d9ff;
        }
        
        .file-input {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
            margin: 10px 0;
            cursor: pointer;
            transition: border-color 0.2s;
        }
        .file-input:hover {
            border-color: #007AFF;
        }
        
        .progress-section {
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #007AFF;
            width: 0%;
            transition: width 0.3s ease;
        }
        .progress-text {
            margin-top: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.1);
            border: none;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.location.href='index.html'">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m12 19-7-7 7-7"/>
        </svg>
    </button>

    <div class="data-manager">
        <div class="manager-header">
            <h1>📊 数据管理工具</h1>
            <p>管理图片索引数据，支持文件存储和CSV编辑</p>
        </div>

        <div class="manager-sections">
            <!-- 数据统计 -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">📈</span>
                    数据统计
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalImages">-</div>
                        <div class="stat-label">总图片数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="indexedImages">-</div>
                        <div class="stat-label">已索引</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="appCategories">-</div>
                        <div class="stat-label">APP分类</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="pageTypes">-</div>
                        <div class="stat-label">页面类型</div>
                    </div>
                </div>
                <button class="btn secondary" onclick="refreshStats()">刷新统计</button>
                <div class="status-bar" id="statsStatus"></div>
            </div>

            <!-- 文件导出 -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">📤</span>
                    数据导出
                </div>
                <p>将当前数据导出为文件，可用于备份或在Excel中编辑</p>
                <button class="btn success" onclick="exportToJson()">导出JSON文件</button>
                <button class="btn success" onclick="exportToCsv()">导出CSV表格</button>
                <button class="btn secondary" onclick="exportBackup()">创建备份</button>
                <div class="status-bar" id="exportStatus"></div>
            </div>

            <!-- 文件导入 -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">📥</span>
                    数据导入
                </div>
                <p>从文件导入数据，支持JSON和CSV格式</p>
                <div class="file-input" onclick="importFromJson()">
                    📄 选择JSON文件导入
                </div>
                <div class="file-input" onclick="importFromCsv()">
                    📊 选择CSV文件导入
                </div>
                <div class="status-bar" id="importStatus"></div>
            </div>

            <!-- 索引操作 -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">🔄</span>
                    图片索引
                </div>
                <p>重新扫描和索引所有图片（106张图片）</p>
                <button class="btn" onclick="startFullIndex()">开始完整索引</button>
                <button class="btn secondary" onclick="startPartialIndex()">仅索引未处理图片</button>
                <div class="progress-section" id="indexProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>
                <div class="status-bar" id="indexStatus"></div>
            </div>

            <!-- 存储设置 -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">⚙️</span>
                    存储设置
                </div>
                <p>配置数据存储方式和同步选项</p>
                <label>
                    <input type="checkbox" id="useFileStorage" checked onchange="toggleStorageMode()">
                    使用文件存储（推荐）
                </label>
                <br><br>
                <button class="btn secondary" onclick="migrateFromLocalStorage()">从localStorage迁移</button>
                <button class="btn danger" onclick="clearAllData()">清除所有数据</button>
                <div class="status-bar" id="settingsStatus"></div>
            </div>

            <!-- Excel实时同步 -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">🔄</span>
                    Excel实时同步
                </div>
                <p>直接使用本地Excel文件维护数据，修改后自动同步到网站</p>
                <div id="excelSyncStatus" style="margin: 15px 0; padding: 10px; border-radius: 5px; font-size: 14px;">
                    <strong>状态：</strong><span id="syncStatusText">未连接</span><br>
                    <span id="syncFileInfo" style="color: #666;"></span>
                </div>
                <button class="btn" onclick="createExcelSync()"><span id="createExcelBtnText">🎆 创建Excel文件</span></button>
                <button class="btn secondary" onclick="selectExcelFile()" id="selectExcelBtn">📂 选择现有Excel文件</button>
                <button class="btn success" onclick="startExcelMonitoring()" id="startMonitorBtn" disabled>👁️ 开始监控</button>
                <button class="btn danger" onclick="stopExcelMonitoring()" id="stopMonitorBtn" disabled>⏹️ 停止监控</button>
                <div class="status-bar" id="excelSyncStatusBar"></div>
            </div>

            <!-- CSV编辑指南 -->
            <div class="section">
                <div class="section-title">
                    <span class="section-icon">📝</span>
                    CSV编辑指南
                </div>
                <p>导出的CSV文件可以在Excel或Numbers中编辑</p>
                <ul style="font-size: 14px; color: #666; line-height: 1.6;">
                    <li><strong>路径</strong>: 图片相对路径（不可修改）</li>
                    <li><strong>描述</strong>: 图片描述文字</li>
                    <li><strong>标签</strong>: 用分号";" 分隔多个标签</li>
                    <li><strong>APP分类</strong>: 用分号分隔多个分类</li>
                    <li><strong>页面类型</strong>: 用分号分隔</li>
                    <li><strong>控件类型</strong>: 用分号分隔</li>
                </ul>
                <p style="font-size: 12px; color: #999;">编辑完成后保存为CSV文件，然后使用"数据导入"功能重新导入</p>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <!-- SheetJS库 -->
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- FileSaver.js库 - 用于更可靠的文件下载 -->
    <script src="https://unpkg.com/file-saver@2.0.5/dist/FileSaver.min.js"></script>
    <script src="file-storage-manager.js"></script>
    <script src="excel-sync-manager.js"></script>
    <script src="images-metadata.js"></script>
    <script src="image-classifier.js"></script>
    <script src="image-indexer.js"></script>
    
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('[DataManager] 页面初始化');
            
            // 初始化文件存储管理器
            if (window.fileStorageManager) {
                await window.fileStorageManager.init();
            }
            
            // 初始化Excel同步管理器
            if (window.excelSyncManager) {
                await window.excelSyncManager.init();
                updateExcelSyncStatus();
            }
            
            // 刷新统计信息
            await refreshStats();
        });

        // 刷新数据统计
        async function refreshStats() {
            try {
                showStatus('statsStatus', '正在加载统计信息...', 'info');
                
                if (!window.imagesMetadata) {
                    throw new Error('ImagesMetadata 未初始化');
                }
                
                const stats = window.imagesMetadata.getStats();
                
                document.getElementById('totalImages').textContent = stats.totalImages || 0;
                document.getElementById('indexedImages').textContent = stats.indexedImages || 0;
                document.getElementById('appCategories').textContent = stats.categoryCounts?.app || 0;
                document.getElementById('pageTypes').textContent = stats.categoryCounts?.page || 0;
                
                showStatus('statsStatus', `统计数据已更新 (${stats.totalImages} 张图片)`, 'success');
            } catch (error) {
                showStatus('statsStatus', `加载统计失败: ${error.message}`, 'error');
            }
        }

        // 导出JSON
        async function exportToJson() {
            try {
                showStatus('exportStatus', '正在导出JSON文件...', 'info');
                
                if (!window.imagesMetadata) {
                    throw new Error('ImagesMetadata 未初始化');
                }
                
                const success = await window.imagesMetadata.exportToJson();
                if (success) {
                    showStatus('exportStatus', 'JSON文件导出成功！', 'success');
                } else {
                    throw new Error('导出失败');
                }
            } catch (error) {
                showStatus('exportStatus', `导出失败: ${error.message}`, 'error');
            }
        }

        // 导出CSV
        async function exportToCsv() {
            try {
                showStatus('exportStatus', '正在导出CSV文件...', 'info');
                
                if (!window.imagesMetadata) {
                    throw new Error('ImagesMetadata 未初始化');
                }
                
                const success = await window.imagesMetadata.exportToCsv();
                if (success) {
                    showStatus('exportStatus', 'CSV文件导出成功！可用Excel编辑', 'success');
                } else {
                    throw new Error('导出失败');
                }
            } catch (error) {
                showStatus('exportStatus', `导出失败: ${error.message}`, 'error');
            }
        }

        // 创建备份
        async function exportBackup() {
            try {
                showStatus('exportStatus', '正在创建备份...', 'info');
                
                if (!window.fileStorageManager || !window.imagesMetadata) {
                    throw new Error('存储管理器未初始化');
                }
                
                await window.fileStorageManager.createBackup(window.imagesMetadata.images);
                showStatus('exportStatus', '备份创建成功！', 'success');
            } catch (error) {
                showStatus('exportStatus', `创建备份失败: ${error.message}`, 'error');
            }
        }

        // 从JSON导入
        async function importFromJson() {
            try {
                showStatus('importStatus', '请选择JSON文件...', 'info');
                
                if (!window.imagesMetadata) {
                    throw new Error('ImagesMetadata 未初始化');
                }
                
                const success = await window.imagesMetadata.importFromJson();
                if (success) {
                    showStatus('importStatus', 'JSON数据导入成功！', 'success');
                    await refreshStats();
                } else {
                    throw new Error('导入失败或取消');
                }
            } catch (error) {
                showStatus('importStatus', `导入失败: ${error.message}`, 'error');
            }
        }

        // 从CSV导入
        async function importFromCsv() {
            try {
                showStatus('importStatus', '请选择CSV文件...', 'info');
                
                if (!window.imagesMetadata) {
                    throw new Error('ImagesMetadata 未初始化');
                }
                
                const success = await window.imagesMetadata.importFromCsv();
                if (success) {
                    showStatus('importStatus', 'CSV数据导入成功！', 'success');
                    await refreshStats();
                } else {
                    throw new Error('导入失败或取消');
                }
            } catch (error) {
                showStatus('importStatus', `导入失败: ${error.message}`, 'error');
            }
        }

        // 开始完整索引
        async function startFullIndex() {
            if (!confirm('将重新索引所有106张图片，这可能需要较长时间。继续吗？')) {
                return;
            }
            
            try {
                showStatus('indexStatus', '开始完整索引...', 'info');
                document.getElementById('indexProgress').style.display = 'block';
                
                // 这里需要调用图片索引器
                // 暂时模拟进度
                await simulateIndexProgress();
                
                showStatus('indexStatus', '索引完成！', 'success');
                await refreshStats();
            } catch (error) {
                showStatus('indexStatus', `索引失败: ${error.message}`, 'error');
            }
        }

        // 开始部分索引
        async function startPartialIndex() {
            try {
                showStatus('indexStatus', '开始增量索引...', 'info');
                document.getElementById('indexProgress').style.display = 'block';
                
                // 这里需要调用图片索引器的增量索引
                await simulateIndexProgress();
                
                showStatus('indexStatus', '增量索引完成！', 'success');
                await refreshStats();
            } catch (error) {
                showStatus('indexStatus', `索引失败: ${error.message}`, 'error');
            }
        }

        // 模拟索引进度
        async function simulateIndexProgress() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            for (let i = 0; i <= 100; i += 10) {
                progressFill.style.width = i + '%';
                progressText.textContent = `正在处理... ${i}%`;
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            document.getElementById('indexProgress').style.display = 'none';
        }

        // 切换存储模式
        function toggleStorageMode() {
            const useFileStorage = document.getElementById('useFileStorage').checked;
            if (window.imagesMetadata) {
                window.imagesMetadata.useFileStorage = useFileStorage;
                showStatus('settingsStatus', 
                    `已切换到${useFileStorage ? '文件存储' : 'localStorage'}模式`, 'success');
            }
        }

        // 从localStorage迁移
        async function migrateFromLocalStorage() {
            try {
                showStatus('settingsStatus', '正在迁移数据...', 'info');
                
                if (!window.imagesMetadata) {
                    throw new Error('ImagesMetadata 未初始化');
                }
                
                // 强制从localStorage加载数据
                window.imagesMetadata.loadFromLocalStorage();
                
                // 然后保存到文件存储
                await window.imagesMetadata.saveToFileStorage();
                
                showStatus('settingsStatus', '数据迁移成功！', 'success');
                await refreshStats();
            } catch (error) {
                showStatus('settingsStatus', `迁移失败: ${error.message}`, 'error');
            }
        }

        // 清除所有数据
        function clearAllData() {
            if (!confirm('确定要清除所有数据吗？此操作不可撤销！')) {
                return;
            }
            
            if (!confirm('再次确认：这将删除所有图片索引数据！')) {
                return;
            }
            
            try {
                if (window.imagesMetadata) {
                    window.imagesMetadata.images.clear();
                    window.imagesMetadata.saveToLocalStorage();
                }
                
                localStorage.clear();
                showStatus('settingsStatus', '所有数据已清除', 'success');
                refreshStats();
            } catch (error) {
                showStatus('settingsStatus', `清除失败: ${error.message}`, 'error');
            }
        }

        // 显示状态消息
        function showStatus(elementId, message, type = 'info') {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = `status-bar status-${type}`;
            statusEl.style.display = 'block';
            
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 5000);
            }
        }

        // ================== Excel同步功能 ==================

        /**
         * 更新Excel同步状态显示
         */
        function updateExcelSyncStatus() {
            if (!window.excelSyncManager) return;
            
            const status = window.excelSyncManager.getSyncStatus();
            const statusText = document.getElementById('syncStatusText');
            const fileInfo = document.getElementById('syncFileInfo');
            const statusDiv = document.getElementById('excelSyncStatus');
            const startBtn = document.getElementById('startMonitorBtn');
            const stopBtn = document.getElementById('stopMonitorBtn');
            const createBtn = document.getElementById('createExcelBtnText');
            
            if (status.hasFile) {
                statusText.textContent = status.isMonitoring ? '监控中' : '已连接';
                fileInfo.textContent = `文件: ${status.fileName}`;
                statusDiv.style.backgroundColor = status.isMonitoring ? '#d4edda' : '#cce7ff';
                statusDiv.style.border = status.isMonitoring ? '1px solid #c3e6cb' : '1px solid #b3d9ff';
                
                startBtn.disabled = status.isMonitoring || !status.canWrite;
                stopBtn.disabled = !status.isMonitoring;
                createBtn.textContent = '🔄 更新Excel文件';
            } else {
                statusText.textContent = '未连接';
                fileInfo.textContent = '请选择或创建 Excel 文件';
                statusDiv.style.backgroundColor = '#f8f9fa';
                statusDiv.style.border = '1px solid #dee2e6';
                
                startBtn.disabled = true;
                stopBtn.disabled = true;
                createBtn.textContent = '🎆 创建Excel文件';
            }
        }

        /**
         * 创建或更新Excel文件
         */
        async function createExcelSync() {
            try {
                showStatus('excelSyncStatusBar', '正在创建/更新Excel文件...', 'info');
                
                // 检查系统初始化状态
                if (!window.imagesMetadata) {
                    throw new Error('ImagesMetadata 未初始化');
                }
                
                if (!window.excelSyncManager) {
                    throw new Error('ExcelSyncManager 未初始化');
                }
                
                // 检查XLSX库是否加载
                if (typeof XLSX === 'undefined') {
                    throw new Error('XLSX库未加载，请检查网络连接');
                }
                
                const status = window.excelSyncManager.getSyncStatus();
                
                if (status.hasFile && status.canWrite) {
                    // 更新现有文件
                    await window.excelSyncManager.writeExcelData(window.imagesMetadata.images);
                    showStatus('excelSyncStatusBar', 'Excel文件已更新！', 'success');
                } else {
                    // 创建新文件
                    await window.excelSyncManager.createInitialExcelFile(window.imagesMetadata.images);
                    showStatus('excelSyncStatusBar', 'Excel文件创建成功！请保存后选择文件开始同步', 'success');
                }
                
                updateExcelSyncStatus();
                
            } catch (error) {
                showStatus('excelSyncStatusBar', `操作失败: ${error.message}`, 'error');
            }
        }

        /**
         * 选择Excel文件
         */
        async function selectExcelFile() {
            try {
                showStatus('excelSyncStatusBar', '请选择Excel文件...', 'info');
                
                if (!window.excelSyncManager) {
                    throw new Error('Excel同步管理器未初始化');
                }
                
                const file = await window.excelSyncManager.selectExcelFile();
                
                if (file) {
                    showStatus('excelSyncStatusBar', `已选择文件: ${file.name}`, 'success');
                    
                    // 询问是否要从 Excel 文件加载数据
                    if (confirm('是否要从此Excel文件加载数据到网站？')) {
                        await loadDataFromExcel();
                    }
                }
                
                updateExcelSyncStatus();
                
            } catch (error) {
                showStatus('excelSyncStatusBar', `选择文件失败: ${error.message}`, 'error');
            }
        }

        /**
         * 从 Excel 加载数据
         */
        async function loadDataFromExcel() {
            try {
                showStatus('excelSyncStatusBar', '正在从 Excel 加载数据...', 'info');
                
                const imagesMap = await window.excelSyncManager.readExcelData();
                
                if (imagesMap.size > 0) {
                    // 更新到ImagesMetadata
                    window.imagesMetadata.images = imagesMap;
                    await window.imagesMetadata.saveToStorage();
                    
                    showStatus('excelSyncStatusBar', `已从 Excel 加载 ${imagesMap.size} 条记录`, 'success');
                    await refreshStats();
                } else {
                    showStatus('excelSyncStatusBar', 'Excel文件为空或格式不正确', 'error');
                }
                
            } catch (error) {
                showStatus('excelSyncStatusBar', `加载数据失败: ${error.message}`, 'error');
            }
        }

        /**
         * 开始监控Excel文件变化
         */
        function startExcelMonitoring() {
            try {
                if (!window.excelSyncManager) {
                    throw new Error('Excel同步管理器未初始化');
                }
                
                // 开始监控，设置数据变化回调
                window.excelSyncManager.startMonitoring(async (newData) => {
                    try {
                        // 更新数据
                        window.imagesMetadata.images = newData;
                        await window.imagesMetadata.saveToStorage();
                        
                        showStatus('excelSyncStatusBar', `检测到Excel文件变化，已同步 ${newData.size} 条记录`, 'success');
                        await refreshStats();
                        
                    } catch (error) {
                        showStatus('excelSyncStatusBar', `同步数据失败: ${error.message}`, 'error');
                    }
                });
                
                showStatus('excelSyncStatusBar', 'Excel文件监控已开始，修改文件后会自动同步', 'success');
                updateExcelSyncStatus();
                
            } catch (error) {
                showStatus('excelSyncStatusBar', `开始监控失败: ${error.message}`, 'error');
            }
        }

        /**
         * 停止监控Excel文件
         */
        function stopExcelMonitoring() {
            try {
                if (!window.excelSyncManager) {
                    throw new Error('Excel同步管理器未初始化');
                }
                
                window.excelSyncManager.stopMonitoring();
                showStatus('excelSyncStatusBar', 'Excel文件监控已停止', 'info');
                updateExcelSyncStatus();
                
            } catch (error) {
                showStatus('excelSyncStatusBar', `停止监控失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>