/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
    color: #1a1a1a;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.top-nav {
    background-color: #1a1a1a;
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid #333;
    flex-shrink: 0;
}

.nav-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-left {
    display: flex;
    align-items: center;
}

.logo {
    color: white;
    display: flex;
    align-items: center;
}

.nav-right {
    display: flex;
    align-items: center;
}

.nav-btn {
    background: transparent;
    border: 1px solid #444;
    color: #ccc;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.nav-btn:hover {
    background-color: #333;
    color: white;
}

/* 主体容器 */
.main-container {
    flex: 1;
    display: flex;
    height: calc(100vh - 60px);
    overflow: hidden;
}

/* 左侧来源面板 */
.sources-panel {
    width: 50%;
    background-color: white;
    border-right: 1px solid #e1e5e9;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.sources-header {
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sources-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.upload-btn {
    background: #007aff;
    border: none;
    cursor: pointer;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
}

.upload-btn:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.sources-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
}

.expand-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.expand-btn:hover {
    background-color: #f0f0f0;
}

.sources-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.source-item {
    margin-bottom: 20px;
}

.source-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #1a1a1a;
}

.source-icon {
    font-size: 16px;
}

.source-external {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 2px;
    border-radius: 3px;
    margin-left: auto;
}

.source-external:hover {
    color: #007aff;
}

.source-dropdown {
    margin-bottom: 16px;
}

.dropdown-btn {
    width: 100%;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #1a1a1a;
    transition: all 0.2s ease;
}

.dropdown-btn:hover {
    background-color: #e9ecef;
}

.source-sections {
    space-y: 16px;
}

.source-section {
    margin-bottom: 16px;
}

.source-section h4 {
    font-size: 13px;
    font-weight: 600;
    color: #666;
    margin-bottom: 8px;
}

.source-text {
    font-size: 13px;
    line-height: 1.5;
    color: #555;
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #007aff;
}

/* 右侧对话面板 */
.chat-panel {
    width: 50%;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
}

.chat-header {
    padding: 20px 30px;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
}

.chat-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
}

.chat-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 消息样式 */
.message {
    max-width: 100%;
}

.message.user {
    align-self: flex-end;
}

.message.assistant {
    align-self: flex-start;
}

.message-content {
    padding: 16px 20px;
    border-radius: 16px;
    font-size: 15px;
    line-height: 1.6;
}

.message.user .message-content {
    background-color: #007aff;
    color: white;
    border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
    background-color: #f8f9fa;
    color: #1a1a1a;
    border-bottom-left-radius: 4px;
    border: 1px solid #e1e5e9;
}

.message.assistant .message-content p {
    margin-bottom: 12px;
}

.message.assistant .message-content p:last-child {
    margin-bottom: 0;
}

/* 引用标记 */
.citation {
    display: inline-block;
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin: 0 2px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.citation:hover {
    background-color: #bbdefb;
}

/* 底部输入区域 */
.chat-input-container {
    padding: 20px 30px 30px;
    border-top: 1px solid #e1e5e9;
    background-color: white;
    flex-shrink: 0;
}

.chat-input-box {
    position: relative;
}

.chat-input {
    width: 100%;
    padding: 16px 20px;
    border: 1px solid #e1e5e9;
    border-radius: 24px;
    font-size: 15px;
    outline: none;
    transition: all 0.2s ease;
    background-color: #f8f9fa;
}

.chat-input:focus {
    border-color: #007aff;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.input-suggestions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;
}

.suggestion-btn {
    background-color: #f0f8ff;
    border: 1px solid #d0e7ff;
    color: #0066cc;
    padding: 8px 12px;
    border-radius: 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-btn:hover {
    background-color: #e3f2fd;
    border-color: #90caf9;
}

.input-counter {
    position: absolute;
    right: 20px;
    bottom: -30px;
    font-size: 12px;
    color: #666;
}

/* 加载动画 */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 16px 20px;
    background-color: #f8f9fa;
    border-radius: 16px;
    border-bottom-left-radius: 4px;
    border: 1px solid #e1e5e9;
    width: fit-content;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background-color: #999;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.3;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .sources-panel {
        width: 100%;
        height: 200px;
    }

    .chat-content {
        padding: 20px;
    }

    .chat-input-container {
        padding: 15px 20px 20px;
    }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 折叠状态 */
.sources-panel.collapsed {
    width: 60px;
}

.sources-panel.collapsed .sources-content {
    display: none;
}

.sources-panel.collapsed .sources-header h3 {
    display: none;
}

/* API设置弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    line-height: 1;
}

.close-btn:hover {
    background-color: #f0f0f0;
    color: #1a1a1a;
}

.modal-body {
    padding: 24px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #1a1a1a;
}

.setting-group input,
.setting-group select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.setting-group input:focus,
.setting-group select:focus {
    outline: none;
    border-color: #007aff;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.help-text {
    display: block;
    margin-top: 6px;
    font-size: 12px;
    color: #666;
}

.help-text a {
    color: #007aff;
    text-decoration: none;
}

.help-text a:hover {
    text-decoration: underline;
}

.api-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #dc3545;
}

.status-indicator.connected {
    background-color: #28a745;
}

.status-indicator.connecting {
    background-color: #ffc107;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-text {
    font-size: 14px;
    color: #666;
}

.modal-footer {
    padding: 16px 24px 24px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.btn-primary,
.btn-secondary {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background-color: #007aff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-primary:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #666;
    border: 1px solid #e1e5e9;
}

.btn-secondary:hover {
    background-color: #e9ecef;
    color: #1a1a1a;
}

/* 文件上传相关样式 */
.upload-progress {
    margin-top: 16px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.upload-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 8px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e1e5e9;
}

.upload-item:last-child {
    margin-bottom: 0;
}

.file-icon {
    font-size: 16px;
    color: #666;
}

.file-info {
    flex: 1;
}

.file-name {
    font-size: 13px;
    font-weight: 500;
    color: #1a1a1a;
    margin-bottom: 2px;
}

.file-status {
    font-size: 12px;
    color: #666;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: #e1e5e9;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 6px;
}

.progress-fill {
    height: 100%;
    background-color: #007aff;
    transition: width 0.3s ease;
    border-radius: 2px;
}

.error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 4px;
}

.success-message {
    color: #28a745;
    font-size: 12px;
    margin-top: 4px;
}

/* 文档列表样式 */
.document-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background-color: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.document-item:hover {
    border-color: #007aff;
    background-color: #f8fbff;
}

.document-icon {
    font-size: 16px;
    color: #007aff;
}

.document-info {
    flex: 1;
}

.document-name {
    font-size: 13px;
    font-weight: 500;
    color: #1a1a1a;
    margin-bottom: 2px;
}

.document-meta {
    font-size: 11px;
    color: #666;
}

.document-actions {
    display: flex;
    gap: 4px;
}

.doc-action-btn {
    background: none;
    border: none;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    color: #666;
    transition: all 0.2s ease;
}

.doc-action-btn:hover {
    background-color: #f0f0f0;
    color: #dc3545;
}

/* PDF文档阅读视图样式 */
.pdf-document-reader {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f8f9fa;
}

.pdf-reader-container {
    flex: 1;
    overflow: hidden;
}

/* PDF查看器样式 */
.pdf-viewer {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f5f5f5;
}

.pdf-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: #fff;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
}

.pdf-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pdf-zoom-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pdf-btn {
    background: none;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    padding: 4px 8px;
    cursor: pointer;
    color: #656d76;
    font-size: 12px;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pdf-btn:hover {
    background-color: #f3f4f6;
    border-color: #b0b7c3;
}

.pdf-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pdf-page-info {
    font-size: 12px;
    color: #656d76;
    padding: 0 8px;
}

.pdf-zoom-level {
    font-size: 12px;
    color: #656d76;
    min-width: 40px;
    text-align: center;
}

.pdf-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f5f5f5;
}

.pdf-page-container {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
}

.pdf-page-wrapper {
    position: relative;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.pdf-page-wrapper:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pdf-page-canvas {
    display: block;
    max-width: 100%;
    height: auto;
}

.pdf-page-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.pdf-text-item {
    position: absolute;
    color: transparent;
    cursor: text;
    pointer-events: auto;
    user-select: text;
    line-height: 1;
}

.pdf-text-highlight {
    border-radius: 2px;
    z-index: 10;
}

.pdf-page-number {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.pdf-page-wrapper:hover .pdf-page-number {
    opacity: 1;
}

.pdf-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #666;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e1e5e9;
    border-top: 2px solid #007aff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #666;
}

.pdf-error {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.error-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.error-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 8px;
    text-align: center;
}

.error-message {
    font-size: 14px;
    text-align: center;
    margin-bottom: 20px;
}

/* PDF降级显示样式 */
.pdf-fallback {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-top: 20px;
    max-width: 400px;
}

.pdf-fallback h4 {
    font-size: 14px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 12px;
}

.pdf-text-content {
    background-color: white;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 12px;
    font-size: 13px;
    line-height: 1.5;
    color: #666;
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 16px;
    white-space: pre-wrap;
}

.reupload-btn {
    background-color: #007aff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 0 auto;
}

.reupload-btn:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

/* 高亮动画 */
@keyframes highlight-flash {
    0% {
        background-color: #fff3cd;
        opacity: 0.8;
    }
    50% {
        background-color: #ffc107;
        opacity: 1;
    }
    100% {
        background-color: #fff3cd;
        opacity: 0.6;
    }
}

/* 文档阅读视图样式 */
.document-reader {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.document-reader-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e1e5e9;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

.back-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.back-btn:hover {
    background-color: #e9ecef;
    color: #1a1a1a;
}

.document-reader-title {
    font-size: 14px;
    font-weight: 500;
    color: #1a1a1a;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.document-reader-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: white;
}

.document-chunk {
    margin-bottom: 16px;
    padding: 12px;
    border-radius: 8px;
    background-color: #fafbfc;
    border: 1px solid #e1e5e9;
    font-size: 14px;
    line-height: 1.6;
    transition: all 0.3s ease;
}

.document-chunk.highlighted {
    background-color: #fff3cd;
    border-color: #ffc107;
    animation: highlight-flash 2s ease-out;
}

@keyframes highlight-flash {
    0% { background-color: #fff3cd; }
    100% { background-color: #fafbfc; }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state-text {
    font-size: 14px;
    margin-bottom: 8px;
}

.empty-state-subtext {
    font-size: 12px;
    opacity: 0.7;
}

/* 欢迎页面样式 */
.message.welcome .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px;
    border-radius: 16px;
    border: none;
}

.welcome-section h3 {
    margin: 0 0 16px 0;
    font-size: 20px;
    font-weight: 600;
}

.welcome-section p {
    margin: 0 0 12px 0;
    opacity: 0.9;
    line-height: 1.5;
}

.welcome-section ul {
    margin: 16px 0;
    padding-left: 20px;
}

.welcome-section li {
    margin-bottom: 8px;
    line-height: 1.4;
    opacity: 0.9;
}

.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 20px;
}

.quick-actions .suggestion-btn {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-actions .suggestion-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

/* 流式响应中的消息处理 */
.message.assistant .message-content {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Markdown 渲染样式优化 */
.message.assistant .message-content h1 {
    font-size: 20px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 20px 0 12px 0;
    line-height: 1.3;
    border-bottom: 2px solid #e1e5e9;
    padding-bottom: 8px;
}

.message.assistant .message-content h2 {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 18px 0 10px 0;
    line-height: 1.3;
    border-bottom: 1px solid #e1e5e9;
    padding-bottom: 6px;
}

.message.assistant .message-content h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 16px 0 8px 0;
    line-height: 1.3;
}

.message.assistant .message-content h4 {
    font-size: 15px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 14px 0 6px 0;
    line-height: 1.3;
}

.message.assistant .message-content h5,
.message.assistant .message-content h6 {
    font-size: 14px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 12px 0 6px 0;
    line-height: 1.3;
}

.message.assistant .message-content p {
    margin-bottom: 14px;
    line-height: 1.7;
}

.message.assistant .message-content strong {
    font-weight: 600;
    color: #1a1a1a;
}

.message.assistant .message-content em {
    font-style: italic;
    color: #555;
}

.message.assistant .message-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    font-size: 14px;
}

.message.assistant .message-content table th,
.message.assistant .message-content table td {
    border: 1px solid #e1e5e9;
    padding: 8px 12px;
    text-align: left;
}

.message.assistant .message-content table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #1a1a1a;
}

.message.assistant .message-content table tr:nth-child(even) {
    background-color: #fafbfc;
}

/* 代码块样式 */
.message.assistant .message-content pre {
    background-color: #f5f5f5;
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 8px 0;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.message.assistant .message-content code {
    background-color: #f0f0f0;
    padding: 2px 4px;
    border-radius: 4px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 13px;
}

/* 列表样式优化 */
.message.assistant .message-content ul,
.message.assistant .message-content ol {
    margin: 8px 0;
    padding-left: 20px;
}

.message.assistant .message-content li {
    margin-bottom: 4px;
    line-height: 1.5;
}

/* 引用样式优化 */
.message.assistant .message-content blockquote {
    margin: 12px 0;
    padding: 8px 16px;
    border-left: 4px solid #007aff;
    background-color: #f8fbff;
    font-style: italic;
}

/* 加载状态优化 */
.message .message-content:empty::after {
    content: '|';
    animation: blink 1s infinite;
    opacity: 0.5;
}

@keyframes blink {
    0%, 50% { opacity: 0.5; }
    51%, 100% { opacity: 0; }
}