<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据备份和恢复工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background: #f5f5f7;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 { color: #333; margin-bottom: 30px; text-align: center; }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .btn:hover { background: #0056CC; }
        .btn.danger { background: #FF3B30; }
        .btn.danger:hover { background: #D70015; }
        .info {
            background: #E3F2FD;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #2196F3;
        }
        textarea {
            width: 100%;
            height: 200px;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数据备份和恢复工具</h1>
        
        <div class="info">
            <strong>说明：</strong>该工具可以帮助您备份和恢复网站的本地数据，包括图片索引信息和API配置。
            在更换浏览器、清除缓存或迁移数据时非常有用。
        </div>

        <div class="section">
            <h2>📤 备份数据</h2>
            <p>点击下方按钮导出您当前的所有数据：</p>
            <button class="btn" onclick="exportData()">导出数据</button>
            <button class="btn" onclick="exportToFile()">保存到文件</button>
            <div class="status" id="exportStatus"></div>
            <textarea id="exportData" placeholder="备份数据将显示在这里..."></textarea>
        </div>

        <div class="section">
            <h2>📥 恢复数据</h2>
            <p>将备份的数据粘贴到下方文本框，然后点击恢复：</p>
            <textarea id="importData" placeholder="请粘贴备份数据..."></textarea>
            <br>
            <input type="file" id="fileInput" accept=".json" style="margin: 10px 0;">
            <br>
            <button class="btn" onclick="importData()">恢复数据</button>
            <button class="btn" onclick="loadFromFile()">从文件加载</button>
            <div class="status" id="importStatus"></div>
        </div>

        <div class="section">
            <h2>🔍 数据状态</h2>
            <p>查看当前存储的数据统计：</p>
            <button class="btn" onclick="showDataStats()">检查数据</button>
            <div class="status" id="statsStatus"></div>
            <div id="statsDisplay"></div>
        </div>

        <div class="section">
            <h2>🗑️ 清除数据</h2>
            <div class="info" style="background: #FFF3CD; border-left-color: #FFC107;">
                <strong>警告：</strong>以下操作将永久删除所有本地数据，请谨慎操作！
            </div>
            <button class="btn danger" onclick="clearAllData()">清除所有数据</button>
            <div class="status" id="clearStatus"></div>
        </div>
    </div>

    <script>
        function showStatus(elementId, message, type = 'success') {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }

        function exportData() {
            try {
                const data = {
                    timestamp: new Date().toISOString(),
                    localStorage: {},
                    version: '1.0'
                };

                // 导出所有localStorage数据
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    data.localStorage[key] = localStorage.getItem(key);
                }

                const jsonStr = JSON.stringify(data, null, 2);
                document.getElementById('exportData').value = jsonStr;
                showStatus('exportStatus', `成功导出 ${Object.keys(data.localStorage).length} 项数据`);
            } catch (error) {
                showStatus('exportStatus', `导出失败: ${error.message}`, 'error');
            }
        }

        function exportToFile() {
            exportData();
            const data = document.getElementById('exportData').value;
            if (!data) return;

            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `website-data-backup-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            showStatus('exportStatus', '数据已保存到文件');
        }

        function importData() {
            try {
                const jsonStr = document.getElementById('importData').value.trim();
                if (!jsonStr) {
                    showStatus('importStatus', '请先输入备份数据', 'error');
                    return;
                }

                const data = JSON.parse(jsonStr);
                if (!data.localStorage) {
                    showStatus('importStatus', '备份数据格式不正确', 'error');
                    return;
                }

                let imported = 0;
                Object.entries(data.localStorage).forEach(([key, value]) => {
                    localStorage.setItem(key, value);
                    imported++;
                });

                showStatus('importStatus', `成功恢复 ${imported} 项数据，请刷新页面查看效果`);
                
                // 3秒后自动刷新页面
                setTimeout(() => {
                    if (confirm('数据已恢复，是否刷新页面查看效果？')) {
                        window.location.reload();
                    }
                }, 3000);
            } catch (error) {
                showStatus('importStatus', `恢复失败: ${error.message}`, 'error');
            }
        }

        function loadFromFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            if (!file) {
                showStatus('importStatus', '请先选择一个备份文件', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                document.getElementById('importData').value = e.target.result;
                importData();
            };
            reader.readAsText(file);
        }

        function showDataStats() {
            try {
                const stats = {
                    totalItems: localStorage.length,
                    items: {}
                };

                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    const value = localStorage.getItem(key);
                    stats.items[key] = {
                        size: new Blob([value]).size,
                        preview: value.length > 100 ? value.substring(0, 100) + '...' : value
                    };
                }

                let html = `<strong>总计：${stats.totalItems} 项数据</strong><br><br>`;
                Object.entries(stats.items).forEach(([key, info]) => {
                    html += `<div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">`;
                    html += `<strong>${key}</strong> (${info.size} 字节)<br>`;
                    html += `<code style="font-size: 11px; color: #666;">${info.preview}</code>`;
                    html += `</div>`;
                });

                document.getElementById('statsDisplay').innerHTML = html;
                showStatus('statsStatus', '数据统计已更新');
            } catch (error) {
                showStatus('statsStatus', `检查失败: ${error.message}`, 'error');
            }
        }

        function clearAllData() {
            if (!confirm('确定要清除所有本地数据吗？此操作不可撤销！')) return;
            if (!confirm('再次确认：这将删除所有图片索引和API配置，您确定吗？')) return;

            try {
                localStorage.clear();
                showStatus('clearStatus', '所有数据已清除，请刷新页面');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } catch (error) {
                showStatus('clearStatus', `清除失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示数据统计
        window.addEventListener('load', showDataStats);
    </script>
</body>
</html>