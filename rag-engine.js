class RAGEngine {
    constructor() {
        this.maxContextChunks = 5;  // 最多使用的文档片段数
        this.minRelevanceScore = 0.1; // 最小相关性分数阈值
        this.stopWords = new Set([
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这样', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at',
            'to', 'for', 'of', 'with', 'by', 'as', 'is', 'are', 'was', 'were'
        ]);
    }

    async searchRelevantChunks(query, maxResults = null) {
        if (!query || query.trim().length === 0) {
            return [];
        }

        maxResults = maxResults || this.maxContextChunks;

        try {
            // 获取所有文档片段
            const chunks = await window.dbManager.getAllChunks();
            if (chunks.length === 0) {
                return [];
            }

            // 预处理查询
            const processedQuery = this.preprocessText(query);
            const queryTerms = this.extractTerms(processedQuery);

            if (queryTerms.length === 0) {
                return [];
            }

            // 计算每个片段的相关性分数
            const scoredChunks = [];
            for (const chunk of chunks) {
                const score = this.calculateRelevanceScore(chunk.content, queryTerms, query);
                if (score >= this.minRelevanceScore) {
                    scoredChunks.push({
                        ...chunk,
                        relevanceScore: score
                    });
                }
            }

            // 按分数排序并限制结果数量
            scoredChunks.sort((a, b) => b.relevanceScore - a.relevanceScore);
            const topChunks = scoredChunks.slice(0, maxResults);

            // 获取文档信息并计算chunk索引
            const results = [];
            for (const chunk of topChunks) {
                try {
                    const document = await window.dbManager.getDocument(chunk.docId);

                    // 获取该文档的所有chunks并计算当前chunk的索引
                    const docChunks = await window.dbManager.getChunksByDocId(chunk.docId);
                    const chunkIndex = docChunks.findIndex(c => c.id === chunk.id);

                    results.push({
                        content: chunk.content,
                        filename: document?.filename || '未知文档',
                        docId: chunk.docId,
                        chunkId: chunk.id,
                        chunkIndex: chunkIndex,
                        relevanceScore: chunk.relevanceScore,
                        position: chunk.position || 0
                    });
                } catch (error) {
                    console.warn('Failed to get document info for chunk:', chunk.id, error);
                }
            }

            return results;
        } catch (error) {
            console.error('RAG search failed:', error);
            return [];
        }
    }

    calculateRelevanceScore(content, queryTerms, originalQuery) {
        const processedContent = this.preprocessText(content);
        const contentTerms = this.extractTerms(processedContent);

        let score = 0;

        // 1. 完整短语匹配（权重最高）
        const normalizedQuery = originalQuery.toLowerCase().trim();
        const normalizedContent = processedContent.toLowerCase();
        if (normalizedContent.includes(normalizedQuery)) {
            score += 10;
        }

        // 2. 词汇匹配分数
        const termMatches = this.calculateTermMatches(queryTerms, contentTerms);
        score += termMatches.exactMatches * 3; // 精确匹配
        score += termMatches.partialMatches * 1; // 部分匹配

        // 3. 词频-逆文档频率 (简化版TF-IDF)
        const tfScore = this.calculateTFScore(queryTerms, contentTerms);
        score += tfScore;

        // 4. 位置权重（出现在文档开头的内容权重更高）
        const positionWeight = this.calculatePositionWeight(content);
        score *= positionWeight;

        // 5. 长度惩罚（避免过短的片段得分过高）
        const lengthPenalty = this.calculateLengthPenalty(content);
        score *= lengthPenalty;

        return score;
    }

    calculateTermMatches(queryTerms, contentTerms) {
        const contentTermsLower = contentTerms.map(term => term.toLowerCase());
        const queryTermsLower = queryTerms.map(term => term.toLowerCase());

        let exactMatches = 0;
        let partialMatches = 0;

        for (const queryTerm of queryTermsLower) {
            // 精确匹配
            if (contentTermsLower.includes(queryTerm)) {
                exactMatches++;
                continue;
            }

            // 部分匹配（包含关系）
            const hasPartialMatch = contentTermsLower.some(contentTerm =>
                contentTerm.includes(queryTerm) || queryTerm.includes(contentTerm)
            );

            if (hasPartialMatch) {
                partialMatches++;
            }
        }

        return { exactMatches, partialMatches };
    }

    calculateTFScore(queryTerms, contentTerms) {
        let tfScore = 0;
        const contentLength = contentTerms.length;

        for (const queryTerm of queryTerms) {
            const termFreq = contentTerms.filter(term =>
                term.toLowerCase() === queryTerm.toLowerCase()
            ).length;

            if (termFreq > 0) {
                // 简化的TF计算
                const tf = termFreq / contentLength;
                tfScore += tf * 2;
            }
        }

        return tfScore;
    }

    calculatePositionWeight(content) {
        // 假设文档开头的内容更重要
        if (content.length < 200) {
            return 1.2; // 短内容给予略高权重
        }
        return 1.0;
    }

    calculateLengthPenalty(content) {
        const length = content.length;
        if (length < 50) {
            return 0.5; // 过短内容降权
        }
        if (length > 2000) {
            return 0.8; // 过长内容稍微降权
        }
        return 1.0;
    }

    preprocessText(text) {
        return text
            .toLowerCase()
            .replace(/[^\w\s\u4e00-\u9fff]/g, ' ') // 保留中文、英文字母和数字
            .replace(/\s+/g, ' ')
            .trim();
    }

    extractTerms(text) {
        // 先按空格分割（适用于英文和已有空格的文本）
        const spaceWords = text.split(/\s+/)
            .filter(word => word.length > 1)
            .filter(word => !this.stopWords.has(word));

        // 提取中文关键词（2-4字的词组）
        const chineseTerms = [];
        const cleanText = text.replace(/\s+/g, '');

        // 生成2字词组
        for (let i = 0; i < cleanText.length - 1; i++) {
            const term = cleanText.substring(i, i + 2);
            if (term.match(/[\u4e00-\u9fff]{2}/) && !this.stopWords.has(term)) {
                chineseTerms.push(term);
            }
        }

        // 生成3字词组
        for (let i = 0; i < cleanText.length - 2; i++) {
            const term = cleanText.substring(i, i + 3);
            if (term.match(/[\u4e00-\u9fff]{3}/) && !this.stopWords.has(term)) {
                chineseTerms.push(term);
            }
        }

        // 生成4字词组
        for (let i = 0; i < cleanText.length - 3; i++) {
            const term = cleanText.substring(i, i + 4);
            if (term.match(/[\u4e00-\u9fff]{4}/) && !this.stopWords.has(term)) {
                chineseTerms.push(term);
            }
        }

        // 合并所有词汇并去重
        const allTerms = [...spaceWords, ...chineseTerms];
        return [...new Set(allTerms)];
    }

    async buildContext(query) {
        const relevantChunks = await this.searchRelevantChunks(query);

        if (relevantChunks.length === 0) {
            return null;
        }

        // 构建上下文 - 为每个chunk创建独立的引用以保留定位信息
        const contextParts = relevantChunks.map(chunk => ({
            filename: chunk.filename,
            content: chunk.content,
            docId: chunk.docId,
            chunkIndex: chunk.chunkIndex,
            relevanceScore: chunk.relevanceScore,
            position: chunk.position || 0
        }));

        // 按相关性分数排序
        contextParts.sort((a, b) => b.relevanceScore - a.relevanceScore);

        return contextParts;
    }

    async getDocumentStats() {
        try {
            const documents = await window.dbManager.getAllDocuments();
            const chunks = await window.dbManager.getAllChunks();

            const stats = {
                totalDocuments: documents.length,
                totalChunks: chunks.length,
                averageChunksPerDoc: documents.length > 0 ?
                    Math.round(chunks.length / documents.length) : 0,
                documentTypes: {},
                totalSize: 0
            };

            // 统计文档类型和大小
            for (const doc of documents) {
                const type = doc.type || '未知';
                stats.documentTypes[type] = (stats.documentTypes[type] || 0) + 1;
                stats.totalSize += doc.size || 0;
            }

            return stats;
        } catch (error) {
            console.error('Failed to get document stats:', error);
            return {
                totalDocuments: 0,
                totalChunks: 0,
                averageChunksPerDoc: 0,
                documentTypes: {},
                totalSize: 0
            };
        }
    }

    // 高级搜索功能
    async advancedSearch(query, options = {}) {
        const {
            documentTypes = [], // 限制搜索的文档类型
            maxResults = this.maxContextChunks,
            minScore = this.minRelevanceScore,
            includeMetadata = false
        } = options;

        let chunks = await window.dbManager.getAllChunks();

        // 按文档类型过滤
        if (documentTypes.length > 0) {
            const filteredChunks = [];
            for (const chunk of chunks) {
                try {
                    const doc = await window.dbManager.getDocument(chunk.docId);
                    if (doc && documentTypes.includes(doc.type)) {
                        filteredChunks.push(chunk);
                    }
                } catch (error) {
                    console.warn('Failed to check document type for chunk:', chunk.id);
                }
            }
            chunks = filteredChunks;
        }

        // 执行搜索
        const processedQuery = this.preprocessText(query);
        const queryTerms = this.extractTerms(processedQuery);

        const results = chunks
            .map(chunk => ({
                ...chunk,
                relevanceScore: this.calculateRelevanceScore(chunk.content, queryTerms, query)
            }))
            .filter(chunk => chunk.relevanceScore >= minScore)
            .sort((a, b) => b.relevanceScore - a.relevanceScore)
            .slice(0, maxResults);

        // 添加元数据
        if (includeMetadata) {
            for (const result of results) {
                try {
                    const doc = await window.dbManager.getDocument(result.docId);
                    result.documentMetadata = {
                        filename: doc?.filename,
                        type: doc?.type,
                        uploadTime: doc?.uploadTime,
                        size: doc?.size
                    };
                } catch (error) {
                    result.documentMetadata = null;
                }
            }
        }

        return results;
    }
}

// 全局实例
window.ragEngine = new RAGEngine();