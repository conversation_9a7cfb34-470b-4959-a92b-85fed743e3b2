/**
 * 提示词模板管理器
 * 统一管理所有AI分析和搜索相关的提示词模板
 */
class PromptTemplates {
    constructor() {
        this.templates = this.initializeTemplates();
        this.taxonomyCache = null;
    }

    /**
     * 初始化所有提示词模板
     */
    initializeTemplates() {
        return {
            // 图像分析模板
            imageAnalysis: {
                detailed: this.createDetailedImageAnalysisTemplate(),
                simple: this.createSimpleImageAnalysisTemplate(),
                classification: this.createClassificationTemplate()
            },

            // 语义搜索模板
            semanticSearch: {
                queryAnalysis: this.createQueryAnalysisTemplate(),
                similarity: this.createSimilarityTemplate(),
                matching: this.createMatchingTemplate()
            },

            // 搜索建议模板
            suggestions: {
                autoComplete: this.createAutoCompleteTemplate(),
                related: this.createRelatedSuggestionsTemplate(),
                popular: this.createPopularSuggestionsTemplate()
            },

            // 分类优化模板
            classification: {
                enhancement: this.createClassificationEnhancementTemplate(),
                validation: this.createClassificationValidationTemplate()
            }
        };
    }

    /**
     * 详细图像分析模板（5-10句话要求）
     */
    createDetailedImageAnalysisTemplate() {
        return {
            systemPrompt: `作为专业的UI/UX设计分析师，请深度分析移动应用界面截图。`,

            userPrompt: (taxonomyData = {}) => `深度分析这张移动应用界面截图，返回JSON格式结果。

返回格式：{"description":"详细描述","tags":["标签1","标签2",...]}

描述要求(5-10句话)：
1. 功能描述：界面主要功能和业务场景
2. 布局分析：页面结构、信息层次和内容组织
3. 视觉风格：设计语言、色彩运用和视觉特征
4. 交互元素：按钮、输入框、导航等UI组件识别
5. 用户体验：信息传达清晰度和操作便利性评价

标签要求(10-20个中文标签)：
必须包含基础分类标签(每类至少1个)：
- APP分类: ${this.formatTaxonomyList(taxonomyData.app)}
- 页面类型: ${this.formatTaxonomyList(taxonomyData.page)}
- 控件: ${this.formatTaxonomyList(taxonomyData.control)}

补充标签类型：
- 主色调：蓝色、绿色、红色、黄色、紫色、橙色、粉色、黑色、白色、灰色
- 主题风格：深色主题、浅色主题、现代化、极简风格、商务风格、时尚设计
- 布局特征：卡片式布局、列表布局、网格布局、瀑布流、侧边栏、底部导航
- 功能特征：数据可视化、图表展示、地图导航、音视频播放、文件上传、支付流程

仅返回JSON，无需解释。`,

            postProcess: (response) => {
                return this.validateImageAnalysisResponse(response);
            }
        };
    }

    /**
     * 简化图像分析模板
     */
    createSimpleImageAnalysisTemplate() {
        return {
            systemPrompt: `作为UI分析师，快速分析移动应用界面。`,

            userPrompt: `分析这张移动应用截图，返回JSON：{"description":"详细描述","tags":["标签1","标签2"]}

要求：
- description: 5-10句话概括主要功能和特点
- tags: 5-10个中文标签，包含应用类型、页面类型、主要控件

仅返回JSON。`,

            postProcess: (response) => {
                return this.validateSimpleResponse(response);
            }
        };
    }

    /**
     * 分类模板
     */
    createClassificationTemplate() {
        return {
            systemPrompt: `作为分类专家，将界面元素映射到标准分类体系。`,

            userPrompt: (taxonomyData = {}) => `根据三大分类体系对界面进行分类：

APP分类: ${this.formatTaxonomyList(taxonomyData.app)}
页面类型: ${this.formatTaxonomyList(taxonomyData.page)}
控件: ${this.formatTaxonomyList(taxonomyData.control)}

返回JSON格式：
{
  "appCategory": "最匹配的APP分类",
  "pageType": "最匹配的页面类型",
  "controls": ["识别到的控件1", "控件2"],
  "confidence": 0.85
}

仅返回JSON。`,

            postProcess: (response) => {
                return this.validateClassificationResponse(response);
            }
        };
    }

    /**
     * 查询分析模板
     */
    createQueryAnalysisTemplate() {
        return {
            systemPrompt: `作为搜索意图分析专家，分析用户的图片搜索查询。`,

            userPrompt: (query) => `分析用户搜索查询的意图：

查询: "${query}"

返回JSON格式：
{
  "intent": "用户搜索意图描述",
  "keywords": ["关键词1", "关键词2"],
  "categories": {
    "app": ["相关应用类型"],
    "page": ["相关页面类型"],
    "control": ["相关控件类型"]
  },
  "attributes": {
    "colors": ["颜色相关"],
    "styles": ["风格相关"],
    "functions": ["功能相关"]
  },
  "searchScope": "broad|specific|visual|functional",
  "complexity": "simple|moderate|complex",
  "userNeed": "用户真实需求描述"
}

可能的分类参考：
- 应用类型：金融、健康、教育、体育、社交、购物、工具、娱乐等
- 页面类型：登录、注册、主页、详情、设置、列表、搜索、支付等
- 控件类型：按钮、输入框、卡片、导航栏、列表、图表等

仅返回JSON。`,

            postProcess: (response) => {
                return this.validateQueryAnalysisResponse(response);
            }
        };
    }

    /**
     * 语义相似度模板
     */
    createSimilarityTemplate() {
        return {
            systemPrompt: `作为语义分析专家，计算文本间的语义相似度。`,

            userPrompt: (query, description) => `计算以下两段文本的语义相似度：

查询: "${query}"
描述: "${description}"

返回JSON格式：
{
  "similarity": 0.85,
  "explanation": "相似度原因说明",
  "keyMatches": ["匹配的关键概念"],
  "semanticRelations": ["语义关联说明"]
}

similarity范围0-1，1表示完全相似。
仅返回JSON。`,

            postProcess: (response) => {
                return this.validateSimilarityResponse(response);
            }
        };
    }

    /**
     * 语义匹配模板
     */
    createMatchingTemplate() {
        return {
            systemPrompt: `作为图片搜索分析师，匹配用户查询与图片内容。`,

            userPrompt: (query, imageData) => `分析用户查询与图片的匹配度：

用户查询: "${query}"

图片数据:
${imageData.map((img, index) =>
    `[${index}] 路径: ${img.path}
    描述: ${img.description || '无描述'}
    标签: ${(img.tags || []).join(', ') || '无标签'}`
).join('\n\n')}

返回JSON格式：
{
  "queryAnalysis": {
    "intent": "用户意图",
    "keywords": ["关键词"],
    "searchType": "visual|functional|categorical",
    "complexity": "simple|complex"
  },
  "matches": [
    {
      "imageIndex": 0,
      "relevanceScore": 0.95,
      "matchReason": "匹配原因说明",
      "semanticSimilarity": 0.9,
      "contextualRelevance": 0.8
    }
  ]
}

要求：
1. relevanceScore范围0-1
2. 按相关度降序排列
3. 至少返回3个匹配结果
4. matchReason要具体

仅返回JSON。`,

            postProcess: (response) => {
                return this.validateMatchingResponse(response);
            }
        };
    }

    /**
     * 自动补全模板
     */
    createAutoCompleteTemplate() {
        return {
            systemPrompt: `作为搜索建议专家，生成智能搜索补全。`,

            userPrompt: (partialQuery, context = {}) => `基于用户输入生成搜索补全建议：

当前输入: "${partialQuery}"
搜索历史: ${(context.recentQueries || []).join(', ')}
热门标签: ${(context.popularTags || []).join(', ')}

返回JSON格式：
{
  "suggestions": [
    {
      "text": "补全文本",
      "type": "completion|related|popular",
      "category": "app|page|control|style|color",
      "confidence": 0.9,
      "reason": "推荐理由"
    }
  ]
}

要求：
1. 提供5-8个建议
2. 优先相关度高的
3. confidence表示推荐质量
4. 建议要简洁实用

仅返回JSON。`,

            postProcess: (response) => {
                return this.validateSuggestionsResponse(response);
            }
        };
    }

    /**
     * 相关搜索建议模板
     */
    createRelatedSuggestionsTemplate() {
        return {
            systemPrompt: `作为相关搜索专家，基于当前查询生成相关建议。`,

            userPrompt: (currentQuery, searchResults = []) => `基于当前搜索生成相关建议：

当前查询: "${currentQuery}"
搜索结果标签: ${this.extractTagsFromResults(searchResults)}

生成相关搜索建议，返回JSON：
{
  "relatedQueries": [
    {
      "text": "相关查询",
      "reason": "推荐理由",
      "similarity": 0.8
    }
  ]
}

要求语义相关但有所扩展，帮助用户发现更多内容。
仅返回JSON。`,

            postProcess: (response) => {
                return this.validateRelatedResponse(response);
            }
        };
    }

    /**
     * 热门搜索模板
     */
    createPopularSuggestionsTemplate() {
        return {
            systemPrompt: `作为搜索趋势分析师，生成热门搜索建议。`,

            userPrompt: (context = {}) => `基于当前图片库生成热门搜索建议：

图片标签统计: ${(context.tagFrequency || []).join(', ')}
分类分布: ${(context.categoryDistribution || []).join(', ')}

返回JSON格式：
{
  "popularSearches": [
    {
      "text": "热门搜索",
      "category": "分类",
      "frequency": 0.8,
      "trending": true
    }
  ]
}

生成6-10个实用的热门搜索词。
仅返回JSON。`,

            postProcess: (response) => {
                return this.validatePopularResponse(response);
            }
        };
    }

    /**
     * 分类增强模板
     */
    createClassificationEnhancementTemplate() {
        return {
            systemPrompt: `作为分类优化专家，提升图片分类的准确性。`,

            userPrompt: (imageData, currentClassification) => `优化图片分类结果：

图片信息:
描述: ${imageData.description}
标签: ${(imageData.tags || []).join(', ')}
路径: ${imageData.path}

当前分类:
APP类型: ${currentClassification.app || '未分类'}
页面类型: ${currentClassification.page || '未分类'}
控件: ${(currentClassification.controls || []).join(', ')}

返回优化后的分类JSON：
{
  "optimizedClassification": {
    "app": "优化后的APP分类",
    "page": "优化后的页面类型",
    "controls": ["优化后的控件列表"],
    "confidence": 0.9,
    "improvements": ["改进说明"]
  }
}

仅返回JSON。`,

            postProcess: (response) => {
                return this.validateEnhancementResponse(response);
            }
        };
    }

    /**
     * 分类验证模板
     */
    createClassificationValidationTemplate() {
        return {
            systemPrompt: `作为质量控制专家，验证分类结果的准确性。`,

            userPrompt: (imageData, classification) => `验证分类结果的准确性：

图片数据:
描述: ${imageData.description}
标签: ${(imageData.tags || []).join(', ')}

分类结果:
APP: ${classification.app}
页面: ${classification.page}
控件: ${(classification.controls || []).join(', ')}

返回验证结果JSON：
{
  "validation": {
    "accuracy": 0.85,
    "issues": ["发现的问题"],
    "suggestions": ["改进建议"],
    "confidence": "high|medium|low"
  }
}

仅返回JSON。`,

            postProcess: (response) => {
                return this.validateValidationResponse(response);
            }
        };
    }

    /**
     * 获取模板
     * @param {string} category - 模板分类
     * @param {string} type - 模板类型
     * @returns {Object} 模板对象
     */
    getTemplate(category, type) {
        return this.templates[category]?.[type];
    }

    /**
     * 格式化分类列表
     */
    formatTaxonomyList(list) {
        return Array.isArray(list) ? list.join('、') : '';
    }

    /**
     * 从搜索结果中提取标签
     */
    extractTagsFromResults(results) {
        const allTags = results.flatMap(result => result.tags || []);
        const uniqueTags = [...new Set(allTags)];
        return uniqueTags.slice(0, 10).join(', ');
    }

    /**
     * 验证图像分析响应
     */
    validateImageAnalysisResponse(response) {
        try {
            const parsed = JSON.parse(response);

            // 验证描述长度
            const description = parsed.description || '';
            const sentenceCount = (description.match(/[。！？.!?]/g) || []).length;

            if (sentenceCount < 3) {
                console.warn('描述句数不足，建议增加内容');
            }

            // 验证标签数量
            const tags = parsed.tags || [];
            if (tags.length < 8) {
                console.warn('标签数量不足，建议增加到10-20个');
            }

            return parsed;
        } catch (error) {
            console.error('图像分析响应解析失败:', error);
            return null;
        }
    }

    /**
     * 验证简单响应
     */
    validateSimpleResponse(response) {
        try {
            const parsed = JSON.parse(response);
            return {
                description: parsed.description || '无法分析此图片',
                tags: Array.isArray(parsed.tags) ? parsed.tags : []
            };
        } catch (error) {
            console.error('简单响应解析失败:', error);
            return null;
        }
    }

    /**
     * 验证分类响应
     */
    validateClassificationResponse(response) {
        try {
            const parsed = JSON.parse(response);
            return {
                appCategory: parsed.appCategory || '',
                pageType: parsed.pageType || '',
                controls: Array.isArray(parsed.controls) ? parsed.controls : [],
                confidence: Math.max(0, Math.min(1, parsed.confidence || 0))
            };
        } catch (error) {
            console.error('分类响应解析失败:', error);
            return null;
        }
    }

    /**
     * 验证查询分析响应
     */
    validateQueryAnalysisResponse(response) {
        try {
            const parsed = JSON.parse(response);
            return {
                intent: parsed.intent || '',
                keywords: Array.isArray(parsed.keywords) ? parsed.keywords : [],
                categories: parsed.categories || { app: [], page: [], control: [] },
                attributes: parsed.attributes || { colors: [], styles: [], functions: [] },
                searchScope: parsed.searchScope || 'broad',
                complexity: parsed.complexity || 'simple',
                userNeed: parsed.userNeed || ''
            };
        } catch (error) {
            console.error('查询分析响应解析失败:', error);
            return null;
        }
    }

    /**
     * 验证相似度响应
     */
    validateSimilarityResponse(response) {
        try {
            const parsed = JSON.parse(response);
            return {
                similarity: Math.max(0, Math.min(1, parsed.similarity || 0)),
                explanation: parsed.explanation || '',
                keyMatches: Array.isArray(parsed.keyMatches) ? parsed.keyMatches : [],
                semanticRelations: Array.isArray(parsed.semanticRelations) ? parsed.semanticRelations : []
            };
        } catch (error) {
            console.error('相似度响应解析失败:', error);
            return null;
        }
    }

    /**
     * 验证匹配响应
     */
    validateMatchingResponse(response) {
        try {
            const parsed = JSON.parse(response);
            return {
                queryAnalysis: parsed.queryAnalysis || {},
                matches: Array.isArray(parsed.matches) ? parsed.matches : []
            };
        } catch (error) {
            console.error('匹配响应解析失败:', error);
            return null;
        }
    }

    /**
     * 验证建议响应
     */
    validateSuggestionsResponse(response) {
        try {
            const parsed = JSON.parse(response);
            const suggestions = Array.isArray(parsed.suggestions) ? parsed.suggestions : [];
            return {
                suggestions: suggestions.map(s => ({
                    text: s.text || '',
                    type: s.type || 'suggestion',
                    category: s.category || 'general',
                    confidence: Math.max(0, Math.min(1, s.confidence || 0.5)),
                    reason: s.reason || ''
                }))
            };
        } catch (error) {
            console.error('建议响应解析失败:', error);
            return null;
        }
    }

    /**
     * 验证相关响应
     */
    validateRelatedResponse(response) {
        try {
            const parsed = JSON.parse(response);
            return {
                relatedQueries: Array.isArray(parsed.relatedQueries) ? parsed.relatedQueries : []
            };
        } catch (error) {
            console.error('相关响应解析失败:', error);
            return null;
        }
    }

    /**
     * 验证热门响应
     */
    validatePopularResponse(response) {
        try {
            const parsed = JSON.parse(response);
            return {
                popularSearches: Array.isArray(parsed.popularSearches) ? parsed.popularSearches : []
            };
        } catch (error) {
            console.error('热门响应解析失败:', error);
            return null;
        }
    }

    /**
     * 验证增强响应
     */
    validateEnhancementResponse(response) {
        try {
            const parsed = JSON.parse(response);
            return {
                optimizedClassification: parsed.optimizedClassification || {}
            };
        } catch (error) {
            console.error('增强响应解析失败:', error);
            return null;
        }
    }

    /**
     * 验证验证响应
     */
    validateValidationResponse(response) {
        try {
            const parsed = JSON.parse(response);
            return {
                validation: parsed.validation || {}
            };
        } catch (error) {
            console.error('验证响应解析失败:', error);
            return null;
        }
    }

    /**
     * 加载分类体系数据
     */
    async loadTaxonomyData() {
        if (this.taxonomyCache) {
            return this.taxonomyCache;
        }

        try {
            const response = await fetch('分类/标签分类明细.md');
            if (!response.ok) throw new Error('加载分类数据失败');

            const content = await response.text();
            this.taxonomyCache = this.parseTaxonomy(content);
            return this.taxonomyCache;
        } catch (error) {
            console.warn('加载分类数据失败:', error);
            return { app: [], page: [], control: [] };
        }
    }

    /**
     * 解析分类文档
     */
    parseTaxonomy(content) {
        const parseSection = (title) => {
            const start = content.indexOf(`## ${title}`);
            if (start === -1) return [];
            const rest = content.slice(start);
            const nextIdx = rest.indexOf('\n\n## ');
            const block = nextIdx === -1 ? rest : rest.slice(0, nextIdx);
            return (block.match(/\* .+/g) || []).map(line => line.replace(/\* /, '').trim());
        };

        return {
            app: parseSection('APP分类'),
            page: parseSection('页面类型'),
            control: parseSection('控件')
        };
    }
}

// 导出全局实例
window.PromptTemplates = PromptTemplates;
window.promptTemplates = new PromptTemplates();