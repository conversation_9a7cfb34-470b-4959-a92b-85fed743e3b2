/**
 * 图片元数据管理系统
 * 统一管理所有图片的标签、描述和分类信息
 */
class ImagesMetadata {
    constructor() {
        this.images = new Map();
        this.storageKey = 'images_metadata_v1';
        this.useFileStorage = false; // 默认关闭文件存储，避免自动下载
        this.bulkAdding = false; // 批量添加模式（初始化时不频繁保存）
        this.isReady = false; // 添加就绪状态标志
        this.readyPromise = null; // 添加就绪Promise
        this.initializeAsync(); // 改为异步初始化
    }

    /**
     * 异步初始化方法
     */
    async initializeAsync() {
        try {
            // 先从持久化存储恢复，避免默认数据覆盖用户数据
            await this.loadFromStorage();

            // 批量补充已知图片（仅对缺失路径插入默认数据，避免频繁存储写入）
            this.bulkAdding = true;
            this.initializeKnownImages();
            this.bulkAdding = false;

            // 将合并后的数据保存一次
            await this.saveToStorage();

            this.isReady = true;
            console.log('[ImagesMetadata] 初始化完成，数据已就绪');
        } catch (error) {
            console.error('[ImagesMetadata] 初始化失败:', error);
            this.isReady = true; // 即使失败也设置为就绪，避免无限等待
        }
    }

    /**
     * 等待数据就绪
     */
    async waitForReady() {
        if (this.isReady) return;

        // 如果还没有就绪，等待初始化完成
        while (!this.isReady) {
            await new Promise(resolve => setTimeout(resolve, 50));
        }
    }

    /**
     * 初始化已知图片的元数据
     */
    initializeKnownImages() {
        const knownImages = [
            {
                path: '示例图片/MLS iOS 13.png',
                description: '这是一个体育比赛管理应用的界面，显示了足球赛事的详细信息。界面采用深色主题设计，展示比赛时间、队伍信息和相关操作按钮。',
                tags: ['体育应用', 'MLS', '足球', '赛事管理', '深色主题'],
                classifications: null // 将被自动计算
            },
            {
                path: '示例图片/MLS iOS 53.png',
                description: '这是一个体育应用的详情页面，展示了比赛的具体信息和数据统计。界面设计注重信息的清晰展示和用户体验。',
                tags: ['体育应用', 'MLS', '足球', '详情页', '数据展示'],
                classifications: null
            },
            {
                path: '示例图片/Atoms iOS 74.png',
                description: '这是一个习惯追踪应用的界面，采用简洁的卡片式设计。显示了用户的日常习惯项目，包含进度指示和操作建议，界面风格现代化。',
                tags: ['习惯追踪', '健康应用', '简约设计', '进度管理', '生活方式'],
                classifications: null
            },
            {
                path: '示例图片/Duolingo iOS 29451.png',
                description: '这是语言学习应用Duolingo的界面，展示了学习进度和课程内容。界面设计色彩丰富，具有游戏化学习元素。',
                tags: ['语言学习', '教育应用', '游戏化', '进度追踪', '在线学习'],
                classifications: null
            },
            {
                path: '示例图片/Fuse iOS 115.png',
                description: '这是一个金融钱包应用的订阅管理界面。显示了不同的订阅计划和功能对比，采用现代化的卡片布局设计。',
                tags: ['金融应用', '数字钱包', '订阅管理', '定价页面', '卡片布局'],
                classifications: null
            },
            {
                path: '示例图片/Fuse iOS 15.png',
                description: '这是一个金融应用的主界面，显示账户余额和交易功能。界面包含多个操作按钮和数据可视化元素。',
                tags: ['金融应用', '数字钱包', '账户管理', '交易功能', '数据可视化'],
                classifications: null
            },
            {
                path: '示例图片/Fuse iOS 61.png',
                description: '这是一个支付应用的界面，展示了支付方式选择和账户信息。界面设计简洁清晰，注重用户操作的便利性。',
                tags: ['金融应用', '支付', '支付方式', '账户信息', '用户界面'],
                classifications: null
            },
            {
                path: '示例图片/Fuse iOS 62.png',
                description: '这是一个金融应用的支付确认界面，显示了交易详情和确认按钮。界面采用清晰的布局设计。',
                tags: ['金融应用', '支付确认', '交易详情', '确认页面', '支付流程'],
                classifications: null
            },
            {
                path: '示例图片/Fuse iOS 66.png',
                description: '这是一个金融应用的促销活动页面，展示了特别优惠和活动信息。界面设计吸引眼球，突出促销内容。',
                tags: ['金融应用', '促销活动', '优惠信息', '营销页面', '活动展示'],
                classifications: null
            },
            {
                path: '示例图片/Kit iOS 157.png',
                description: '这是一个移动应用的界面截图，采用卡片式设计布局。界面风格简洁现代，具有良好的信息层次结构和用户体验设计。',
                tags: ['移动应用', 'UI设计', '卡片布局', '用户界面', '现代设计'],
                classifications: null
            },
            {
                path: '示例图片/Kit iOS 77.png',
                description: '这是一个应用的弹窗界面，展示了投资相关的操作选项。弹窗设计简洁明了，提供了清晰的操作指导。',
                tags: ['移动应用', '弹窗设计', '投资功能', '操作界面', '用户交互'],
                classifications: null
            },
            {
                path: '示例图片/Rivian iOS 55.png',
                description: '这是一个汽车相关应用的界面，可能涉及电动车辆管理或汽车服务。界面设计注重功能性和用户体验。',
                tags: ['汽车应用', '电动车', '车辆管理', '移动服务', '智能出行'],
                classifications: null
            },
            {
                path: '示例图片/Ultrahuman iOS 26.png',
                description: '这是一个健康监测应用的日历界面，显示了用户的健康数据记录。界面采用现代化设计，具有清晰的数据展示。',
                tags: ['健康监测', '日历界面', '健康数据', '数据记录', '用户界面'],
                classifications: null
            },
            {
                path: '示例图片/Ultrahuman iOS 27.png',
                description: '这是一个健康应用的日历详情页面，展示了特定日期的健康指标和活动记录。界面设计专业且易于理解。',
                tags: ['健康监测', '日历详情', '健康指标', '活动记录', '数据可视化'],
                classifications: null
            },
            {
                path: '示例图片/Ultrahuman iOS 32.png',
                description: '这是一个健康监测应用的运营界面，显示了用户的生物指标和健康数据。界面采用深色主题，具有专业的数据可视化元素。',
                tags: ['健康监测', '生物指标', '健康数据', '深色主题', '数据可视化'],
                classifications: null
            },
            {
                path: '示例图片/Ultrahuman iOS 59.png',
                description: '这是一个健康应用的数据分析界面，展示了详细的健康指标和趋势分析。界面设计专业，数据展示清晰。',
                tags: ['健康监测', '数据分析', '健康指标', '趋势分析', '专业界面'],
                classifications: null
            }
        ];

        // 初始化图片数据并计算分类（若本地已有则跳过，避免覆盖用户数据）
        knownImages.forEach(imageData => {
            if (!this.images.has(imageData.path)) {
                this.addImage(imageData);
            }
        });
    }

    /**
     * 从存储恢复数据（localStorage 或文件存储）
     */
    async loadFromStorage() {
        try {
            if (this.useFileStorage && window.fileStorageManager) {
                try {
                    await this.loadFromFileStorage();
                } catch (e) {
                    console.warn('从文件存储加载失败，尝试localStorage:', e);
                    this.loadFromLocalStorage();
                }
            } else {
                this.loadFromLocalStorage();
            }
        } catch (error) {
            console.error('数据加载完全失败，使用默认数据:', error);
            // 确保至少有基础数据可用
            this.initializeKnownImages();
        }
    }

    /**
     * 从 localStorage 恢复数据
     */
    loadFromLocalStorage() {
        try {
            const raw = localStorage.getItem(this.storageKey);
            if (!raw) return;
            const data = JSON.parse(raw);
            const images = data?.images || data || {};
            Object.entries(images).forEach(([path, meta]) => {
                this.images.set(path, meta);
            });
            console.log(`[ImagesMetadata] 已从 localStorage 恢复 ${this.images.size} 条记录`);
        } catch (e) {
            console.warn('恢复图片元数据失败:', e);
        }
    }

    /**
     * 从文件存储加载数据
     */
    async loadFromFileStorage() {
        if (!window.fileStorageManager) {
            throw new Error('FileStorageManager 未初始化');
        }
        
        try {
            // 这里暂时不自动加载文件，需要用户手动选择
            // const imagesMap = await window.fileStorageManager.loadFromJson();
            // this.images = imagesMap;
            console.log(`[ImagesMetadata] 文件存储模式已启用，请使用手动导入功能`);
        } catch (e) {
            throw e;
        }
    }

    /**
     * 保存数据到存储（仅使用 localStorage，避免自动文件下载）
     */
    async saveToStorage() {
        // 默认只保存到 localStorage，避免自动触发文件下载
        this.saveToLocalStorage();

        // 同步到Excel文件（如果已连接）
        await this.syncToExcel();
    }

    /**
     * 保存数据到 localStorage
     */
    saveToLocalStorage() {
        try {
            const obj = {};
            this.images.forEach((meta, path) => {
                obj[path] = meta;
            });
            const dataToSave = { images: obj };
            localStorage.setItem(this.storageKey, JSON.stringify(dataToSave));
            console.log(`[ImagesMetadata] 已保存到 localStorage，包含 ${this.images.size} 条记录`);

            // 验证保存是否成功
            const saved = localStorage.getItem(this.storageKey);
            if (saved) {
                const parsed = JSON.parse(saved);
                console.log(`[ImagesMetadata] 验证保存成功，localStorage 中有 ${Object.keys(parsed.images || {}).length} 条记录`);
            }
        } catch (e) {
            console.warn('保存图片元数据失败:', e);
        }
    }

    /**
     * 保存数据到文件存储
     */
    async saveToFileStorage() {
        if (!window.fileStorageManager) {
            throw new Error('FileStorageManager 未初始化');
        }
        
        try {
            await window.fileStorageManager.saveToJson(this.images);
            // 同时导出Csv供编辑
            await window.fileStorageManager.exportToCsv(this.images);
            console.log(`[ImagesMetadata] 已保存到文件存储，包含 ${this.images.size} 条记录`);
        } catch (e) {
            throw e;
        }
    }

    /**
     * 添加图片元数据
     * @param {Object} imageData - 图片数据
     */
    addImage(imageData) {
        const { path, description = '', tags = [], preview = undefined } = imageData;

        // 使用分类引擎计算分类
        let classifications = imageData.classifications;
        if (!classifications) {
            if (window.imageClassifier) {
                // 合并基于内容的分类和基于文件名的分类
                const contentClassification = window.imageClassifier.classifyImage({ tags, description });
                const filenameClassification = window.imageClassifier.classifyByFilename(path);
                classifications = window.imageClassifier.mergeClassifications(
                    contentClassification,
                    filenameClassification
                );
                console.log(`为图片 ${path} 生成分类:`, classifications);
            } else {
                console.warn(`ImageClassifier未加载，无法为图片 ${path} 生成分类`);
            }
        }

        const metadata = {
            path,
            description,
            tags,
            classifications,
            lastUpdated: new Date().toISOString(),
            indexed: true,
            ...(preview ? { preview } : {})
        };

        this.images.set(path, metadata);
        //       
        if (!this.bulkAdding) {
            this.saveToStorage();
        }
    }

    /**
     * 重新计算所有图片的分类（在ImageClassifier加载后调用）
     */
    recalculateClassifications() {
        if (!window.imageClassifier) {
            console.warn('ImageClassifier未加载，延迟重新计算分类');
            // 设置延迟重试
            setTimeout(() => this.recalculateClassifications(), 1000);
            return;
        }

        console.log('重新计算所有图片的分类...');
        const images = Array.from(this.images.values());

        images.forEach(imageData => {
            if (!imageData.classifications) {
                // 重新计算分类
                const contentClassification = window.imageClassifier.classifyImage({
                    tags: imageData.tags,
                    description: imageData.description
                });
                const filenameClassification = window.imageClassifier.classifyByFilename(imageData.path);
                const classifications = window.imageClassifier.mergeClassifications(
                    contentClassification,
                    filenameClassification
                );

                // 更新元数据
                imageData.classifications = classifications;
                imageData.lastUpdated = new Date().toISOString();
                this.images.set(imageData.path, imageData);
            
                console.log(`重新计算图片 ${imageData.path} 的分类:`, classifications);
            }
        });

        console.log(`完成重新计算，共处理 ${images.length} 张图片`);
        this.saveToStorage();
    }

    /**
     * 获取图片元数据
     * @param {string} path - 图片路径
     * @returns {Object|null} 图片元数据
     */
    getImage(path) {
        return this.images.get(path) || null;
    }

    /**
     * 获取所有图片
     * @returns {Array} 所有图片的元数据数组
     */
    getAllImages() {
        return Array.from(this.images.values());
    }

    /**
     * 根据分类获取图片
     * @param {string} type - 分类类型: 'app', 'page', 'control'
     * @param {string} category - 具体分类名称
     * @returns {Array} 匹配的图片列表
     */
    getImagesByCategory(type, category) {
        const typeMap = {
            'app': 'appCategories',
            'page': 'pageTypes',
            'control': 'controlTypes'
        };

        const classificationKey = typeMap[type];
        if (!classificationKey) return [];

        return this.getAllImages().filter(image => {
            if (!image.classifications || !image.classifications[classificationKey]) {
                return false;
            }

            return image.classifications[classificationKey].some(
                item => item.category === category
            );
        });
    }

    /**
     * 获取特定类型的所有分类及其图片数量
     * @param {string} type - 分类类型: 'app', 'page', 'control'
     * @returns {Array} 分类统计信息
     */
    getCategoryStats(type) {
        const images = this.getAllImages();
        const categoryCount = new Map();

        const typeMap = {
            'app': 'appCategories',
            'page': 'pageTypes',
            'control': 'controlTypes'
        };

        const classificationKey = typeMap[type];
        if (!classificationKey) return [];

        images.forEach(image => {
            if (image.classifications && image.classifications[classificationKey]) {
                image.classifications[classificationKey].forEach(item => {
                    const category = item.category;
                    const currentCount = categoryCount.get(category) || 0;
                    categoryCount.set(category, currentCount + 1);
                });
            }
        });

        // 转换为数组并排序
        return Array.from(categoryCount.entries())
            .map(([category, count]) => ({
                category,
                count,
                type
            }))
            .sort((a, b) => b.count - a.count);
    }

    /**
     * 搜索图片
     * @param {string} query - 搜索查询
     * @returns {Array} 匹配的图片列表
     */
    searchImages(query) {
        if (!query) return this.getAllImages();

        const lowerQuery = query.toLowerCase();
        return this.getAllImages().filter(image => {
            // 搜索描述
            if (image.description.toLowerCase().includes(lowerQuery)) {
                return true;
            }

            // 搜索标签
            if (image.tags.some(tag => tag.toLowerCase().includes(lowerQuery))) {
                return true;
            }

            // 搜索分类
            if (image.classifications) {
                const allCategories = [
                    ...image.classifications.appCategories,
                    ...image.classifications.pageTypes,
                    ...image.classifications.controlTypes
                ];
                if (allCategories.some(item =>
                    item.category.toLowerCase().includes(lowerQuery)
                )) {
                    return true;
                }
            }

            return false;
        });
    }

    /**
     * 更新图片元数据
     * @param {string} path - 图片路径
     * @param {Object} updates - 更新数据
     */
    updateImage(path, updates) {
        const existing = this.images.get(path);
        if (!existing) return false;

        const updated = {
            ...existing,
            ...updates,
            lastUpdated: new Date().toISOString()
        };

        // 如果标签或描述被更新，重新计算分类
        if (updates.tags || updates.description) {
            if (window.imageClassifier) {
                const contentClassification = window.imageClassifier.classifyImage({
                    tags: updated.tags,
                    description: updated.description
                });
                const filenameClassification = window.imageClassifier.classifyByFilename(path);
                updated.classifications = window.imageClassifier.mergeClassifications(
                    contentClassification,
                    filenameClassification
                );
            }
        }

        this.images.set(path, updated);
        this.saveToStorage();
        return true;
    }

    /**
     * 删除图片元数据
     * @param {string} path - 图片路径
     */
    removeImage(path) {
        const res = this.images.delete(path);
        this.saveToStorage();
        return res;
    }

    /**
     * 获取未索引的图片列表
     * @param {Array} allImagePaths - 所有图片路径
     * @returns {Array} 未索引的图片路径
     */
    getUnindexedImages(allImagePaths) {
        return allImagePaths.filter(path => !this.images.has(path));
    }

    /**
     * 导出元数据（用于备份或调试）
     * @returns {Object} 所有元数据
     */
    exportMetadata() {
        const data = {};
        this.images.forEach((metadata, path) => {
            data[path] = metadata;
        });
        return {
            version: '1.0',
            timestamp: new Date().toISOString(),
            images: data
        };
    }

    /**
     * 导入元数据
     * @param {Object} data - 导入的数据
     */
    async importMetadata(data) {
        if (data.images) {
            Object.entries(data.images).forEach(([path, metadata]) => {
                this.images.set(path, metadata);
            });
            await this.saveToStorage();
        }
    }

    /**
     * 从Csv文件导入数据
     */
    async importFromCsv() {
        if (!window.fileStorageManager) {
            console.error('FileStorageManager 未初始化');
            return false;
        }
        
        try {
            const imagesMap = await window.fileStorageManager.importFromCsv();
            if (imagesMap.size > 0) {
                this.images = imagesMap;
                await this.saveToStorage();
                console.log(`[ImagesMetadata] 从 CSV 导入了 ${this.images.size} 条记录`);
                return true;
            }
        } catch (e) {
            console.error('从 CSV 导入失败:', e);
        }
        return false;
    }

    /**
     * 从Json文件导入数据
     */
    async importFromJson() {
        if (!window.fileStorageManager) {
            console.error('FileStorageManager 未初始化');
            return false;
        }
        
        try {
            const imagesMap = await window.fileStorageManager.loadFromJson();
            if (imagesMap.size > 0) {
                this.images = imagesMap;
                await this.saveToStorage();
                console.log(`[ImagesMetadata] 从 JSON 导入了 ${this.images.size} 条记录`);
                return true;
            }
        } catch (e) {
            console.error('从 JSON 导入失败:', e);
        }
        return false;
    }

    /**
     * 导出到CSV文件
     */
    async exportToCsv() {
        if (!window.fileStorageManager) {
            console.error('FileStorageManager 未初始化');
            return false;
        }
        
        try {
            await window.fileStorageManager.exportToCsv(this.images);
            console.log(`[ImagesMetadata] 已导出 CSV 文件，包含 ${this.images.size} 条记录`);
            return true;
        } catch (e) {
            console.error('导出 CSV 失败:', e);
        }
        return false;
    }

    /**
     * 导出到JSON文件
     */
    async exportToJson() {
        if (!window.fileStorageManager) {
            console.error('FileStorageManager 未初始化');
            return false;
        }
        
        try {
            await window.fileStorageManager.saveToJson(this.images);
            console.log(`[ImagesMetadata] 已导出 JSON 文件，包含 ${this.images.size} 条记录`);
            return true;
        } catch (e) {
            console.error('导出 JSON 失败:', e);
        }
        return false;
    }

    /**
     * 同步数据到Excel文件
     */
    async syncToExcel() {
        // 检查是否启用Excel同步
        if (!window.excelSyncManager) {
            return; // Excel同步管理器未初始化
        }
        
        const status = window.excelSyncManager.getSyncStatus();
        if (!status.hasFile || !status.canWrite || status.isMonitoring) {
            return; // 无Excel文件或正在监控中（避免循环同步）
        }
        
        try {
            console.log('[ImagesMetadata] 同步数据到Excel文件...');
            await window.excelSyncManager.writeExcelData(this.images);
            console.log('[ImagesMetadata] Excel文件同步完成');
        } catch (error) {
            console.warn('[ImagesMetadata] Excel同步失败:', error);
        }
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const images = this.getAllImages();
        const totalImages = images.length;
        const indexedImages = images.filter(img => img.indexed).length;

        const appCategoryStats = this.getCategoryStats('app');
        const pageCategoryStats = this.getCategoryStats('page');
        const controlCategoryStats = this.getCategoryStats('control');

        return {
            totalImages,
            indexedImages,
            unindexedImages: totalImages - indexedImages,
            categoryCounts: {
                app: appCategoryStats.length,
                page: pageCategoryStats.length,
                control: controlCategoryStats.length
            },
            topCategories: {
                app: appCategoryStats.slice(0, 5),
                page: pageCategoryStats.slice(0, 5),
                control: controlCategoryStats.slice(0, 5)
            }
        };
    }
}

// 导出全局实例
window.ImagesMetadata = ImagesMetadata;
window.imagesMetadata = new ImagesMetadata();
