class FileProcessor {
    constructor() {
        this.supportedTypes = {
            'text/plain': this.processTextFile.bind(this),
            'text/markdown': this.processTextFile.bind(this),
            'application/pdf': this.processPDFFile.bind(this),
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': this.processWordFile.bind(this)
        };

        this.maxChunkSize = 1000; // 每个片段最大字符数
        this.chunkOverlap = 100;  // 片段重叠字符数
    }

    isSupported(file) {
        return this.supportedTypes.hasOwnProperty(file.type) ||
               this.getFileTypeByExtension(file.name) !== null;
    }

    getFileTypeByExtension(filename) {
        const ext = filename.toLowerCase().split('.').pop();
        const extensionMap = {
            'txt': 'text/plain',
            'md': 'text/markdown',
            'pdf': 'application/pdf',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        };
        return extensionMap[ext] || null;
    }

    async processFile(file, onProgress = null) {
        if (!this.isSupported(file)) {
            throw new Error(`不支持的文件类型: ${file.type || '未知'}`);
        }

        const fileType = file.type || this.getFileTypeByExtension(file.name);
        const processor = this.supportedTypes[fileType];

        if (!processor) {
            throw new Error(`无法处理文件类型: ${fileType}`);
        }

        try {
            onProgress?.(10, '开始处理文件...');

            const content = await processor(file, onProgress);

            onProgress?.(70, '分割文本片段...');
            const chunks = this.splitIntoChunks(content);

            onProgress?.(90, '保存到数据库...');

            // 对于PDF文件，保存原始文件数据用于渲染
            let fileData = null;
            if (file.type === 'application/pdf' || fileType === 'application/pdf') {
                fileData = await this.fileToArrayBuffer(file);
            }

            const docId = await window.dbManager.saveDocument(file, content, chunks, fileData);

            onProgress?.(100, '处理完成');

            return {
                id: docId,
                filename: file.name,
                type: fileType,
                size: file.size,
                content: content,
                chunksCount: chunks.length
            };
        } catch (error) {
            throw new Error(`文件处理失败: ${error.message}`);
        }
    }

    async processTextFile(file, onProgress = null) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                onProgress?.(50, '读取文件内容...');
                resolve(e.target.result);
            };

            reader.onerror = () => {
                reject(new Error('文件读取失败'));
            };

            reader.readAsText(file, 'UTF-8');
        });
    }

    async processPDFFile(file, onProgress = null) {
        if (typeof pdfjsLib === 'undefined') {
            throw new Error('PDF.js 未加载，无法处理PDF文件');
        }

        const arrayBuffer = await this.fileToArrayBuffer(file);
        onProgress?.(30, '解析PDF文件...');

        try {
            const loadingTask = pdfjsLib.getDocument(arrayBuffer);
            const pdf = await loadingTask.promise;

            let fullText = '';
            const totalPages = pdf.numPages;
            this.pdfPageData = new Map(); // 存储页面信息用于后续chunk分割

            for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
                onProgress?.(30 + (pageNum / totalPages) * 40, `处理第 ${pageNum}/${totalPages} 页...`);

                const page = await pdf.getPage(pageNum);
                const textContent = await page.getTextContent();

                // 获取页面文本和位置信息
                const pageTextItems = textContent.items.map(item => ({
                    text: item.str,
                    x: item.transform[4],
                    y: item.transform[5],
                    width: item.width || 0,
                    height: item.height || 0
                }));

                const pageText = pageTextItems
                    .map(item => item.text)
                    .join(' ')
                    .trim();

                if (pageText) {
                    // 记录页面开始位置
                    const pageStartPos = fullText.length;
                    fullText += `\n\n=== 第 ${pageNum} 页 ===\n${pageText}`;

                    // 存储页面信息
                    this.pdfPageData.set(pageNum, {
                        text: pageText,
                        textItems: pageTextItems,
                        startPosition: pageStartPos,
                        endPosition: fullText.length
                    });
                }
            }

            return fullText.trim();
        } catch (error) {
            throw new Error(`PDF处理失败: ${error.message}`);
        }
    }

    async processWordFile(file, onProgress = null) {
        // 简化的DOCX处理 - 实际项目中可以使用mammoth.js等库
        onProgress?.(50, '处理Word文档...');

        try {
            // 这里只是一个占位符实现
            // 实际应该使用专门的DOCX解析库
            const text = await this.processTextFile(file, onProgress);
            return text;
        } catch (error) {
            throw new Error(`Word文档处理失败: ${error.message}`);
        }
    }

    fileToArrayBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsArrayBuffer(file);
        });
    }

    splitIntoChunks(content) {
        if (!content || content.length === 0) {
            return [];
        }

        const chunks = [];
        const sentences = this.splitIntoSentences(content);

        let currentChunk = '';
        let position = 0;

        for (const sentence of sentences) {
            // 如果单个句子就超过最大长度，直接作为一个片段
            if (sentence.length > this.maxChunkSize) {
                if (currentChunk) {
                    const chunkInfo = this.createChunk(currentChunk.trim(), position);
                    chunks.push(chunkInfo);
                    currentChunk = '';
                }

                const chunkInfo = this.createChunk(sentence.trim(), position);
                chunks.push(chunkInfo);

                position += sentence.length;
                continue;
            }

            // 检查添加这个句子是否会超过最大长度
            if (currentChunk.length + sentence.length > this.maxChunkSize) {
                const chunkInfo = this.createChunk(currentChunk.trim(), position);
                chunks.push(chunkInfo);

                // 保留重叠部分
                const overlap = this.getOverlap(currentChunk, this.chunkOverlap);
                currentChunk = overlap + sentence;
            } else {
                currentChunk += sentence;
            }

            position += sentence.length;
        }

        // 添加最后一个片段
        if (currentChunk.trim()) {
            const chunkInfo = this.createChunk(currentChunk.trim(), position);
            chunks.push(chunkInfo);
        }

        return chunks;
    }

    // 创建带有页码信息的chunk
    createChunk(content, position) {
        const chunk = {
            content: content,
            position: position
        };

        // 如果有PDF页面数据，添加页码和位置信息
        if (this.pdfPageData && this.pdfPageData.size > 0) {
            const pageInfo = this.findPageForPosition(position);
            if (pageInfo) {
                chunk.pageNumber = pageInfo.pageNumber;
                chunk.page = pageInfo.pageNumber; // 保持兼容性
                chunk.textPosition = pageInfo.textPosition;
            }
        }

        return chunk;
    }

    // 根据文本位置找到对应的PDF页码
    findPageForPosition(position) {
        for (const [pageNum, pageData] of this.pdfPageData.entries()) {
            if (position >= pageData.startPosition && position <= pageData.endPosition) {
                return {
                    pageNumber: pageNum,
                    textPosition: {
                        // 简化的位置信息，实际中可以更精确
                        page: pageNum,
                        y: 0 // 可以根据文本在页面中的相对位置计算
                    }
                };
            }
        }
        return null;
    }

    splitIntoSentences(text) {
        // 简化的句子分割，实际项目中可以使用更复杂的NLP库
        return text
            .split(/[。！？\n\r]+/)
            .map(s => s.trim())
            .filter(s => s.length > 0)
            .map(s => s + '。');
    }

    getOverlap(text, overlapSize) {
        if (text.length <= overlapSize) {
            return text;
        }

        // 尝试在单词边界处截断
        const overlap = text.slice(-overlapSize);
        const spaceIndex = overlap.indexOf(' ');

        if (spaceIndex > 0) {
            return overlap.slice(spaceIndex);
        }

        return overlap;
    }

    // 批量处理文件
    async processMultipleFiles(files, onProgress = null, onFileComplete = null) {
        const results = [];
        const errors = [];

        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            try {
                onProgress?.(
                    (i / files.length) * 100,
                    `处理文件 ${i + 1}/${files.length}: ${file.name}`
                );

                const result = await this.processFile(file, (progress, message) => {
                    const totalProgress = ((i / files.length) + (progress / 100) / files.length) * 100;
                    onProgress?.(totalProgress, `${file.name}: ${message}`);
                });

                results.push(result);
                onFileComplete?.(result, null);

            } catch (error) {
                const errorInfo = {
                    filename: file.name,
                    error: error.message
                };
                errors.push(errorInfo);
                onFileComplete?.(null, errorInfo);
            }
        }

        return { results, errors };
    }

    // 获取文件预览
    async getFilePreview(content, maxLength = 500) {
        if (!content) return '';

        const preview = content.substring(0, maxLength);
        return content.length > maxLength ? preview + '...' : preview;
    }

    // 验证文件
    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const errors = [];

        if (!file) {
            errors.push('文件不能为空');
            return errors;
        }

        if (file.size > maxSize) {
            errors.push('文件大小不能超过10MB');
        }

        if (!this.isSupported(file)) {
            errors.push('不支持的文件类型');
        }

        return errors;
    }
}

// 全局实例
window.fileProcessor = new FileProcessor();