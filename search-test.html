<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能搜索测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f7;
        }
        .test-container {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #1d1d1f;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e5e7;
            border-radius: 12px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        .search-examples {
            display: grid;
            gap: 10px;
        }
        .search-example {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .search-example:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }
        .search-text {
            flex: 1;
            font-size: 14px;
            color: #333;
        }
        .search-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
        }
        .search-btn:hover {
            background: #0056CC;
        }
        .test-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e5e7;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .test-input:focus {
            outline: none;
            border-color: #007AFF;
        }
        .custom-search-btn {
            background: #1d1d1f;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            width: 100%;
        }
        .custom-search-btn:hover {
            background: #2d2d2f;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 智能图片搜索测试</h1>
        
        <div class="test-section">
            <div class="test-title">📱 应用类型搜索</div>
            <div class="search-examples">
                <div class="search-example" onclick="testSearch('金融应用')">
                    <span class="search-text">金融应用</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('健康应用')">
                    <span class="search-text">健康应用</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('教育应用')">
                    <span class="search-text">教育应用</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('体育应用')">
                    <span class="search-text">体育应用</span>
                    <button class="search-btn">搜索</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🎨 界面特征搜索</div>
            <div class="search-examples">
                <div class="search-example" onclick="testSearch('深色主题')">
                    <span class="search-text">深色主题</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('卡片布局')">
                    <span class="search-text">卡片布局</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('数据可视化')">
                    <span class="search-text">数据可视化</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('简约设计')">
                    <span class="search-text">简约设计</span>
                    <button class="search-btn">搜索</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 功能页面搜索</div>
            <div class="search-examples">
                <div class="search-example" onclick="testSearch('登录页面')">
                    <span class="search-text">登录页面</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('设置界面')">
                    <span class="search-text">设置界面</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('支付页面')">
                    <span class="search-text">支付页面</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('详情页面')">
                    <span class="search-text">详情页面</span>
                    <button class="search-btn">搜索</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 复合搜索</div>
            <div class="search-examples">
                <div class="search-example" onclick="testSearch('金融应用的登录页面')">
                    <span class="search-text">金融应用的登录页面</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('健康应用的深色主题界面')">
                    <span class="search-text">健康应用的深色主题界面</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('带有按钮的设置页面')">
                    <span class="search-text">带有按钮的设置页面</span>
                    <button class="search-btn">搜索</button>
                </div>
                <div class="search-example" onclick="testSearch('体育应用的数据展示界面')">
                    <span class="search-text">体育应用的数据展示界面</span>
                    <button class="search-btn">搜索</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✏️ 自定义搜索</div>
            <input type="text" class="test-input" id="customSearch" placeholder="输入您的搜索内容...">
            <button class="custom-search-btn" onclick="testCustomSearch()">搜索</button>
        </div>

        <div class="test-section">
            <div class="test-title">🏠 返回主页</div>
            <button class="custom-search-btn" onclick="goToHome()" style="background: #28a745;">
                返回主页测试搜索功能
            </button>
        </div>
    </div>

    <script>
        function testSearch(query) {
            const url = `gallery.html?search=${encodeURIComponent(query)}`;
            window.open(url, '_blank');
        }

        function testCustomSearch() {
            const input = document.getElementById('customSearch');
            const query = input.value.trim();
            if (query) {
                testSearch(query);
            } else {
                alert('请输入搜索内容');
            }
        }

        function goToHome() {
            window.open('index.html', '_blank');
        }

        // 回车键搜索
        document.getElementById('customSearch').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                testCustomSearch();
            }
        });
    </script>
</body>
</html>
