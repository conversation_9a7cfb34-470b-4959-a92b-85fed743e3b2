/**
 * 图片错误处理模块
 * 处理图片加载失败、文件缺失等问题
 */
class ImageErrorHandler {
    constructor() {
        this.missingImages = new Set();
        this.fallbackImages = new Map();
        this.retryAttempts = new Map();
        this.maxRetries = 3;

        this.initializeFallbacks();
    }

    /**
     * 初始化备用图片
     */
    initializeFallbacks() {
        // 创建简单的占位符图片
        this.createPlaceholderImage('app', '#007bff', '📱');
        this.createPlaceholderImage('page', '#28a745', '📄');
        this.createPlaceholderImage('control', '#ffc107', '🎛️');
        this.createPlaceholderImage('default', '#6c757d', '🖼️');
    }

    /**
     * 创建占位符图片
     */
    createPlaceholderImage(type, color, emoji) {
        const canvas = document.createElement('canvas');
        canvas.width = 200;
        canvas.height = 300;
        const ctx = canvas.getContext('2d');

        // 绘制背景
        ctx.fillStyle = color;
        ctx.fillRect(0, 0, 200, 300);

        // 绘制emoji
        ctx.font = '48px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = 'white';
        ctx.fillText(emoji, 100, 150);

        // 添加文字
        ctx.font = '14px Arial';
        ctx.fillText('图片加载失败', 100, 200);

        this.fallbackImages.set(type, canvas.toDataURL());
    }

    /**
     * 处理图片加载错误
     */
    handleImageError(imgElement, imagePath, category = 'default') {
        const retryCount = this.retryAttempts.get(imagePath) || 0;

        if (retryCount < this.maxRetries) {
            // 尝试重新加载
            this.retryAttempts.set(imagePath, retryCount + 1);
            setTimeout(() => {
                imgElement.src = imagePath + '?retry=' + (retryCount + 1);
            }, 1000 * (retryCount + 1));
        } else {
            // 使用占位符
            this.missingImages.add(imagePath);
            const fallbackType = this.determineFallbackType(category);
            imgElement.src = this.fallbackImages.get(fallbackType);
            imgElement.classList.add('fallback-image');

            console.warn(`图片加载失败，使用占位符: ${imagePath}`);
        }
    }

    /**
     * 确定占位符类型
     */
    determineFallbackType(category) {
        if (category.includes('应用') || category.includes('app')) return 'app';
        if (category.includes('页面') || category.includes('界面')) return 'page';
        if (category.includes('控件') || category.includes('按钮')) return 'control';
        return 'default';
    }

    /**
     * 批量检查图片是否存在
     */
    async checkImagesExist(imagePaths) {
        const results = new Map();

        for (const path of imagePaths) {
            try {
                const response = await fetch(path, { method: 'HEAD' });
                results.set(path, response.ok);
            } catch (error) {
                results.set(path, false);
            }
        }

        return results;
    }

    /**
     * 获取缺失图片统计
     */
    getMissingImageStats() {
        return {
            total: this.missingImages.size,
            list: Array.from(this.missingImages)
        };
    }

    /**
     * 为图片元素添加错误处理
     */
    attachErrorHandler(imgElement, imagePath, category) {
        imgElement.onerror = () => {
            this.handleImageError(imgElement, imagePath, category);
        };

        // 添加加载超时处理
        const timeout = setTimeout(() => {
            if (!imgElement.complete) {
                this.handleImageError(imgElement, imagePath, category);
            }
        }, 10000); // 10秒超时

        imgElement.onload = () => {
            clearTimeout(timeout);
        };
    }

    /**
     * 验证图片目录
     */
    async validateImageDirectory() {
        try {
            const manifestResponse = await fetch('示例图片/manifest.json');
            if (!manifestResponse.ok) {
                throw new Error('Manifest文件不存在');
            }

            const manifest = await manifestResponse.json();
            const imagePaths = manifest.files.map(file => `示例图片/${file}`);

            const existenceResults = await this.checkImagesExist(imagePaths);
            const missingCount = Array.from(existenceResults.values()).filter(exists => !exists).length;

            return {
                total: imagePaths.length,
                missing: missingCount,
                exists: imagePaths.length - missingCount,
                details: existenceResults
            };
        } catch (error) {
            console.error('验证图片目录失败:', error);
            return {
                total: 0,
                missing: 0,
                exists: 0,
                error: error.message
            };
        }
    }
}

// 导出全局实例
window.ImageErrorHandler = ImageErrorHandler;
window.imageErrorHandler = new ImageErrorHandler();

// 自动验证图片目录
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', async () => {
        const validation = await window.imageErrorHandler.validateImageDirectory();
        if (validation.missing > 0) {
            console.warn(`发现 ${validation.missing} 个图片文件缺失`);
        }
    });
} else {
    window.imageErrorHandler.validateImageDirectory().then(validation => {
        if (validation.missing > 0) {
            console.warn(`发现 ${validation.missing} 个图片文件缺失`);
        }
    });
}