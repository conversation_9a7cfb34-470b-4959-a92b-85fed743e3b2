#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';

/**
 * 图片清单生成工具
 * 扫描指定目录中的图片文件，生成manifest.json清单
 */

const IMAGE_DIR = '示例图片';
const SUPPORTED_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'];
const FILENAME_PATTERN = /^Saved Screens (\d+)\.png$/i;

async function scanImageDirectory() {
    const imageDir = path.resolve(process.cwd(), IMAGE_DIR);
    
    try {
        await fs.access(imageDir);
    } catch (error) {
        console.error(`❌ 图片目录 "${IMAGE_DIR}" 不存在`);
        process.exit(1);
    }

    console.log(`📂 扫描图片目录: ${imageDir}`);
    
    const entries = await fs.readdir(imageDir, { withFileTypes: true });
    const imageFiles = [];
    const savedScreensFiles = [];
    
    for (const entry of entries) {
        if (!entry.isFile()) continue;
        
        const fileName = entry.name;
        const ext = path.extname(fileName).toLowerCase();
        
        // 检查是否为支持的图片格式
        if (!SUPPORTED_EXTENSIONS.includes(ext)) continue;
        
        // 检查文件是否存在并可读
        const filePath = path.join(imageDir, fileName);
        try {
            await fs.access(filePath, fs.constants.R_OK);
        } catch (error) {
            console.warn(`⚠️  文件不可读: ${fileName}`);
            continue;
        }
        
        // 获取文件统计信息
        const stats = await fs.stat(filePath);
        
        const fileInfo = {
            name: fileName,
            size: stats.size,
            url: `${IMAGE_DIR}/${fileName}`,
            lastModified: stats.mtime.toISOString()
        };
        
        // 检查是否为 Saved Screens 格式文件
        const match = fileName.match(FILENAME_PATTERN);
        if (match) {
            const index = parseInt(match[1], 10);
            savedScreensFiles.push({
                ...fileInfo,
                index: index
            });
        } else {
            imageFiles.push(fileInfo);
        }
    }
    
    // 对 Saved Screens 文件按索引排序
    savedScreensFiles.sort((a, b) => a.index - b.index);
    
    // 合并所有文件
    const allFiles = [...imageFiles, ...savedScreensFiles];
    
    return {
        imageFiles,
        savedScreensFiles,
        allFiles
    };
}

async function generateManifest() {
    console.log('🔧 开始生成图片清单...');
    
    const { imageFiles, savedScreensFiles, allFiles } = await scanImageDirectory();
    
    // 生成清单数据
    const manifest = {
        generatedAt: new Date().toISOString(),
        version: '1.0.0',
        dir: `${IMAGE_DIR}/`,
        
        // 所有文件列表（按原有格式保持兼容性）
        files: allFiles.map(file => file.name),
        
        // 详细文件信息
        details: allFiles,
        
        // 统计信息
        stats: {
            totalFiles: allFiles.length,
            totalSize: allFiles.reduce((sum, file) => sum + file.size, 0),
            imageFiles: imageFiles.length,
            savedScreensFiles: savedScreensFiles.length
        }
    };
    
    // 如果有 Saved Screens 文件，添加索引信息
    if (savedScreensFiles.length > 0) {
        const indices = savedScreensFiles.map(f => f.index);
        manifest.stats.savedScreens = {
            count: savedScreensFiles.length,
            minIndex: Math.min(...indices),
            maxIndex: Math.max(...indices),
            indexRange: indices
        };
    }
    
    // 写入清单文件
    const manifestPath = path.resolve(process.cwd(), IMAGE_DIR, 'manifest.json');
    await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2), 'utf-8');
    
    // 输出统计信息
    console.log('✅ 清单生成完成');
    console.log(`📄 清单文件: ${manifestPath}`);
    console.log(`📊 统计信息:`);
    console.log(`   总文件数: ${manifest.stats.totalFiles}`);
    console.log(`   总大小: ${(manifest.stats.totalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   普通图片: ${manifest.stats.imageFiles}`);
    console.log(`   Saved Screens: ${manifest.stats.savedScreensFiles}`);
    
    if (manifest.stats.savedScreens) {
        const { minIndex, maxIndex, count } = manifest.stats.savedScreens;
        console.log(`   Saved Screens 范围: ${minIndex} - ${maxIndex} (共${count}个)`);
        
        // 检查是否有缺失的索引
        const expectedCount = maxIndex - minIndex + 1;
        if (count < expectedCount) {
            const missing = [];
            for (let i = minIndex; i <= maxIndex; i++) {
                if (!manifest.stats.savedScreens.indexRange.includes(i)) {
                    missing.push(i);
                }
            }
            console.log(`⚠️  缺失的 Saved Screens 索引: ${missing.join(', ')}`);
        }
    }
    
    return manifest;
}

// 运行脚本
if (process.argv[1] && process.argv[1].endsWith('build-image-manifest.mjs')) {
    console.log('🚀 开始执行脚本...');
    try {
        await generateManifest();
        console.log('🎉 脚本执行完成');
        process.exit(0);
    } catch (error) {
        console.error('❤️ 生成清单失败:', error);
        process.exit(1);
    }
}

export { generateManifest };