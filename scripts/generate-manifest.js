// 生成 示例图片/manifest.json，供前端扫描目录使用
const fs = require('fs');
const path = require('path');

const IMAGES_DIR = path.join(__dirname, '..', '示例图片');
const OUT_FILE = path.join(IMAGES_DIR, 'manifest.json');

const exts = new Set(['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg']);

function main() {
  if (!fs.existsSync(IMAGES_DIR)) {
    console.error('目录不存在:', IMAGES_DIR);
    process.exit(1);
  }

  const files = fs
    .readdirSync(IMAGES_DIR)
    .filter((f) => exts.has(path.extname(f).toLowerCase()))
    .sort((a, b) => a.localeCompare(b, 'zh-Hans-CN'));

  const data = {
    generatedAt: new Date().toISOString(),
    files,
  };

  fs.writeFileSync(OUT_FILE, JSON.stringify(data, null, 2), 'utf8');
  console.log(`Manifest written: ${OUT_FILE} (files: ${files.length})`);
}

main();

