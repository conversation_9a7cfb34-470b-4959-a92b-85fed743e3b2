<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投资页面 - 图片展示</title>
    <link rel="stylesheet" href="gallery-styles.css">
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <button class="back-btn" onclick="goBack()">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,8 8,12 12,16"/>
            </svg>
        </button>
        <h1 class="page-title" id="pageTitle">投资页面</h1>
        
    </header>

    <!-- 瀑布流容器 -->
    <main class="gallery-container">
        <div class="masonry-grid" id="masonryGrid">
            <!-- 瀑布流图片将通过JavaScript动态生成 -->
        </div>
    </main>

    <!-- 图片预览模态弹窗 -->
    <div class="modal-overlay" id="imageModal">
        <div class="modal-container">
            <div class="modal-controls">
                <button class="modal-control-btn modal-refresh-btn" id="refreshAnalysisBtn" title="重新生成分析">
                    <svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M21 12a9 9 0 1 1-2.64-6.36" fill="none" stroke="currentColor" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M21 4v5h-5" fill="none" stroke="currentColor" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </button>
                <button class="modal-control-btn modal-close-btn" id="closeModal">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>

            <div class="modal-content">
                <!-- 左侧大图预览区域 -->
                <div class="modal-image-section">
                    <div class="modal-image-container">
                        <img id="modalImage" src="" alt="预览图片" />
                        <div class="modal-image-loading">
                            <div class="loading-spinner"></div>
                        </div>
                    </div>
                </div>

                <!-- 右侧信息面板 -->
                <div class="modal-info-section">
                    <div class="modal-info-header">
                        <h3>⚡ AI图像分析</h3>
                    </div>

                    <div class="modal-info-content">
                        <!-- 图像描述区域 -->
                        <div class="info-block">
                            <h4>📝 图像描述</h4>
                            <div class="description-content" id="imageDescription">
                                <div class="analysis-loading">
                                    <div class="loading-dots"></div>
                                    <span>AI正在分析图片...</span>
                                </div>
                            </div>
                        </div>

                        <!-- 标签区域 -->
                        <div class="info-block">
                            <h4>🏷️ 智能标签</h4>
                            <div class="tags-content" id="imageTags">
                                <div class="analysis-loading">
                                    <div class="loading-dots"></div>
                                    <span>正在生成标签...</span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    

    <script src="semantic-search.js"></script>
    <script src="openrouter-api.js"></script>
    <script src="gallery-script.js"></script>
</body>
</html>
