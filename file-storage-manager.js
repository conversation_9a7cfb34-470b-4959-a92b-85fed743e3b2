/**
 * 文件存储管理器
 * 负责处理图片元数据的文件存储，支持JSON和CSV格式
 */
class FileStorageManager {
    constructor() {
        this.dataPath = './data/';
        this.jsonFile = 'images_metadata.json';
        this.csvFile = 'images_index.csv';
        this.backupPath = './data/backups/';
    }

    /**
     * 初始化存储管理器
     */
    async init() {
        console.log('[FileStorageManager] 初始化文件存储管理器');
        await this.ensureDirectories();
    }

    /**
     * 确保必要的目录存在
     */
    async ensureDirectories() {
        // 注意：在浏览器环境中，我们无法直接创建目录
        // 这个方法主要是为了文档说明，实际创建目录需要在服务器端完成
        console.log('[FileStorageManager] 检查数据目录结构');
    }

    /**
     * 保存图片元数据到JSON文件
     * @param {Map} imagesMap - 图片元数据Map对象
     */
    async saveToJson(imagesMap) {
        try {
            const data = {
                version: "2.0",
                timestamp: new Date().toISOString(),
                totalImages: imagesMap.size,
                images: {}
            };

            // 转换Map为普通对象
            imagesMap.forEach((metadata, path) => {
                data.images[path] = metadata;
            });

            const jsonContent = JSON.stringify(data, null, 2);
            
            // 使用文件下载的方式保存
            await this.downloadFile(jsonContent, this.jsonFile, 'application/json');
            
            console.log(`[FileStorageManager] JSON数据已保存，包含 ${imagesMap.size} 张图片`);
            return true;
        } catch (error) {
            console.error('[FileStorageManager] 保存JSON失败:', error);
            return false;
        }
    }

    /**
     * 从JSON文件加载图片元数据
     * @returns {Map} 图片元数据Map对象
     */
    async loadFromJson() {
        try {
            // 由于浏览器安全限制，我们需要用户手动选择文件
            const fileContent = await this.selectAndReadFile('.json');
            if (!fileContent) {
                console.log('[FileStorageManager] 未选择JSON文件或文件为空');
                return new Map();
            }

            const data = JSON.parse(fileContent);
            const imagesMap = new Map();

            if (data.images) {
                Object.entries(data.images).forEach(([path, metadata]) => {
                    imagesMap.set(path, metadata);
                });
                console.log(`[FileStorageManager] 从JSON加载了 ${imagesMap.size} 张图片数据`);
            }

            return imagesMap;
        } catch (error) {
            console.error('[FileStorageManager] 加载JSON失败:', error);
            return new Map();
        }
    }

    /**
     * 导出为CSV格式
     * @param {Map} imagesMap - 图片元数据Map对象
     */
    async exportToCsv(imagesMap) {
        try {
            const headers = [
                '路径',
                '描述',
                '标签',
                'APP分类',
                '页面类型', 
                '控件类型',
                '最后更新时间',
                '状态'
            ];

            let csvContent = headers.join(',') + '\n';

            imagesMap.forEach((metadata, path) => {
                const row = [
                    this.escapeCsvField(path),
                    this.escapeCsvField(metadata.description || ''),
                    this.escapeCsvField((metadata.tags || []).join(';')),
                    this.escapeCsvField(this.extractTopCategories(metadata.classifications?.appCategories)),
                    this.escapeCsvField(this.extractTopCategories(metadata.classifications?.pageTypes)),
                    this.escapeCsvField(this.extractTopCategories(metadata.classifications?.controlTypes)),
                    this.escapeCsvField(metadata.lastUpdated || ''),
                    this.escapeCsvField(metadata.indexed ? '已索引' : '未索引')
                ];
                csvContent += row.join(',') + '\n';
            });

            await this.downloadFile(csvContent, this.csvFile, 'text/csv');
            console.log(`[FileStorageManager] CSV数据已导出，包含 ${imagesMap.size} 张图片`);
            return true;
        } catch (error) {
            console.error('[FileStorageManager] 导出CSV失败:', error);
            return false;
        }
    }

    /**
     * 从CSV文件导入数据
     * @returns {Map} 图片元数据Map对象
     */
    async importFromCsv() {
        try {
            const fileContent = await this.selectAndReadFile('.csv');
            if (!fileContent) {
                console.log('[FileStorageManager] 未选择CSV文件或文件为空');
                return new Map();
            }

            const lines = fileContent.split('\n').filter(line => line.trim());
            if (lines.length < 2) {
                throw new Error('CSV文件格式不正确');
            }

            const headers = this.parseCsvLine(lines[0]);
            const imagesMap = new Map();

            for (let i = 1; i < lines.length; i++) {
                const values = this.parseCsvLine(lines[i]);
                if (values.length >= headers.length) {
                    const path = values[0];
                    const metadata = {
                        path: path,
                        description: values[1] || '',
                        tags: values[2] ? values[2].split(';').filter(t => t.trim()) : [],
                        classifications: {
                            appCategories: this.parseCategories(values[3]),
                            pageTypes: this.parseCategories(values[4]),
                            controlTypes: this.parseCategories(values[5])
                        },
                        lastUpdated: values[6] || new Date().toISOString(),
                        indexed: values[7] === '已索引'
                    };
                    imagesMap.set(path, metadata);
                }
            }

            console.log(`[FileStorageManager] 从CSV导入了 ${imagesMap.size} 张图片数据`);
            return imagesMap;
        } catch (error) {
            console.error('[FileStorageManager] 导入CSV失败:', error);
            return new Map();
        }
    }

    /**
     * 创建自动备份
     * @param {Map} imagesMap - 图片元数据Map对象
     */
    async createBackup(imagesMap) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFileName = `images_metadata_backup_${timestamp}.json`;
        
        const data = {
            version: "2.0",
            timestamp: new Date().toISOString(),
            totalImages: imagesMap.size,
            images: {}
        };

        imagesMap.forEach((metadata, path) => {
            data.images[path] = metadata;
        });

        const jsonContent = JSON.stringify(data, null, 2);
        await this.downloadFile(jsonContent, backupFileName, 'application/json');
        
        console.log(`[FileStorageManager] 备份已创建: ${backupFileName}`);
    }

    // ================== 辅助方法 ==================

    /**
     * 提取顶级分类
     * @param {Array} categories - 分类数组
     * @returns {string} 分类字符串
     */
    extractTopCategories(categories) {
        if (!categories || !Array.isArray(categories)) return '';
        return categories
            .sort((a, b) => b.score - a.score)
            .slice(0, 3)
            .map(c => c.category)
            .join(';');
    }

    /**
     * 解析分类字符串
     * @param {string} categoriesStr - 分类字符串
     * @returns {Array} 分类数组
     */
    parseCategories(categoriesStr) {
        if (!categoriesStr) return [];
        return categoriesStr.split(';').map(cat => ({
            category: cat.trim(),
            score: 1,
            keywords: []
        })).filter(c => c.category);
    }

    /**
     * CSV字段转义
     * @param {string} field - 字段内容
     * @returns {string} 转义后的字段
     */
    escapeCsvField(field) {
        if (!field) return '';
        const fieldStr = String(field);
        if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
            return '"' + fieldStr.replace(/"/g, '""') + '"';
        }
        return fieldStr;
    }

    /**
     * 解析CSV行
     * @param {string} line - CSV行
     * @returns {Array} 字段数组
     */
    parseCsvLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                if (inQuotes && line[i + 1] === '"') {
                    current += '"';
                    i++; // 跳过下一个引号
                } else {
                    inQuotes = !inQuotes;
                }
            } else if (char === ',' && !inQuotes) {
                result.push(current);
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current);
        return result;
    }

    /**
     * 下载文件
     * @param {string} content - 文件内容
     * @param {string} filename - 文件名
     * @param {string} mimeType - MIME类型
     */
    async downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * 选择并读取文件
     * @param {string} accept - 接受的文件类型
     * @returns {Promise<string>} 文件内容
     */
    selectAndReadFile(accept) {
        return new Promise((resolve) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = accept;
            input.style.display = 'none';
            
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) {
                    resolve(null);
                    return;
                }
                
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = () => resolve(null);
                reader.readAsText(file);
            };
            
            document.body.appendChild(input);
            input.click();
            document.body.removeChild(input);
        });
    }
}

// 创建全局实例
window.fileStorageManager = new FileStorageManager();