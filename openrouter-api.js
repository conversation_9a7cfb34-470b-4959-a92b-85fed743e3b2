class OpenRouterAPI {
    constructor() {
        this.baseURL = 'https://openrouter.ai/api/v1';
        this.apiKey = this.getStoredApiKey();
        this.model = this.getStoredModel();
        this.isConnected = false;
    }

    getStoredApiKey() {
        return localStorage.getItem('openrouter_api_key') || '';
    }

    getStoredModel() {
        return localStorage.getItem('openrouter_model') || 'anthropic/claude-3.5-sonnet';
    }

    // 获取支持视觉的模型列表（常见模型的白名单，便于快速判断）
    getVisionSupportedModels() {
        return [
            // Anthropic
            'anthropic/claude-3.5-sonnet',
            'anthropic/claude-3-opus',
            'anthropic/claude-3-sonnet',
            'anthropic/claude-3-haiku',
            // OpenAI
            'openai/gpt-4-vision-preview',
            'openai/gpt-4o',
            'openai/gpt-4o-mini',
            // Google
            'google/gemini-2.5-flash-image-preview',
            'google/gemini-2.5-flash-preview-09-2025',
            // Qwen（阿里巴巴通义千问）
            'qwen/qwen-vl-plus',
            'qwen/qwen-vl-max',
            'qwen/qwen2-vl-72b-instruct',
            'qwen/qwen2.5-vl-7b-instruct'
        ];
    }

    // 检查当前模型是否支持视觉
    isVisionSupported() {
        const model = (this.model || '').toLowerCase();
        if (!model) return false;

        // 1) 先查白名单
        if (this.getVisionSupportedModels().map(m => m.toLowerCase()).includes(model)) {
            return true;
        }

        // 2) 名称启发式：包含常见视觉模型关键词时认为支持
        // - qwen 的视觉模型通常包含 "-vl" 或 "/qwen-vl"
        // - openai 的视觉模型包含 "gpt-4o" 或 "vision"
        // - 其他厂商常见带有 "vision"、"image"、"vl"
        const heuristic = /(gpt-4o|vision|\bvl\b|\-vl|\/qwen-vl|image)/i;
        return heuristic.test(model);
    }

    setApiKey(key) {
        this.apiKey = key;
        localStorage.setItem('openrouter_api_key', key);
    }

    setModel(model) {
        this.model = model;
        localStorage.setItem('openrouter_model', model);
    }

    getHeaders() {
        return {
            'Authorization': `Bearer ${this.apiKey}`,
            'HTTP-Referer': window.location.origin,
            'X-Title': 'AI Experience Design Chat',
            'Content-Type': 'application/json; charset=utf-8'
        };
    }

    async testConnection() {
        if (!this.apiKey) {
            throw new Error('API key not configured');
        }

        try {
            const response = await fetch(`${this.baseURL}/models`, {
                method: 'GET',
                headers: this.getHeaders()
            });

            if (response.ok) {
                this.isConnected = true;
                return true;
            } else {
                const error = await response.json().catch(() => ({ error: 'Unknown error' }));
                throw new Error(error.error?.message || 'Connection failed');
            }
        } catch (error) {
            this.isConnected = false;
            throw error;
        }
    }

    async chat(messages, onStream = null, context = null) {
        if (!this.apiKey) {
            throw new Error('Please configure API key first');
        }

        const systemMessage = this.buildSystemMessage(context);
        const allMessages = systemMessage ? [systemMessage, ...messages] : messages;

        const requestBody = {
            model: this.model,
            messages: allMessages,
            stream: onStream ? true : false,
            max_tokens: 4000,
            temperature: 0.7
        };

        try {
            const response = await fetch(`${this.baseURL}/chat/completions`, {
                method: 'POST',
                headers: this.getHeaders(),
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const error = await response.json().catch(() => ({ error: 'Unknown error' }));
                throw new Error(error.error?.message || `HTTP ${response.status}`);
            }

            if (onStream && response.body) {
                await this.handleStreamResponse(response.body, onStream);
                return null;
            } else {
                const data = await response.json();
                return data.choices[0]?.message?.content || '';
            }
        } catch (error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('Network connection error, please check your network settings');
            }
            throw error;
        }
    }

    async handleStreamResponse(body, onStream) {
        const reader = body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);

                        if (data === '[DONE]') {
                            onStream('', true);
                            return;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            const content = parsed.choices?.[0]?.delta?.content;

                            if (content) {
                                onStream(content, false);
                            }
                        } catch (e) {
                            // 忽略解析错误，继续处理其他chunks
                            console.warn('Failed to parse SSE chunk:', e);
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }
    }

    buildSystemMessage(context) {
        if (!context || context.length === 0) {
            return {
                role: 'system',
                content: '你是一个专门研究AI体验设计的助手。请基于提供的资料回答用户问题，回答要准确、有条理，并在适当位置添加引用标记。'
            };
        }

        const contextText = context.map((doc, index) =>
            `[文档${index + 1}] ${doc.filename}\n${doc.content}`
        ).join('\n\n');

        return {
            role: 'system',
            content: `你是一个专门研究AI体验设计的助手。请基于以下提供的资料回答用户问题：

${contextText}

请根据上述资料回答问题，并在回答中使用 <sup class="citation" data-ref="数字">数字</sup> 格式添加引用标记。如果资料中没有相关信息，请诚实说明。`
        };
    }

    getConnectionStatus() {
        return {
            connected: this.isConnected,
            hasApiKey: !!this.apiKey,
            model: this.model
        };
    }

    async getAvailableModels() {
        try {
            const response = await fetch(`${this.baseURL}/models`, {
                method: 'GET',
                headers: this.getHeaders()
            });

            if (response.ok) {
                const data = await response.json();
                return data.data || [];
            }
            return [];
        } catch (error) {
            console.error('Failed to fetch models:', error);
            return [];
        }
    }

    // 专门用于图像分析的方法
    async analyzeImage(base64Image, prompt = null) {
        if (!this.isVisionSupported()) {
            throw new Error(`当前模型 ${this.model} 不支持图像分析，请选择支持视觉的模型`);
        }

        const defaultPrompt = '请分析这张图片，提供详细的描述和相关标签。';

        const messages = [
            {
                role: 'user',
                content: [
                    {
                        type: 'image_url',
                        image_url: {
                            url: base64Image
                        }
                    },
                    {
                        type: 'text',
                        text: prompt || defaultPrompt
                    }
                ]
            }
        ];

        return await this.chat(messages);
    }

    // 将图片文件转换为base64的辅助方法
    async fileToBase64(file) {
        return new Promise((resolve, reject) => {
            if (!file || !file.type.startsWith('image/')) {
                reject(new Error('请选择有效的图片文件'));
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                resolve(e.target.result);
            };
            reader.onerror = function() {
                reject(new Error('文件读取失败'));
            };
            reader.readAsDataURL(file);
        });
    }

    // 直接从File对象转换为base64
    async imageFileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = function(e) {
                // FileReader的结果已经是data URL格式
                const base64 = e.target.result;
                console.log('用户文件转换成功，base64长度：', base64.length);
                resolve(base64);
            };

            reader.onerror = function(e) {
                console.error('文件读取失败：', e);
                reject(new Error('文件读取失败'));
            };

            reader.readAsDataURL(file);
        });
    }

    // 从URL加载图片并转换为base64
    async imageUrlToBase64(imageUrl) {
        return new Promise((resolve, reject) => {
            const img = new Image();

            // 首先尝试不设置crossOrigin（对于同源图片）
            img.onload = function() {
                try {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = img.width;
                    canvas.height = img.height;
                    ctx.drawImage(img, 0, 0);

                    const base64 = canvas.toDataURL('image/jpeg', 0.8);
                    console.log('图片转换成功，base64长度：', base64.length);
                    resolve(base64);
                } catch (e) {
                    console.error('Canvas转换失败：', e);
                    // 如果canvas转换失败，尝试设置crossOrigin重新加载
                    this.onerror = null;
                    this.onload = null;
                    this.crossOrigin = 'anonymous';
                    this.src = imageUrl + '?t=' + Date.now(); // 添加时间戳避免缓存

                    this.onload = function() {
                        try {
                            const canvas2 = document.createElement('canvas');
                            const ctx2 = canvas2.getContext('2d');
                            canvas2.width = this.width;
                            canvas2.height = this.height;
                            ctx2.drawImage(this, 0, 0);
                            const base64_2 = canvas2.toDataURL('image/jpeg', 0.8);
                            resolve(base64_2);
                        } catch (e2) {
                            reject(new Error('图片跨域访问受限，无法转换'));
                        }
                    };

                    this.onerror = () => reject(new Error('图片加载失败（跨域）'));
                }
            };

            img.onerror = function(e) {
                console.error('图片加载失败：', imageUrl, e);
                reject(new Error(`图片加载失败: ${imageUrl}`));
            };

            console.log('开始加载图片：', imageUrl);
            img.src = imageUrl;
        });
    }

    formatError(error) {
        if (error.message.includes('401')) {
            return 'API密钥无效或已过期，请检查你的密钥';
        }
        if (error.message.includes('429')) {
            return '请求过于频繁，请稍后再试';
        }
        if (error.message.includes('500')) {
            return 'OpenRouter服务器错误，请稍后再试';
        }
        return error.message || '未知错误';
    }

    /**
     * 语义搜索相关方法
     */

    /**
     * 调用AI API进行语义理解和搜索
     * @param {string} query - 用户查询
     * @param {Array} imageDescriptions - 图片描述数组
     * @returns {Promise<Array>} 语义匹配结果
     */
    async semanticSearch(query, imageDescriptions) {
        if (!this.apiKey) {
            throw new Error('请先配置API密钥');
        }

        const prompt = `作为图片搜索分析师，请分析用户查询意图并匹配最相关的图片。

用户查询: "${query}"

图片数据:
${imageDescriptions.map((img, index) =>
    `[${index}] 路径: ${img.path}
    描述: ${img.description || '无描述'}
    标签: ${(img.tags || []).join(', ') || '无标签'}`
).join('\n\n')}

请返回JSON格式的分析结果：
{
  "queryAnalysis": {
    "intent": "用户意图描述",
    "keywords": ["关键词1", "关键词2"],
    "searchType": "visual|functional|categorical",
    "complexity": "simple|complex"
  },
  "matches": [
    {
      "imageIndex": 0,
      "relevanceScore": 0.95,
      "matchReason": "匹配原因说明",
      "semanticSimilarity": 0.9,
      "contextualRelevance": 0.8
    }
  ]
}

要求：
1. relevanceScore应在0-1之间，表示总体相关度
2. 至少返回3个匹配结果（如果有的话）
3. matchReason要具体说明为什么匹配
4. 按相关度降序排列matches

只返回JSON，不要其他解释。`;

        try {
            const response = await this.callAPI(prompt);
            const result = JSON.parse(response);

            // 验证和标准化结果
            return this.normalizeSemanticSearchResult(result, imageDescriptions);
        } catch (error) {
            console.error('语义搜索失败:', error);
            throw new Error('语义搜索分析失败: ' + error.message);
        }
    }

    /**
     * 分析查询意图
     * @param {string} query - 用户查询
     * @returns {Promise<Object>} 查询意图分析结果
     */
    async analyzeQueryIntent(query) {
        const prompt = `分析以下用户搜索查询的意图，返回结构化分析结果：

查询: "${query}"

请返回JSON格式：
{
  "intent": "具体搜索意图描述",
  "keywords": ["主要关键词"],
  "categories": {
    "app": ["相关应用类型"],
    "page": ["相关页面类型"],
    "control": ["相关控件类型"]
  },
  "attributes": {
    "colors": ["颜色相关"],
    "styles": ["风格相关"],
    "functions": ["功能相关"]
  },
  "searchScope": "broad|specific|visual|functional",
  "complexity": "simple|moderate|complex",
  "userNeed": "用户真实需求描述"
}

只返回JSON，不要解释。`;

        try {
            const response = await this.callAPI(prompt);
            return JSON.parse(response);
        } catch (error) {
            console.warn('查询意图分析失败，使用基础分析', error);
            return this.getBasicQueryAnalysis(query);
        }
    }

    /**
     * 计算语义相似度
     * @param {string} query - 查询文本
     * @param {string} description - 图片描述
     * @returns {Promise<number>} 相似度分数 (0-1)
     */
    async calculateSemanticSimilarity(query, description) {
        const prompt = `计算以下两段文本的语义相似度：

文本1（查询）: "${query}"
文本2（描述）: "${description}"

请返回JSON格式：
{
  "similarity": 0.85,
  "explanation": "相似度说明",
  "keyMatches": ["匹配的关键概念"],
  "semanticRelations": ["语义关联说明"]
}

similarity取值0-1，1表示完全相似，0表示完全不相似。
只返回JSON。`;

        try {
            const response = await this.callAPI(prompt);
            const result = JSON.parse(response);
            return Math.max(0, Math.min(1, result.similarity || 0));
        } catch (error) {
            console.warn('语义相似度计算失败，使用简单算法', error);
            return this.calculateSimpleSimilarity(query, description);
        }
    }

    /**
     * 文本智能摘要和关键词提取
     * @param {string} text - 输入文本
     * @returns {Promise<Object>} 摘要和关键词
     */
    async extractKeywordsAndSummary(text) {
        const prompt = `分析以下文本，提取关键信息：

文本: "${text}"

返回JSON格式：
{
  "summary": "一句话摘要",
  "keywords": ["关键词1", "关键词2"],
  "topics": ["主题1", "主题2"],
  "sentiment": "positive|neutral|negative",
  "complexity": "simple|moderate|complex"
}

只返回JSON。`;

        try {
            const response = await this.callAPI(prompt);
            return JSON.parse(response);
        } catch (error) {
            console.warn('关键词提取失败，使用基础方法', error);
            return this.extractBasicKeywords(text);
        }
    }

    /**
     * 智能搜索建议生成
     * @param {string} partialQuery - 部分查询输入
     * @param {Array} recentQueries - 最近查询历史
     * @returns {Promise<Array>} 搜索建议列表
     */
    async generateSearchSuggestions(partialQuery, recentQueries = []) {
        const prompt = `基于用户的部分输入和搜索历史，生成智能搜索建议：

当前输入: "${partialQuery}"
最近查询: ${recentQueries.join(', ')}

生成适合UI界面搜索的建议，返回JSON格式：
{
  "suggestions": [
    {
      "text": "建议文本",
      "type": "completion|related|popular",
      "category": "app|page|control|style|color",
      "confidence": 0.9
    }
  ]
}

要求：
1. 提供5-8个建议
2. 建议要简洁实用
3. 优先高相关度的建议
4. confidence表示建议质量(0-1)

只返回JSON。`;

        try {
            const response = await this.callAPI(prompt);
            const result = JSON.parse(response);
            return result.suggestions || [];
        } catch (error) {
            console.warn('搜索建议生成失败，使用基础建议', error);
            return this.getBasicSearchSuggestions(partialQuery);
        }
    }

    /**
     * 通用API调用方法
     * @param {string} prompt - 提示词
     * @param {Object} options - 可选参数
     * @returns {Promise<string>} API响应
     */
    async callAPI(prompt, options = {}) {
        const messages = [
            {
                role: 'user',
                content: prompt
            }
        ];

        const requestBody = {
            model: this.model,
            messages: messages,
            max_tokens: options.maxTokens || 2000,
            temperature: options.temperature || 0.3,
            stream: false
        };

        const response = await fetch(`${this.baseURL}/chat/completions`, {
            method: 'POST',
            headers: this.getHeaders(),
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const error = await response.json().catch(() => ({ error: 'Unknown error' }));
            throw new Error(error.error?.message || `HTTP ${response.status}`);
        }

        const data = await response.json();
        return data.choices[0]?.message?.content || '';
    }

    /**
     * 辅助方法：标准化语义搜索结果
     */
    normalizeSemanticSearchResult(result, imageDescriptions) {
        const normalized = {
            queryAnalysis: result.queryAnalysis || {},
            matches: []
        };

        if (result.matches && Array.isArray(result.matches)) {
            normalized.matches = result.matches
                .filter(match =>
                    match.imageIndex >= 0 &&
                    match.imageIndex < imageDescriptions.length &&
                    match.relevanceScore > 0
                )
                .map(match => ({
                    ...imageDescriptions[match.imageIndex],
                    relevanceScore: Math.max(0, Math.min(1, match.relevanceScore)),
                    matchReason: match.matchReason || '语义匹配',
                    semanticSimilarity: match.semanticSimilarity || match.relevanceScore,
                    contextualRelevance: match.contextualRelevance || match.relevanceScore
                }))
                .sort((a, b) => b.relevanceScore - a.relevanceScore);
        }

        return normalized;
    }

    /**
     * 基础查询分析（回退方案）
     */
    getBasicQueryAnalysis(query) {
        const keywords = query.toLowerCase().split(/\s+/).filter(w => w.length > 1);

        return {
            intent: `搜索与"${query}"相关的界面`,
            keywords: keywords,
            categories: {
                app: [],
                page: [],
                control: []
            },
            attributes: {
                colors: [],
                styles: [],
                functions: []
            },
            searchScope: keywords.length > 3 ? 'complex' : 'simple',
            complexity: 'simple',
            userNeed: query
        };
    }

    /**
     * 简单相似度计算（回退方案）
     */
    calculateSimpleSimilarity(query, description) {
        const queryWords = new Set(query.toLowerCase().split(/\s+/));
        const descWords = description.toLowerCase().split(/\s+/);

        let matches = 0;
        for (const word of descWords) {
            if (queryWords.has(word)) {
                matches++;
            }
        }

        return queryWords.size > 0 ? matches / queryWords.size : 0;
    }

    /**
     * 基础关键词提取（回退方案）
     */
    extractBasicKeywords(text) {
        const words = text.toLowerCase().split(/\s+/).filter(w => w.length > 2);
        const keywords = [...new Set(words)].slice(0, 5);

        return {
            summary: text.slice(0, 50) + (text.length > 50 ? '...' : ''),
            keywords: keywords,
            topics: [],
            sentiment: 'neutral',
            complexity: text.length > 100 ? 'moderate' : 'simple'
        };
    }

    /**
     * 基础搜索建议（回退方案）
     */
    getBasicSearchSuggestions(partialQuery) {
        const basicSuggestions = [
            '金融应用', '支付界面', '登录页面', '设置界面',
            '健康应用', '体育应用', '深色主题', '浅色主题',
            '卡片布局', '按钮设计', '输入框', '导航栏'
        ];

        return basicSuggestions
            .filter(suggestion =>
                !partialQuery || suggestion.includes(partialQuery) ||
                partialQuery.split('').some(char => suggestion.includes(char))
            )
            .slice(0, 6)
            .map(text => ({
                text,
                type: 'popular',
                category: 'general',
                confidence: 0.7
            }));
    }
}

// 全局实例 - 同时导出两种命名方式以兼容
const openRouterAPIInstance = new OpenRouterAPI();
window.openrouterAPI = openRouterAPIInstance;
window.openRouterAPI = openRouterAPIInstance;
