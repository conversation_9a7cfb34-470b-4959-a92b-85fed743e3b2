# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述
这是一个AI体验设计案例展示网站项目，专注于展示各种AI产品在不同行业中的应用案例。项目采用原生HTML、CSS、JavaScript技术栈，无需构建工具或包管理器。

## 技术架构

### 核心文件结构
#### 主要页面
- `index.html` - 主页面，展示项目卡片和搜索功能
- `chat.html` - 聊天对话页面，模拟NotebookLM风格的AI问答界面，支持RAG功能
- `gallery.html` - 图片画廊页面，按类别展示AI应用界面截图

#### JavaScript模块
- `script.js` - 主页面交互逻辑，包含动态分类展示管理器和图片索引功能
- `chat-script.js` - 聊天页面逻辑，包含AI回答数据库和对话交互，集成RAG功能
- `gallery-script.js` - 图片画廊交互逻辑，支持新的分类筛选系统
- `image-classifier.js` - 图片分类引擎，基于关键词和语义分析自动分类图片
- `images-metadata.js` - 图片元数据管理系统，统一管理图片标签、描述和分类信息
- `image-indexer.js` - 图片索引系统，自动扫描和分析新图片，支持AI标注
- `db-manager.js` - IndexedDB数据库管理器，处理文档和片段存储
- `rag-engine.js` - RAG（检索增强生成）引擎，实现语义搜索和相关性评分
- `file-processor.js` - 文件处理器，支持文本、Markdown、PDF、Word文档解析和分块
- `openrouter-api.js` - OpenRouter API集成，提供真实的AI对话能力
- `markdown-parser.js` - Markdown解析器
- `pdf-viewer.js` - PDF查看器

#### 样式文件
- `styles.css` - 主页面样式
- `chat-styles.css` - 聊天页面样式
- `gallery-styles.css` - 图片画廊样式

#### 资源目录
- `示例图片/` - 存放各种AI应用界面截图
- `分类/` - 包含分类标准文档和参考图片
  - `标签分类明细.md` - 三大分类体系：APP分类、页面类型、控件

### 数据架构

#### 图片分类系统
- **三大分类体系**：APP分类（32类）、页面类型（40类）、控件（38类）
- **动态分类数据**：`ImageCategoryManager` 类管理分类展示，支持基于元数据的智能分组
- **图片元数据**：`ImagesMetadata` 类统一管理所有图片的标签、描述和分类映射
- **分类引擎**：`ImageClassifier` 基于关键词匹配和语义分析自动分类图片
- **索引系统**：`ImageIndexer` 自动扫描`示例图片/`目录，检测新图片并调用AI分析

#### 静态数据存储
- AI回答内容存储在 `chat-script.js` 中的 `aiResponses` 对象，支持关键词匹配
- 分类标准定义在 `分类/标签分类明细.md` 中，包含完整的分类映射规则

#### IndexedDB存储（RAG功能）
- `documents` 表：存储上传的文档文件及其元数据
- `chunks` 表：存储文档分块数据，用于语义搜索
- `conversations` 表：存储聊天对话历史
- 支持文本、Markdown、PDF、Word文档的自动分块和索引

### 页面交互流程

#### 智能分类展示流程
1. 主页动态生成三个Tab的分类卡片（类别/控件/流程）
2. 用户点击"索引图片"按钮扫描新图片，自动调用AI分析并分类
3. 点击分类卡片 → 跳转到 `gallery.html?type=app&category=金融`
4. 画廊页面根据分类类型和具体分类筛选显示图片

#### 聊天对话流程
1. 用户在主页输入问题 → 跳转到 `chat.html?question=...`
2. 聊天页面解析URL参数，显示问题并生成AI回答
3. 支持打字机效果、引用标注、来源高亮等交互特性

#### RAG增强流程
1. 用户可上传文档（文本、Markdown、PDF、Word）到聊天页面
2. 文档自动分块存储到IndexedDB中
3. 用户提问时，RAG引擎搜索相关文档片段
4. 结合检索到的上下文和OpenRouter API生成回答
5. 支持配置API密钥和模型选择

#### 图片索引与分析流程
1. `ImageIndexer` 扫描`示例图片/`目录发现新图片
2. 对新图片调用OpenRouter API进行AI分析（可选）或使用基础文件名分析
3. `ImageClassifier` 根据分析结果自动分类到三大体系
4. `ImagesMetadata` 更新图片元数据并刷新主页分类展示

## 开发环境

### 本地开发
#### 启动开发服务器
```bash
# 使用本地HTTP服务器运行（推荐）
python3 -m http.server 8000
# 或
npx serve .
# 或使用PHP内置服务器
php -S localhost:8000
```

#### 开发调试
```bash
# 清除浏览器存储数据（IndexedDB、localStorage）
# 在浏览器控制台中执行：
# indexedDB.deleteDatabase('AIExperienceChat');
# localStorage.clear();

# 查看IndexedDB数据
# 浏览器开发者工具 > Application > Storage > IndexedDB > AIExperienceChat
```

### 文件修改注意事项

#### 图片分类系统修改
- **添加新图片**：直接放入 `示例图片/` 目录，然后在主页点击"索引图片"按钮自动分析分类
- **修改分类规则**：编辑 `image-classifier.js` 中的分类映射关键词
- **调整分类体系**：修改 `分类/标签分类明细.md` 中的分类定义
- **自定义分析逻辑**：修改 `image-indexer.js` 中的AI分析提示词

#### 数据配置修改
- 添加AI回答：在 `chat-script.js` 的 `aiResponses` 对象中添加新的问答对
- 修改静态分类数据：编辑 `script.js` 中 `ImageCategoryManager` 的 `initializeStaticData()` 方法

#### RAG功能配置
- 文档处理器配置：`file-processor.js` 中修改 `maxChunkSize`（默认1000字符）和 `chunkOverlap`（默认100字符）
- RAG引擎配置：`rag-engine.js` 中修改 `maxContextChunks`（默认5个）和 `minRelevanceScore`（默认0.1）
- API配置：通过聊天页面界面配置OpenRouter API密钥和模型选择

### 技术特性
#### 前端技术栈
- 纯原生JavaScript（ES6+），无需构建工具
- 使用现代CSS Grid和Flexbox布局
- 模块化设计，每个功能独立封装为类

#### 数据持久化
- IndexedDB用于本地存储文档和对话数据
- localStorage用于保存API配置和用户偏好
- 支持离线模式的基础数据展示

#### AI集成能力
- OpenRouter API集成，支持多种AI模型
- RAG（检索增强生成）功能，基于上传文档的智能问答
- 图片智能分析和自动分类，支持基于内容的标签生成
- 中英文分词和语义搜索算法

### 浏览器兼容性
- Chrome 60+, Firefox 55+, Safari 11+
- 依赖IndexedDB、Fetch API、ES6 Classes等现代Web API
- 需要HTTPS环境（或localhost）用于某些浏览器API访问