{"permissions": {"allow": ["mcp__playwright__browser_navigate", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_type", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_press_key", "mcp__playwright__browser_click", "mcp__playwright__browser_wait_for", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(python3:*)", "mcp__playwright__browser_handle_dialog", "mcp__playwright__browser_file_upload", "Bash(lsof:*)", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_close", "mcp__playwright__browser_console_messages", "Read(//Users/<USER>/.claude/**)", "Read(//Users/<USER>/**)", "<PERSON><PERSON>(cat:*)", "Bash(bun x ccusage:*)", "Read(//private/tmp/**)", "mcp__chrome-devtools__new_page", "mcp__chrome-devtools__list_pages"], "deny": [], "ask": []}}