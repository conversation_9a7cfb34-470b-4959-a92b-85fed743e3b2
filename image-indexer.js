/**
 * 图片索引系统
 * 负责扫描图片目录，索引新图片并自动分析标签和描述
 */
class ImageIndexer {
    constructor() {
        // 配置常量
        this.IMAGE_DIR = '示例图片/';
        this.FILENAME_PATTERN = /^Saved Screens (\d+)\.png$/i;
        this.SUPPORTED_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'];
        this.CONCURRENCY_LIMIT = 6; // 并发限制
        this.REQUEST_TIMEOUT = 10000; // 10秒超时
        this.RETRY_ATTEMPTS = 2; // 重试次数
        this.MISSING_THRESHOLD = 20; // 连续缺失阈值

        // 智能探测相关默认值（防止未定义引用）
        this.MAX_PROBE_COUNT = 500; // 探测最大序号上限
        this.MISSING_FILE_THRESHOLD = 30; // 连续未命中阈值
        this.indexedImages = new Set();
        this.indexingProgress = {
            total: 0,
            processed: 0,
            errors: 0,
            isIndexing: false
        };
        this.onProgressUpdate = null;
        this.onIndexComplete = null;
        // 通过原生文件系统访问（可选）
        this.directoryHandle = null;
        this.taxonomy = null; // 从 分类/标签分类明细.md 解析出的基础标签
    }

    /**
     * 设置进度回调
     * @param {Function} onProgress - 进度更新回调
     * @param {Function} onComplete - 索引完成回调
     */
    setCallbacks(onProgress, onComplete) {
        this.onProgressUpdate = onProgress;
        this.onIndexComplete = onComplete;
    }

    /**
     * 统一的URL编码方法
     * @param {string} directory - 目录路径
     * @param {string} fileName - 文件名
     * @returns {string} 编码后的URL
     */
    buildImageUrl(directory, fileName) {
        // 确保目录以斜杠结尾
        const dir = directory.endsWith('/') ? directory : directory + '/';
        // 目录名使用 encodeURI（保留斜杠），文件名使用 encodeURIComponent（处理空格与中文）
        const encodedDir = encodeURI(dir);
        const encodedFileName = encodeURIComponent(fileName);
        return `${encodedDir}${encodedFileName}`;
    }

    /**
     * 扫描目录获取所有图片文件
     * @param {string} directory - 图片目录路径
     * @returns {Promise<Array>} 图片文件路径列表
     */
    async scanDirectory(directory = this.IMAGE_DIR) {
        try {
            console.log(`📂 扫描图片目录: ${directory}`);

            // 优先策略1：从 manifest.json 加载清单
            const manifestList = await this.fetchManifestList(directory);
            if (manifestList && manifestList.length > 0) {
                console.log(`✅ 从清单加载 ${manifestList.length} 个图片文件`);
                return manifestList;
            }

            console.log('⚠️ 清单文件不存在或为空，尝试原生文件系统访问...');

            // 优先策略2：原生文件系统访问（需要安全上下文与用户授权）
            const nativeList = await this.scanWithFileSystemAccess(directory, this.SUPPORTED_EXTENSIONS);
            if (nativeList && nativeList.length > 0) {
                console.log(`✅ 从原生文件系统加载 ${nativeList.length} 个图片文件`);
                return nativeList;
            }

            console.log('⚠️ 原生访问不可用，尝试网络探测模式...');

            // 兑底策略3：智能探测模式
            const detectedList = await this.performIntelligentDetection(directory);
            if (detectedList && detectedList.length > 0) {
                console.log(`✅ 通过探测发现 ${detectedList.length} 个图片文件`);
                return detectedList;
            }

            console.warn('⚠️ 所有扫描策略均失败，使用内置回退列表');

            // 最终回退：使用内置示例列表避免完全无法使用
            return this.getFallbackImageList(directory);

        } catch (error) {
            console.error('❤️ 目录扫描失败:', error);
            return this.getFallbackImageList(directory);
        }
    }

    /**
     * 使用文件系统访问API扫描用户选择的目录
     * 仅在安全上下文且浏览器支持时可用
     */
    async scanWithFileSystemAccess(directory, supportedFormats) {
        try {
            if (!('showDirectoryPicker' in window)) return null;

            // 若未选择过目录，则提示一次
            if (!this.directoryHandle) {
                // 需要用户手动选择“示例图片”目录
                this.directoryHandle = await window.showDirectoryPicker();
            }

            const exts = new Set(supportedFormats);
            const files = [];
            for await (const [name, handle] of this.directoryHandle.entries()) {
                if (handle.kind === 'file') {
                    const lower = name.toLowerCase();
                    const ext = lower.slice(lower.lastIndexOf('.'));
                    if (exts.has(ext)) {
                        files.push(`${directory}${name}`);
                    }
                }
            }
            return files;
        } catch (e) {
            // 用户可能取消选择或环境不支持
            console.warn('原生目录访问不可用或已取消:', e?.message || e);
            return null;
        }
    }

    /**
     * 读取目录清单文件（manifest.json）
     */
    async fetchManifestList(directory) {
        const candidates = ['manifest.json', 'list.json', 'images.json'];

        for (const name of candidates) {
            try {
                const manifestUrl = this.buildImageUrl(directory, name);
                console.log(`🔍 尝试加载清单: ${manifestUrl}`);

                const res = await fetch(manifestUrl, {
                    cache: 'no-store',
                    headers: {
                        'Accept': 'application/json, text/plain, */*'
                    }
                });

                if (!res.ok) {
                    console.log(`⚠️ 清单 ${name} 加载失败: HTTP ${res.status}`);
                    continue;
                }

                const data = await res.json();
                console.log(`📊 清单数据:`, data);

                // 支持新的 manifest 格式（带有 details 字段）和旧格式
                let files = [];

                if (Array.isArray(data?.files)) {
                    // 新格式的 files 字段或旧格式兼容
                    files = data.files;
                } else if (Array.isArray(data)) {
                    // 直接数组格式
                    files = data;
                }

                if (files.length > 0) {
                    // 使用统一的URL编码方法构建完整路径
                    const imagePaths = files.map(fileName => this.buildImageUrl(directory, fileName));

                    console.log(`✅ 从 ${name} 加载了 ${files.length} 个图片文件`);

                    // 记录统计信息（如果有的话）
                    if (data.stats) {
                        console.log(`📈 清单统计:`, data.stats);
                    }

                    return imagePaths;
                }

            } catch (e) {
                console.warn(`⚠️ 加载清单 ${name} 失败:`, e.message);
            }
        }

        return null;
    }

    /**
     * 检查图片是否存在且可访问（支持超时和重试）
     * @param {string} imagePath - 图片路径
     * @returns {Promise<{exists: boolean, error?: string}>} 检查结果
     */
    async checkImageExists(imagePath) {
        try {
            // 优先使用HEAD请求检查文件存在，更精确且节省带宽
            const response = await this.fetchWithTimeout(imagePath, { method: 'HEAD' });

            if (response.ok) {
                return { exists: true };
            } else if (response.status === 404) {
                return { exists: false, error: `文件不存在 (404): ${imagePath}` };
            } else {
                return { exists: false, error: `HTTP ${response.status}: ${response.statusText}` };
            }

        } catch (error) {
            // 如果HEAD请求失败，尝试使用Image对象作为备用方案
            console.log(`HEAD请求失败，尝试Image加载: ${error.message}`);

            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve({ exists: true });
                img.onerror = () => resolve({
                    exists: false,
                    error: `图片加载失败: ${imagePath}`
                });
                img.src = imagePath;

                // 为Image加载设置超时
                setTimeout(() => {
                    img.src = ''; // 取消加载
                    resolve({
                        exists: false,
                        error: `图片加载超时: ${imagePath}`
                    });
                }, this.REQUEST_TIMEOUT);
            });
        }
    }

    /**
     * 带超时和重试的网络请求
     * @param {string} url - 请求URL
     * @param {Object} options - fetch配置
     * @param {number} retryCount - 当前重试次数
     * @returns {Promise<Response>} 响应结果
     */
    async fetchWithTimeout(url, options = {}, retryCount = 0) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.REQUEST_TIMEOUT);

        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            // 如果是404，直接返回，不重试
            if (response.status === 404) {
                return response;
            }

            // 如果是其他错误且还有重试次数
            if (!response.ok && retryCount < this.RETRY_ATTEMPTS) {
                const delay = Math.pow(2, retryCount) * 1000; // 指数退避
                console.log(`⚠️ 请求失败 ${response.status}，${delay}ms 后重试 (${retryCount + 1}/${this.RETRY_ATTEMPTS}): ${url}`);
                await new Promise(resolve => setTimeout(resolve, delay));
                return this.fetchWithTimeout(url, options, retryCount + 1);
            }

            return response;

        } catch (error) {
            clearTimeout(timeoutId);

            // 如果是取消操作（超时）或网络错误，且还有重试次数
            if (retryCount < this.RETRY_ATTEMPTS &&
                (error.name === 'AbortError' || error.name === 'NetworkError' || error.name === 'TypeError')) {
                const delay = Math.pow(2, retryCount) * 1000;
                console.log(`⚠️ 网络错误，${delay}ms 后重试 (${retryCount + 1}/${this.RETRY_ATTEMPTS}): ${error.message}`);
                await new Promise(resolve => setTimeout(resolve, delay));
                return this.fetchWithTimeout(url, options, retryCount + 1);
            }

            throw error;
        }
    }

    /**
     * 并发控制的批量请求处理
     * @param {Array} tasks - 任务列表
     * @param {Function} taskHandler - 任务处理函数
     * @param {number} concurrency - 并发数
     * @returns {Promise<Array>} 结果列表
     */
    async processConcurrent(tasks, taskHandler, concurrency = this.CONCURRENCY_LIMIT) {
        const results = [];
        const executing = [];

        for (let i = 0; i < tasks.length; i++) {
            const task = tasks[i];
            const promise = taskHandler(task, i).then(result => {
                // 从正在执行的列表中移除
                const index = executing.indexOf(promise);
                if (index > -1) executing.splice(index, 1);
                return result;
            });

            results.push(promise);
            executing.push(promise);

            // 当正在执行的任务达到并发限制时，等待一个完成
            if (executing.length >= concurrency) {
                await Promise.race(executing);
            }
        }

        // 等待所有任务完成
        return Promise.all(results);
    }

    /**
     * 智能探测入口：根据已知命名模式尝试通过 HEAD 探测图片是否存在
     * @param {string} directory
     * @returns {Promise<string[]|null>} 返回可访问的图片URL列表，或 null
     */
    async performIntelligentDetection(directory) {
        try {
            return await this.probeFilesByPattern(directory);
        } catch (e) {
            console.warn('智能探测失败，返回 null：', e);
            return null;
        }
    }

    /**
     * 通过 HTTP 探测找到图片目录中的图片文件
     */
    async probeFilesByPattern(directory) {
        console.log(`🔎 开始智能探测图片文件...`);

        const files = [];
        let lastFound = -1;
        let consecutiveMissing = 0;

        // 在多个扩展名和名称模式中尝试
        const extensions = this.SUPPORTED_EXTENSIONS;
        // 根据实际文件名模式更新探测模式
        const patterns = [
            'Saved Screens {index}',  // 主要模式：74个Screenshots
            'Atoms iOS {index}',      // App模式
            'Duolingo iOS {index}',
            'Fuse iOS {index}',
            'Kit iOS {index}',
            'MLS iOS {index}',
            'Rivian iOS {index}',
            'Ultrahuman iOS {index}',
            'image{index}',           // 通用模式
            'img{index}',
            'file{index}'
        ];

        for (let i = 0; i < this.MAX_PROBE_COUNT; i++) {
            let found = false;

            // 尝试每个模式和扩展名的组合
            for (const pattern of patterns) {
                for (const extension of extensions) {
                    const name = `${pattern.replace('{index}', i)}.${extension}`;
                    const url = this.buildImageUrl(directory, name);

                    try {
                        const res = await fetch(url, {
                            method: 'HEAD',
                            cache: 'no-store'
                        });

                        if (res.ok) {
                            files.push(name);
                            lastFound = i;
                            consecutiveMissing = 0;
                            found = true;
                            console.log(`✅ 找到文件: ${name}`);
                            break; // 找到后就不再尝试其他扩展名
                        }
                    } catch (e) {
                        // 忽略网络错误，继续尝试
                    }
                }

                if (found) break; // 找到后就不再尝试其他模式
            }

            if (!found) {
                consecutiveMissing++;
            }

            // 如果连续未找到文件超出阈值，停止搜索
            if (consecutiveMissing >= this.MISSING_FILE_THRESHOLD) {
                console.log(`⚠️ 连续 ${consecutiveMissing} 次未找到文件，停止探测`);
                break;
            }
        }

        if (files.length > 0) {
            console.log(`🎉 智能探测完成: 从索引 0-${lastFound} 中找到 ${files.length} 个文件`);
            return files.map(fileName => this.buildImageUrl(directory, fileName));
        } else {
            console.log(`❌ 智能探测未找到任何文件`);
            return null;
        }
    }

    /**
     * 使用AI分析图片生成标签和描述
     * @param {string} imagePath - 图片路径
     * @returns {Promise<Object>} 分析结果
     */
    async analyzeImageWithAI(imagePath) {
        try {
            // 使用统一的全局实例 openrouterAPI（在 openrouter-api.js 中定义）
            const api = window.openrouterAPI;
            if (!api || !api.apiKey) {
                console.warn('OpenRouter API未配置，使用基础分析');
                const basic = this.getBasicAnalysis(imagePath);
                const enriched = await this.enrichTagsAndDescription(imagePath, basic, null);
                return enriched;
            }
            if (!api.isVisionSupported()) {
                console.warn('当前模型不支持图像分析，使用基础分析');
                const basic = this.getBasicAnalysis(imagePath);
                const enriched = await this.enrichTagsAndDescription(imagePath, basic, null);
                return enriched;
            }

            // 构建提示：详细描述(5-10句) + 标签，强化分类引导
            await this.ensureTaxonomyLoaded();

            const taxonomy = this.taxonomy || {};
            const listToLine = (arr) => (arr || []).join('、');
            const prompt = `作为专业的UI/UX设计分析师，请深度分析这张移动应用界面截图。

返回JSON格式：{"description":"详细描述","tags":["标签1","标签2",...]}

描述要求(5-10句话)：
1. 功能描述：界面主要功能和业务场景
2. 布局分析：页面结构、信息层次和内容组织
3. 视觉风格：设计语言、色彩运用和视觉特征
4. 交互元素：按钮、输入框、导航等UI组件识别
5. 用户体验：信息传达清晰度和操作便利性评价

标签要求(10-20个中文标签)：
必须包含基础分类标签(每类至少1个)：
- APP分类: ${listToLine(taxonomy.app || [])}
- 页面类型: ${listToLine(taxonomy.page || [])}
- 控件: ${listToLine(taxonomy.control || [])}

补充标签类型：
- 主色调：蓝色、绿色、红色、黄色、紫色、橙色、粉色、黑色、白色、灰色
- 主题风格：深色主题、浅色主题、现代化、极简风格、商务风格、时尚设计
- 布局特征：卡片式布局、列表布局、网格布局、瀑布流、侧边栏、底部导航
- 功能特征：数据可视化、图表展示、地图导航、音视频播放、文件上传、支付流程

仅返回JSON，无需解释。`;

            // 将图片转为base64并调用视觉模型
            const base64Image = await api.imageUrlToBase64(imagePath);
            const responseText = await api.analyzeImage(base64Image, prompt);

            if (responseText) {
                const parsed = this.parseAIResponse(responseText);
                const enriched = await this.enrichTagsAndDescription(imagePath, parsed, null);
                return enriched;
            }
            console.warn('AI分析无响应，使用基础分析');
            const basic = this.getBasicAnalysis(imagePath);
            const enriched = await this.enrichTagsAndDescription(imagePath, basic, null);
            return enriched;

        } catch (error) {
            console.error('AI分析失败:', error);
            const basic = this.getBasicAnalysis(imagePath);
            const enriched = await this.enrichTagsAndDescription(imagePath, basic, null);
            return enriched;
        }
    }

    /**
     * 解析AI响应
     * @param {string} response - AI响应文本
     * @returns {Object} 解析后的结果
     */
    parseAIResponse(response) {
        try {
            // 1) 多种JSON解析策略
            // 策略1：完整JSON匹配
            let jsonMatch = response.trim().match(/\{[\s\S]*\}$/);
            if (!jsonMatch) {
                // 策略2：查找JSON代码块
                jsonMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
                if (jsonMatch) jsonMatch[0] = jsonMatch[1];
            }
            if (!jsonMatch) {
                // 策略3：查找任何大括号内容
                jsonMatch = response.match(/\{[\s\S]*?\}/);
            }

            if (jsonMatch) {
                try {
                    const parsed = JSON.parse(jsonMatch[0]);
                    const description = String(parsed.description || parsed.summary || parsed.desc || '').trim();
                    let tags = [];

                    // 多种tags字段名适配
                    if (Array.isArray(parsed.tags)) {
                        tags = parsed.tags;
                    } else if (Array.isArray(parsed.labels)) {
                        tags = parsed.labels;
                    } else if (Array.isArray(parsed.keywords)) {
                        tags = parsed.keywords;
                    } else if (typeof parsed.tags === 'string') {
                        tags = parsed.tags.split(/[，,、]/).map(t => t.trim()).filter(Boolean);
                    }

                    tags = tags.map(t => String(t).trim()).filter(Boolean);

                    // 验证描述长度(5-10句话要求)
                    const sentenceCount = (description.match(/[。！？.!?]/g) || []).length;
                    const isValidDescription = description.length >= 50 && sentenceCount >= 3;

                    if ((isValidDescription || description.length > 0) && tags.length > 0) {
                        return {
                            description: isValidDescription ? description : this.enhanceDescription(description),
                            tags: tags.slice(0, 20) // 限制最多20个标签
                        };
                    }
                } catch (parseError) {
                    console.warn('JSON解析失败，尝试其他策略:', parseError.message);
                }
            }

            // 2) 结构化文本解析策略
            const structuredResult = this.parseStructuredText(response);
            if (structuredResult.description || structuredResult.tags.length > 0) {
                return structuredResult;
            }

            // 3) 兜底策略：将整个响应作为描述处理
            const fallbackDesc = this.enhanceDescription(response.trim());
            return {
                description: fallbackDesc,
                tags: ['移动应用', 'UI设计', '界面分析']
            };

        } catch (error) {
            console.error('AI响应解析失败:', error);
            return {
                description: '无法分析此图片内容，请检查图片格式或重试。',
                tags: ['移动应用', 'UI设计', '解析失败']
            };
        }
    }

    /**
     * 解析结构化文本格式的响应
     */
    parseStructuredText(response) {
        const lines = response.split('\n').map(line => line.trim()).filter(Boolean);
        let description = '';
        let tags = [];

        for (const line of lines) {
            // 描述相关关键词
            if (line.match(/^(描述|说明|分析|功能)[:：]/)) {
                description = line.replace(/^[^:：]*[:：]/, '').trim();
            }
            // 标签相关关键词
            else if (line.match(/^(标签|关键词|分类|特征)[:：]/)) {
                const tagStr = line.replace(/^[^:：]*[:：]/, '').trim();
                tags = tagStr.split(/[，,、]/).map(tag => tag.trim()).filter(Boolean);
            }
            // 如果没有明确标识，较长的行作为描述
            else if (line.length > 20 && !description) {
                description = line;
            }
        }

        return {
            description: description || response.trim(),
            tags: tags.length > 0 ? tags : []
        };
    }

    /**
     * 增强描述内容，确保符合5-10句话要求
     */
    enhanceDescription(originalDesc) {
        if (!originalDesc || originalDesc.length < 20) {
            return '该移动应用界面采用现代化设计语言，布局层次清晰，色彩搭配协调。界面功能明确，交互元素设计合理，符合用户操作习惯。整体视觉效果专业美观，信息传达高效，用户体验友好。页面结构组织清晰，内容层次分明，便于用户快速获取信息。交互设计遵循移动端最佳实践，提供流畅的操作体验。';
        }

        const sentenceCount = (originalDesc.match(/[。！？.!?]/g) || []).length;

        if (sentenceCount >= 5) {
            return originalDesc;
        }

        // 补充描述内容以达到至少5句话的要求
        const supplements = [
            '界面设计遵循移动端设计规范，保证良好的用户体验。',
            '色彩运用恰当，视觉层次分明，信息组织清晰有序。',
            '交互元素布局合理，操作流程顺畅，符合用户使用习惯。',
            '页面内容结构化程度高，信息传达直观明确。',
            '整体设计风格统一，品牌识别度强，视觉体验优秀。'
        ];

        let enhanced = originalDesc.endsWith('。') ? originalDesc : originalDesc + '。';
        let currentCount = sentenceCount;

        for (const supplement of supplements) {
            if (currentCount >= 5) break;
            enhanced += supplement;
            currentCount++;
        }

        // 如果仍然不够5句话，继续添加更多补充内容
        if (currentCount < 5) {
            const additionalSupplements = [
                '功能模块划分清晰，操作路径简洁明了。',
                '视觉焦点突出，重要信息得到有效强调。',
                '响应式设计良好，适配不同屏幕尺寸需求。'
            ];

            for (const supplement of additionalSupplements) {
                if (currentCount >= 5) break;
                enhanced += supplement;
                currentCount++;
            }
        }

        return enhanced;
    }

    /**
     * 载入基础标签分类（从 分类/标签分类明细.md 解析）
     */
    async ensureTaxonomyLoaded() {
        if (this.taxonomy) return;
        try {
            const res = await fetch('分类/标签分类明细.md', { cache: 'no-cache' });
            if (!res.ok) throw new Error('fetch taxonomy failed');
            const md = await res.text();
            const parseSection = (title) => {
                const start = md.indexOf(`## ${title}`);
                if (start === -1) return [];
                const rest = md.slice(start);
                const nextIdx = rest.indexOf('\n\n## ');
                const block = nextIdx === -1 ? rest : rest.slice(0, nextIdx);
                return (block.match(/\*\s+.+/g) || []).map(line => line.replace(/\*\s+/, '').trim());
            };
            this.taxonomy = {
                app: parseSection('APP分类'),
                page: parseSection('页面类型'),
                control: parseSection('控件')
            };
        } catch (e) {
            console.warn('加载基础标签分类失败:', e);
            this.taxonomy = { app: [], page: [], control: [] };
        }
    }

    /**
     * 基于分类与颜色等对描述/标签进行增强，确保符合5-10句描述和高质量标签要求
     */
    async enrichTagsAndDescription(imagePath, { description = '', tags = [] }, fileOrBlob = null) {
        await this.ensureTaxonomyLoaded();

        // 验证和增强描述质量
        const enhancedDescription = this.validateAndEnhanceDescription(description);

        // 去重、清洗标签
        const tagSet = new Set();
        tags.map(t => String(t).trim()).filter(Boolean).forEach(tag => tagSet.add(tag));

        // 颜色分析增强（当提供文件对象时，优先使用文件以避免HTTP 404）
        try {
            const colorTags = await this.extractColorTags(imagePath, fileOrBlob);
            colorTags.forEach(tag => tagSet.add(tag));
        } catch (error) {
            console.warn('颜色提取失败:', error);
        }

        // 基础分类智能匹配
        const classificationTags = this.getSmartClassificationTags(enhancedDescription, Array.from(tagSet), imagePath);
        classificationTags.forEach(tag => tagSet.add(tag));

        // 确保三大分类体系的覆盖
        this.ensureBasicCategoryCoverage(tagSet);

        // 智能补充高质量标签
        this.addIntelligentSupplementTags(tagSet, enhancedDescription, imagePath);

        // 标签质量优化和排序
        const finalTags = this.optimizeAndSortTags(Array.from(tagSet));

        return {
            description: enhancedDescription,
            tags: finalTags.slice(0, 20)  // 控制在20个以内
        };
    }

    /**
     * 生成图片预览（dataURL，缩放到 maxSize）
     */
    async createPreviewFromFile(file, maxSize = 120) {
        return new Promise((resolve, reject) => {
            try {
                const img = new Image();
                const url = URL.createObjectURL(file);
                img.onload = () => {
                    try {
                        const w = img.width;
                        const h = img.height;
                        const scale = Math.min(1, maxSize / Math.max(w, h));
                        const cw = Math.max(1, Math.round(w * scale));
                        const ch = Math.max(1, Math.round(h * scale));
                        const canvas = document.createElement('canvas');
                        canvas.width = cw;
                        canvas.height = ch;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0, cw, ch);
                        const dataUrl = canvas.toDataURL('image/jpeg', 0.75);
                        resolve(dataUrl);
                    } finally {
                        URL.revokeObjectURL(url);
                    }
                };
                img.onerror = (e) => { URL.revokeObjectURL(url); reject(e); };
                img.src = url;
            } catch (e) { reject(e); }
        });
    }

    /**
     * 验证并增强描述质量
     */
    validateAndEnhanceDescription(description) {
        if (!description || description.length < 30) {
            return '该移动应用界面采用现代化设计语言，布局层次清晰，功能分区明确。界面元素排布合理，色彩搭配协调，符合移动端交互设计规范。整体视觉效果简洁专业，用户体验流畅，信息传达清晰有效。交互元素设计合理，操作便利性良好，充分考虑用户使用习惯。';
        }

        // 检查句子数量
        const sentenceCount = (description.match(/[。！？.!?]/g) || []).length;

        if (sentenceCount >= 5) {
            return description;
        }

        // 根据描述内容智能补充，确保达到5句话以上
        return this.enhanceDescription(description);
    }

    /**
     * 智能获取分类标签
     */
    getSmartClassificationTags(description, existingTags, imagePath) {
        const classificationTags = [];

        // 使用分类引擎进行智能分类
        if (window.imageClassifier) {
            try {
                const contentClassification = window.imageClassifier.classifyImage({
                    tags: existingTags,
                    description
                });
                const fileClassification = window.imageClassifier.classifyByFilename(imagePath);
                const mergedClassification = window.imageClassifier.mergeClassifications(
                    contentClassification,
                    fileClassification
                );

                // 获取高置信度的分类结果
                const addTopCategories = (categories, maxCount = 2) => {
                    if (!categories || !Array.isArray(categories)) return;
                    categories.slice(0, maxCount)
                        .filter(item => item.confidence > 0.3) // 只选择高置信度的
                        .forEach(item => classificationTags.push(item.category));
                };

                addTopCategories(mergedClassification.appCategories, 2);
                addTopCategories(mergedClassification.pageTypes, 2);
                addTopCategories(mergedClassification.controlTypes, 3);

            } catch (error) {
                console.warn('智能分类失败，使用基础分类:', error);
            }
        }

        return classificationTags;
    }

    /**
     * 确保基础分类体系的覆盖
     */
    ensureBasicCategoryCoverage(tagSet) {
        const taxonomy = this.taxonomy || {};

        // 检查每个分类体系是否有标签
        const checkAndAdd = (categoryList, categoryName) => {
            if (!categoryList || categoryList.length === 0) return;

            const hasCategory = categoryList.some(cat => tagSet.has(cat));
            if (!hasCategory) {
                // 优先选择常见的分类
                const commonCategories = {
                    app: ['效率', '社交网络', '生活方式', '健康与健身'],
                    page: ['主界面', '设置与偏好', '账户管理', '操作选项'],
                    control: ['按钮', '文本字段', '卡片', '标签']
                };

                const candidates = commonCategories[categoryName] || [];
                const candidate = candidates.find(cat => categoryList.includes(cat)) || categoryList[0];
                tagSet.add(candidate);
            }
        };

        checkAndAdd(taxonomy.app, 'app');
        checkAndAdd(taxonomy.page, 'page');
        checkAndAdd(taxonomy.control, 'control');
    }

    /**
     * 智能补充高质量标签
     */
    addIntelligentSupplementTags(tagSet, description, imagePath) {
        // 基于描述内容的语义分析补充标签
        const semanticTags = this.extractSemanticTags(description);
        semanticTags.forEach(tag => tagSet.add(tag));

        // 基于文件名的智能推断
        const filenameTags = this.extractFilenameInsights(imagePath);
        filenameTags.forEach(tag => tagSet.add(tag));

        // 补充设计相关标签（如果数量不够）
        if (tagSet.size < 10) {
            const designTags = [
                '现代化设计', '用户体验', '交互设计', '信息架构',
                '视觉层次', '功能导向', '移动优先', '简洁明了'
            ];

            for (const tag of designTags) {
                if (tagSet.size >= 15) break;
                tagSet.add(tag);
            }
        }
    }

    /**
     * 从描述中提取语义标签
     */
    extractSemanticTags(description) {
        const semanticTags = [];
        const lowerDesc = description.toLowerCase();

        // 功能关键词映射
        const functionKeywords = {
            '登录|注册|验证': '用户认证',
            '支付|付款|交易': '支付功能',
            '聊天|消息|对话': '即时通讯',
            '地图|导航|位置': '地理服务',
            '音乐|播放|音频': '多媒体',
            '视频|播放器|影像': '视频播放',
            '健康|运动|健身': '健康监测',
            '购物|商品|订单': '电商功能',
            '社交|分享|关注': '社交互动',
            '学习|教育|课程': '在线教育'
        };

        for (const [keywords, tag] of Object.entries(functionKeywords)) {
            if (new RegExp(keywords).test(lowerDesc)) {
                semanticTags.push(tag);
            }
        }

        // 设计风格关键词
        const styleKeywords = {
            '现代|时尚|潮流': '现代风格',
            '简洁|简单|极简': '极简风格',
            '商务|专业|正式': '商务风格',
            '可爱|活泼|有趣': '轻松风格',
            '高端|奢华|精致': '高端设计'
        };

        for (const [keywords, tag] of Object.entries(styleKeywords)) {
            if (new RegExp(keywords).test(lowerDesc)) {
                semanticTags.push(tag);
            }
        }

        return semanticTags.slice(0, 5); // 限制数量
    }

    /**
     * 从文件名提取洞察标签
     */
    extractFilenameInsights(imagePath) {
        const filename = imagePath.split('/').pop().toLowerCase();
        const insights = [];

        // 品牌/应用识别
        const brandMapping = {
            'duolingo': ['教育应用', '语言学习', '游戏化学习'],
            'fuse': ['金融应用', '移动支付', '投资理财'],
            'ultrahuman': ['健康应用', '生活方式', '数据追踪'],
            'rivian': ['汽车应用', '智能出行', '绿色科技'],
            'atoms': ['习惯养成', '个人成长', '目标管理'],
            'mls': ['体育应用', '赛事资讯', '球迷社区'],
            'kit': ['开发工具', '设计系统', '组件库']
        };

        for (const [brand, tags] of Object.entries(brandMapping)) {
            if (filename.includes(brand)) {
                insights.push(...tags);
                break;
            }
        }

        return insights.slice(0, 3);
    }

    /**
     * 优化和排序标签
     */
    optimizeAndSortTags(tags) {
        // 标签重要性权重
        const tagWeights = {
            // 基础分类权重最高
            app: 10,
            page: 9,
            control: 8,
            // 颜色和主题
            color: 7,
            theme: 6,
            // 功能特征
            function: 5,
            // 设计风格
            style: 4,
            // 其他
            other: 3
        };

        // 分类标签并赋权重
        const categorizedTags = tags.map(tag => {
            let weight = tagWeights.other;
            let category = 'other';

            // 判断标签类型
            if (this.taxonomy.app && this.taxonomy.app.includes(tag)) {
                weight = tagWeights.app;
                category = 'app';
            } else if (this.taxonomy.page && this.taxonomy.page.includes(tag)) {
                weight = tagWeights.page;
                category = 'page';
            } else if (this.taxonomy.control && this.taxonomy.control.includes(tag)) {
                weight = tagWeights.control;
                category = 'control';
            } else if (tag.includes('色') || tag.includes('主题')) {
                weight = tagWeights.color;
                category = 'color';
            } else if (tag.includes('功能') || tag.includes('支付') || tag.includes('导航')) {
                weight = tagWeights.function;
                category = 'function';
            }

            return { tag, weight, category };
        });

        // 按权重排序，确保重要标签在前
        return categorizedTags
            .sort((a, b) => b.weight - a.weight)
            .map(item => item.tag);
    }

    /**
     * 提取图片主色/主题色并映射中文颜色标签
     */
    async extractColorTags(imagePath, fileOrBlob = null) {
        return new Promise((resolve) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';

            let objectUrl = null;
            img.onload = () => {
                try {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    const w = canvas.width = Math.min(64, img.width);
                    const h = canvas.height = Math.min(64, img.height);
                    ctx.drawImage(img, 0, 0, w, h);
                    const data = ctx.getImageData(0, 0, w, h).data;

                    // 简单采样
                    const freq = new Map();
                    for (let i = 0; i < data.length; i += 4 * 4) { // 步长取样
                        const r = data[i], g = data[i + 1], b = data[i + 2];
                        const hsv = this.rgbToHsv(r, g, b);
                        const name = this.colorNameFromHsv(hsv[0], hsv[1], hsv[2]);
                        const key = name;
                        freq.set(key, (freq.get(key) || 0) + 1);
                    }
                    const sorted = Array.from(freq.entries()).sort((a, b) => b[1] - a[1]);
                    const top = sorted.slice(0, 3).map(([name]) => name).filter(Boolean);

                    // 亮度判断
                    const avgLuma = (() => {
                        let sum = 0, count = 0;
                        for (let i = 0; i < data.length; i += 4 * 8) {
                            const r = data[i], g = data[i + 1], b = data[i + 2];
                            sum += 0.2126 * r + 0.7152 * g + 0.0722 * b;
                            count++;
                        }
                        return sum / Math.max(count, 1);
                    })();
                    const theme = avgLuma < 110 ? '深色主题' : '浅色主题';
                    resolve([...top, theme]);
                } finally {
                    if (objectUrl) URL.revokeObjectURL(objectUrl);
                }
            };
            img.onerror = () => {
                if (objectUrl) URL.revokeObjectURL(objectUrl);
                resolve([]);
            };

            if (fileOrBlob instanceof Blob) {
                objectUrl = URL.createObjectURL(fileOrBlob);
                img.src = objectUrl;
            } else {
                img.src = imagePath + (imagePath.includes('?') ? '&' : '?') + 't=' + Date.now();
            }
        });
    }

    rgbToHsv(r, g, b) {
        r /= 255; g /= 255; b /= 255;
        const max = Math.max(r, g, b), min = Math.min(r, g, b);
        let h, s, v = max;
        const d = max - min;
        s = max === 0 ? 0 : d / max;
        if (max === min) {
            h = 0;
        } else {
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }
        return [h, s, v];
    }

    colorNameFromHsv(h, s, v) {
        // h: 0..1, s:0..1, v:0..1
        if (v < 0.2) return '黑色';
        if (s < 0.12) return v > 0.9 ? '白色' : '灰色';
        const deg = h * 360;
        if (deg < 15 || deg >= 345) return '红色';
        if (deg < 45) return '橙色';
        if (deg < 70) return '黄色';
        if (deg < 160) return '绿色';
        if (deg < 200) return '青色';
        if (deg < 255) return '蓝色';
        if (deg < 290) return '紫色';
        if (deg < 330) return '粉色';
        return '蓝色';
    }

    /**
     * 获取基础分析（基于文件名的简单分析）
     * @param {string} imagePath - 图片路径
     * @returns {Object} 基础分析结果
     */
    getBasicAnalysis(imagePath) {
        const fileName = imagePath.split('/').pop().toLowerCase();

        if (fileName.includes('mls')) {
            return {
                description: '这是一个体育应用界面，展示了比赛相关的信息和功能。',
                tags: ['体育应用', 'MLS', '界面设计', '移动应用']
            };
        } else if (fileName.includes('duolingo')) {
            return {
                description: '这是语言学习应用的界面，展示了学习内容和进度。',
                tags: ['语言学习', '教育应用', '用户界面', '移动应用']
            };
        } else if (fileName.includes('fuse')) {
            return {
                description: '这是金融应用的界面，涉及账户管理和交易功能。',
                tags: ['金融应用', '移动支付', '用户界面', '移动应用']
            };
        } else if (fileName.includes('ultrahuman')) {
            return {
                description: '这是健康监测应用的界面，展示了健康数据和指标。',
                tags: ['健康应用', '数据监测', '用户界面', '移动应用']
            };
        } else if (fileName.includes('atoms')) {
            return {
                description: '这是习惯追踪应用的界面，帮助用户管理日常习惯。',
                tags: ['习惯追踪', '生活方式', '用户界面', '移动应用']
            };
        } else if (fileName.includes('rivian')) {
            return {
                description: '这是汽车相关应用的界面，涉及车辆管理功能。',
                tags: ['汽车应用', '车辆管理', '用户界面', '移动应用']
            };
        } else if (fileName.includes('kit')) {
            return {
                description: '这是移动应用的界面设计，展示了现代化的用户体验。',
                tags: ['UI设计', '移动应用', '用户界面', '现代设计']
            };
        }

        return {
            description: '这是移动应用的界面截图，展示了应用的功能和设计。',
            tags: ['移动应用', 'UI设计', '用户界面', '应用截图']
        };
    }

    /**
     * 索引单个图片
     * @param {string} imagePath - 图片路径
     * @returns {Promise<Object>} 索引结果
     */
    async indexImage(imagePath, options = {}) {
        try {
            // 检查图片是否存在
            const existsResult = await this.checkImageExists(imagePath);
            if (!existsResult.exists) {
                throw new Error(existsResult.error || `图片不存在: ${imagePath}`);
            }

            // 检查是否已经索引过，强化缓存检查
            const already = window.imagesMetadata && window.imagesMetadata.getImage(imagePath);
            const forceRebuild = !!options.forceRebuild;

            if (already && !forceRebuild) {
                // 检查缓存的完整性：必须有描述、标签和分类信息
                const isCompleteCache = already.description &&
                                       already.tags &&
                                       already.tags.length > 0 &&
                                       already.classifications;

                if (isCompleteCache) {
                    console.log(`✅ 图片已完整索引，跳过分析: ${imagePath}`);
                    this.indexedImages.add(imagePath);
                    return {
                        success: true,
                        updated: false,
                        cached: true,
                        analysis: {
                            description: already.description,
                            tags: already.tags
                        }
                    };
                } else {
                    console.log(`⚠️ 图片缓存不完整，需要重新分析: ${imagePath}`);
                }
            }

            console.log(`🔍 开始AI分析图片: ${imagePath}`);

            // 分析图片
            const analysis = await this.analyzeImageWithAI(imagePath);

            // 添加到元数据管理器
            if (window.imagesMetadata) {
                if (already && (forceRebuild || !already.classifications)) {
                    window.imagesMetadata.updateImage(imagePath, {
                        description: analysis.description,
                        tags: analysis.tags
                    });
                    console.log(`✏️ 更新图片元数据: ${imagePath}`);
                } else {
                    window.imagesMetadata.addImage({
                        path: imagePath,
                        description: analysis.description,
                        tags: analysis.tags
                    });
                    console.log(`➕ 添加新图片元数据: ${imagePath}`);
                }
            }

            this.indexedImages.add(imagePath);

            return {
                success: true,
                updated: true,
                cached: false,
                analysis
            };

        } catch (error) {
            console.error(`❌ 索引图片失败 ${imagePath}:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 批量索引图片（支持并发控制和错误重试）
     * @param {Array} imagePaths - 图片路径列表
     * @param {Object} options - 索引配置
     * @returns {Promise<Object>} 索引结果统计
     */
    async batchIndexImages(imagePaths, options = {}) {
        const startTime = Date.now();
        const results = {
            total: imagePaths.length,
            success: 0,
            skipped: 0,
            cached: 0,
            errors: 0,
            missing: 0,
            retries: 0,
            duration: 0,
            errorDetails: [],
            missingFiles: [],
            currentFile: ''
        };

        this.indexingProgress = {
            total: imagePaths.length,
            processed: 0,
            errors: 0,
            missing: 0,
            currentFile: '',
            isIndexing: true
        };

        console.log(`📋 开始批量索引 ${imagePaths.length} 张图片（并发数: ${this.CONCURRENCY_LIMIT}）...`);

        // 使用并发控制批量处理
        await this.processConcurrent(imagePaths, async (imagePath, index) => {
            let processResult = { success: false, error: null };

            try {
                // 更新当前处理文件
                results.currentFile = imagePath.split('/').pop();
                this.indexingProgress.currentFile = results.currentFile;

                const result = await this.indexImage(imagePath, options);
                processResult = result;

                if (result.success) {
                    if (result.cached) {
                        results.cached++;
                        console.log(`🔄 [${index + 1}/${imagePaths.length}] 使用缓存: ${results.currentFile}`);
                    } else if (result.updated) {
                        results.success++;
                        console.log(`✨ [${index + 1}/${imagePaths.length}] 分析完成: ${results.currentFile}`);
                    } else {
                        results.skipped++;
                    }
                } else {
                    // 区分缺失文件和其他错误
                    if (result.error && result.error.includes('404') ||
                        result.error && result.error.includes('不存在')) {
                        results.missing++;
                        results.missingFiles.push(imagePath);
                        console.log(`⚠️ [${index + 1}/${imagePaths.length}] 文件缺失: ${results.currentFile}`);
                    } else {
                        results.errors++;
                        results.errorDetails.push({
                            path: imagePath,
                            error: result.error,
                            index: index
                        });
                        console.log(`❌ [${index + 1}/${imagePaths.length}] 失败: ${results.currentFile} - ${result.error}`);
                    }
                }

            } catch (error) {
                processResult = { success: false, error: error.message };
                results.errors++;
                results.errorDetails.push({
                    path: imagePath,
                    error: error.message,
                    index: index
                });
                console.log(`❌ [${index + 1}/${imagePaths.length}] 异常: ${imagePath.split('/').pop()} - ${error.message}`);
            }

            // 更新进度
            this.indexingProgress.processed++;
            this.indexingProgress.errors = results.errors;
            this.indexingProgress.missing = results.missing;

            if (this.onProgressUpdate) {
                this.onProgressUpdate({
                    ...this.indexingProgress,
                    currentFile: results.currentFile
                });
            }

            return processResult;
        }, this.CONCURRENCY_LIMIT);

        this.indexingProgress.isIndexing = false;
        results.duration = Date.now() - startTime;
        results.currentFile = '';

        // 输出统计结果
        console.log(`📊 批量索引完成（耗时: ${Math.round(results.duration / 1000)}s）:
            总数: ${results.total}
            新分析: ${results.success}
            使用缓存: ${results.cached}
            跳过: ${results.skipped}
            文件缺失: ${results.missing}
            错误: ${results.errors}`);

        if (results.missing > 0) {
            console.log(`📝 缺失文件数量: ${results.missing}，可考虑重新生成manifest或清理无效文件引用`);
        }

        if (results.errors > 0) {
            console.log(`⚠️ 错误详情:`, results.errorDetails.slice(0, 5)); // 显示前5个错误
        }

        // 确保持久化
        if (window.imagesMetadata && typeof window.imagesMetadata.saveToStorage === 'function') {
            try { await window.imagesMetadata.saveToStorage(); } catch (_) {}
        }

        if (this.onIndexComplete) {
            this.onIndexComplete(results);
        }

        return results;
    }

    /**
     * 开始完整索引流程
     * @param {string} directory - 图片目录
     * @returns {Promise<Object>} 索引结果
     */
    async startIndexing(directory = '示例图片/') {
        try {
            console.log('开始扫描图片目录...');
            const allImages = await this.scanDirectory(directory);

            console.log(`发现 ${allImages.length} 张图片`);

            // 过滤出需要索引的图片
            const unindexedImages = window.imagesMetadata ?
                window.imagesMetadata.getUnindexedImages(allImages) :
                allImages;

            console.log(`需要索引 ${unindexedImages.length} 张图片`);

            if (unindexedImages.length === 0) {
                // 即使没有新图片需要索引，也要检查现有图片并统计缓存使用情况
                console.log('📋 所有图片都已索引，验证缓存完整性...');

                // 验证所有图片的缓存
                const allValidated = await this.batchIndexImages(allImages, { skipIfCached: true });

                const results = {
                    total: allImages.length,
                    success: allValidated.success,
                    cached: allValidated.cached,
                    skipped: allValidated.skipped,
                    errors: allValidated.errors,
                    message: allImages.length > 0 ? '所有图片已完整索引' : '目录中没有发现图片'
                };

                console.log('✅ 索引验证完成，所有图片都已缓存');
                return results;
            }

            // 开始批量索引
            const results = await this.batchIndexImages(unindexedImages);

            console.log('索引完成:', results);
            return results;

        } catch (error) {
            console.error('索引流程失败:', error);
            this.indexingProgress.isIndexing = false;
            throw error;
        }
    }

    /**
     * 从用户选择的文件列表开始索引
     * @param {FileList} fileList - 用户选择的文件列表
     * @returns {Promise<Object>} 索引结果
     */
    async startIndexingFromFiles(fileList) {
        try {
            console.log('开始处理用户选择的文件...');

            // 转换 FileList 为图片文件映射
            const fileMap = this.createFileMapFromFileList(fileList);
            const imagePaths = Array.from(fileMap.keys());
            console.log(`从选择的文件中发现 ${imagePaths.length} 张图片`);

            if (imagePaths.length === 0) {
                const results = {
                    total: 0,
                    success: 0,
                    cached: 0,
                    skipped: 0,
                    errors: 0,
                    message: '选择的文件夹中没有发现图片文件'
                };
                return results;
            }

            // 过滤出需要索引的图片
            const unindexedImages = window.imagesMetadata ?
                window.imagesMetadata.getUnindexedImages(imagePaths) :
                imagePaths;

            console.log(`需要索引 ${unindexedImages.length} 张图片`);

            // 设置文件映射供索引使用
            this.userFileMap = fileMap;

            // 开始批量索引
            const results = await this.batchIndexImagesFromFiles(unindexedImages, fileMap);
            console.log('文件索引完成:', results);

            // 清理文件映射
            this.userFileMap = null;
            return results;

        } catch (error) {
            console.error('文件索引流程失败:', error);
            this.indexingProgress.isIndexing = false;
            this.userFileMap = null;
            throw error;
        }
    }

    /**
     * 从用户选择的文件列表重建索引
     * @param {FileList} fileList - 用户选择的文件列表
     * @returns {Promise<Object>} 索引结果
     */
    async rebuildIndexingFromFiles(fileList) {
        try {
            console.log('开始重建用户选择文件的索引...');

            // 转换 FileList 为图片文件映射
            const fileMap = this.createFileMapFromFileList(fileList);
            const imagePaths = Array.from(fileMap.keys());
            console.log(`从选择的文件中发现 ${imagePaths.length} 张图片`);

            if (imagePaths.length === 0) {
                const results = {
                    total: 0,
                    success: 0,
                    cached: 0,
                    skipped: 0,
                    errors: 0,
                    message: '选择的文件夹中没有发现图片文件'
                };
                return results;
            }

            // 设置文件映射供索引使用
            this.userFileMap = fileMap;

            // 重建所有图片索引（强制重新处理）
            const results = await this.batchIndexImagesFromFiles(imagePaths, fileMap, { forceRebuild: true });
            console.log('文件重建索引完成:', results);

            // 清理文件映射
            this.userFileMap = null;
            return results;

        } catch (error) {
            console.error('文件重建索引失败:', error);
            this.indexingProgress.isIndexing = false;
            this.userFileMap = null;
            throw error;
        }
    }

    /**
     * 从 FileList 提取图片文件路径
     * @param {FileList} fileList - 文件列表
     * @returns {Array<string>} 图片路径数组
     */
    extractImagePathsFromFiles(fileList) {
        const imagePaths = [];

        for (let i = 0; i < fileList.length; i++) {
            const file = fileList[i];

            // 检查是否为图片文件
            if (file.type.startsWith('image/')) {
                // 使用相对路径，保持与现有系统一致
                const relativePath = file.webkitRelativePath || file.name;
                imagePaths.push(relativePath);
            }
        }

        return imagePaths;
    }

    /**
     * 从 FileList 创建文件路径到 File 对象的映射
     * @param {FileList} fileList - 文件列表
     * @returns {Map<string, File>} 路径到文件对象的映射
     */
    createFileMapFromFileList(fileList) {
        const fileMap = new Map();

        for (let i = 0; i < fileList.length; i++) {
            const file = fileList[i];

            // 检查是否为图片文件
            if (file.type.startsWith('image/')) {
                // 使用相对路径，保持与现有系统一致
                const relativePath = file.webkitRelativePath || file.name;
                fileMap.set(relativePath, file);
            }
        }

        return fileMap;
    }

    /**
     * 批量索引用户选择的图片文件
     * @param {Array<string>} imagePaths - 图片路径列表
     * @param {Map<string, File>} fileMap - 文件映射
     * @param {Object} options - 索引配置
     * @returns {Promise<Object>} 索引结果统计
     */
    async batchIndexImagesFromFiles(imagePaths, fileMap, options = {}) {
        const startTime = Date.now();
        const results = {
            total: imagePaths.length,
            success: 0,
            skipped: 0,
            cached: 0,
            errors: 0,
            missing: 0,
            retries: 0,
            duration: 0,
            errorDetails: [],
            missingFiles: [],
            currentFile: ''
        };

        this.indexingProgress = {
            total: imagePaths.length,
            processed: 0,
            errors: 0,
            missing: 0,
            currentFile: '',
            isIndexing: true
        };

        console.log(`📋 开始批量索引 ${imagePaths.length} 张用户文件（并发数: ${this.CONCURRENCY_LIMIT}）...`);

        // 使用并发控制批量处理
        await this.processConcurrent(imagePaths, async (imagePath, index) => {
            let processResult = { success: false, error: null };

            try {
                // 更新当前处理文件
                results.currentFile = imagePath.split('/').pop();
                this.indexingProgress.currentFile = results.currentFile;

                const file = fileMap.get(imagePath);
                if (!file) {
                    throw new Error(`文件不存在于选择的文件列表中: ${imagePath}`);
                }

                const result = await this.indexImageFromFile(imagePath, file, options);
                processResult = result;

                if (result.success) {
                    if (result.cached) {
                        results.cached++;
                        console.log(`🔄 [${index + 1}/${imagePaths.length}] 使用缓存: ${results.currentFile}`);
                    } else if (result.updated) {
                        results.success++;
                        console.log(`✨ [${index + 1}/${imagePaths.length}] 分析完成: ${results.currentFile}`);
                    } else {
                        results.skipped++;
                    }
                } else {
                    results.errors++;
                    results.errorDetails.push({
                        path: imagePath,
                        error: result.error,
                        index: index
                    });
                    console.log(`❌ [${index + 1}/${imagePaths.length}] 失败: ${results.currentFile} - ${result.error}`);
                }

            } catch (error) {
                processResult = { success: false, error: error.message };
                results.errors++;
                results.errorDetails.push({
                    path: imagePath,
                    error: error.message,
                    index: index
                });
                console.log(`❌ [${index + 1}/${imagePaths.length}] 异常: ${imagePath.split('/').pop()} - ${error.message}`);
            }

            // 更新进度
            this.indexingProgress.processed++;
            this.indexingProgress.errors = results.errors;
            this.indexingProgress.missing = results.missing;

            if (this.onProgressUpdate) {
                this.onProgressUpdate({
                    ...this.indexingProgress,
                    currentFile: results.currentFile
                });
            }

            return processResult;
        }, this.CONCURRENCY_LIMIT);

        this.indexingProgress.isIndexing = false;
        results.duration = Date.now() - startTime;
        results.currentFile = '';

        console.log(`✅ 用户文件索引完成! 耗时: ${results.duration}ms`);
        console.log(`📊 统计: 总计=${results.total}, 成功=${results.success}, 缓存=${results.cached}, 跳过=${results.skipped}, 错误=${results.errors}`);

        // 确保所有更改已持久化
        if (window.imagesMetadata && typeof window.imagesMetadata.saveToStorage === 'function') {
            try { await window.imagesMetadata.saveToStorage(); } catch (_) {}
        }

        if (this.onIndexComplete) {
            this.onIndexComplete(results);
        }

        return results;
    }

    /**
     * 索引单个用户文件
     * @param {string} imagePath - 图片路径
     * @param {File} file - 文件对象
     * @param {Object} options - 配置选项
     * @returns {Promise<Object>} 索引结果
     */
    async indexImageFromFile(imagePath, file, options = {}) {
        try {
            // 检查是否已经索引过，强化缓存检查
            const already = window.imagesMetadata && window.imagesMetadata.getImage(imagePath);
            const forceRebuild = !!options.forceRebuild;

            if (already && !forceRebuild) {
                // 检查缓存的完整性：必须有描述、标签和分类信息
                const isCompleteCache = already.description &&
                                       already.tags &&
                                       already.tags.length > 0 &&
                                       already.classifications;

                if (isCompleteCache) {
                    console.log(`✅ 图片已完整索引，跳过分析: ${imagePath}`);
                    this.indexedImages.add(imagePath);
                    return {
                        success: true,
                        updated: false,
                        cached: true,
                        analysis: {
                            description: already.description,
                            tags: already.tags
                        }
                    };
                }
            }

            // 直接使用 File 对象进行 AI 分析
            const analysis = await this.analyzeImageFromFile(file, imagePath);

            if (!analysis) {
                throw new Error('AI 分析返回空结果');
            }

            // 使用图片分类器进行分类（按 ImageClassifier 的签名传入对象）
            const classifications = window.imageClassifier ?
                window.imageClassifier.classifyImage({ tags: analysis.tags || [], description: analysis.description || '' }) :
                null;

            // 生成小尺寸缩略图（dataURL），用于表格/首页预览且避免404
            const preview = await (async () => {
                try {
                    const img = await this.createPreviewFromFile(file, 120);
                    return img;
                } catch (_) { return null; }
            })();

            // 保存到元数据管理器
            if (window.imagesMetadata) {
                const imageData = {
                    path: imagePath,
                    description: analysis.description,
                    tags: analysis.tags,
                    classifications: classifications,
                    preview: preview || undefined,
                    lastUpdated: new Date().toISOString(),
                    indexed: true
                };

                window.imagesMetadata.addImage(imageData);
            }

            this.indexedImages.add(imagePath);

            return {
                success: true,
                updated: true,
                cached: false,
                analysis: analysis,
                classifications: classifications
            };

        } catch (error) {
            console.error(`❌ 索引图片失败 ${imagePath}:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 分析用户上传的图片文件
     * @param {File} file - 图片文件对象
     * @param {string} imagePath - 图片路径（用于日志）
     * @returns {Promise<Object>} 分析结果
     */
    async analyzeImageFromFile(file, imagePath) {
        try {
            // 使用统一的全局实例 openrouterAPI
            const api = window.openrouterAPI;
            if (!api || !api.apiKey) {
                console.warn('OpenRouter API未配置，使用基础分析');
                const basic = this.getBasicAnalysis(imagePath);
                const enriched = await this.enrichTagsAndDescription(imagePath, basic, file);
                return enriched;
            }
            if (!api.isVisionSupported()) {
                console.warn('当前模型不支持图像分析，使用基础分析');
                const basic = this.getBasicAnalysis(imagePath);
                const enriched = await this.enrichTagsAndDescription(imagePath, basic, file);
                return enriched;
            }

            // 构建提示：详细描述(5-10句) + 标签，强化分类引导
            await this.ensureTaxonomyLoaded();

            const taxonomy = this.taxonomy || {};
            const listToLine = (arr) => (arr || []).join('、');
            const prompt = `作为专业的UI/UX设计分析师，请深度分析这张移动应用界面截图。

返回JSON格式：{"description":"详细描述","tags":["标签1","标签2",...]}

描述要求(5-10句话)：
1. 功能描述：界面主要功能和业务场景
2. 布局分析：页面结构、信息层次和内容组织
3. 视觉风格：设计语言、色彩运用和视觉特征
4. 交互元素：按钮、输入框、导航等UI组件识别
5. 用户体验：信息传达清晰度和操作便利性评价

标签要求(10-20个中文标签)：
必须包含基础分类标签(每类至少1个)：
- APP分类: ${listToLine(taxonomy.app || [])}
- 页面类型: ${listToLine(taxonomy.page || [])}
- 控件: ${listToLine(taxonomy.control || [])}

补充标签类型：
- 主色调：蓝色、绿色、红色、黄色、紫色、橙色、粉色、黑色、白色、灰色
- 主题风格：深色主题、浅色主题、现代化、极简风格、商务风格、时尚设计
- 布局特征：卡片式布局、列表布局、网格布局、瀑布流、侧边栏、底部导航
- 功能特征：数据可视化、图表展示、地图导航、音视频播放、文件上传、支付流程

仅返回JSON，无需解释。`;

            // 直接从文件对象转为base64并调用视觉模型
            const base64Image = await api.imageFileToBase64(file);
            const responseText = await api.analyzeImage(base64Image, prompt);

            if (responseText) {
                const parsed = this.parseAIResponse(responseText);
                const enriched = await this.enrichTagsAndDescription(imagePath, parsed, file);
                return enriched;
            }
            console.warn('AI分析无响应，使用基础分析');
            const basic = this.getBasicAnalysis(imagePath);
            const enriched = await this.enrichTagsAndDescription(imagePath, basic, file);
            return enriched;

        } catch (error) {
            console.error(`AI分析失败 (${imagePath}):`, error);
            // 降级到基础分析
            const basic = this.getBasicAnalysis(imagePath);
            const enriched = await this.enrichTagsAndDescription(imagePath, basic);
            return enriched;
        }
    }

    /**
     * 重建索引（无视已打标，全部重跑）
     * @param {string} directory - 图片目录
     */
    async rebuildIndexing(directory = '示例图片/') {
        try {
            console.log('开始重建索引，扫描图片目录...');
            const allImages = await this.scanDirectory(directory);

            if (!allImages || allImages.length === 0) {
                const results = { total: 0, success: 0, skipped: 0, errors: 0, message: '未发现图片' };
                if (this.onIndexComplete) this.onIndexComplete(results);
                return results;
            }

            const results = await this.batchIndexImages(allImages, { forceRebuild: true });
            console.log('重建索引完成:', results);
            return results;
        } catch (error) {
            console.error('重建索引失败:', error);
            this.indexingProgress.isIndexing = false;
            throw error;
        }
    }

    /**
     * 获取索引进度
     * @returns {Object} 当前进度
     */
    getProgress() {
        return { ...this.indexingProgress };
    }

    /**
     * 重置索引器状态
     */
    reset() {
        this.indexedImages.clear();
        this.indexingProgress = {
            total: 0,
            processed: 0,
            errors: 0,
            isIndexing: false
        };
    }

    /**
     * 获取索引统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const metadata = window.imagesMetadata;
        if (!metadata) {
            return {
                totalImages: 0,
                indexedImages: 0,
                unindexedImages: 0
            };
        }

        return metadata.getStats();
    }
}

// 导出全局实例
window.ImageIndexer = ImageIndexer;
window.imageIndexer = new ImageIndexer();
