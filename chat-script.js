// 初始化数据库和API
async function initializeApp() {
    try {
        await window.dbManager.init();
        console.log('数据库初始化成功');
    } catch (error) {
        console.error('数据库初始化失败:', error);
    }
}

// 升级版ChatApp - 集成真实API和文件上传功能
class ChatApp {
    constructor() {
        this.chatContent = document.getElementById('chatContent');
        this.chatInput = document.getElementById('chatInput');
        this.sourcesPanel = document.getElementById('sourcesPanel');
        this.sourcesContent = document.getElementById('sourcesContent');
        this.expandBtn = document.getElementById('expandBtn');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.fileInput = document.getElementById('fileInput');
        this.settingsBtn = document.getElementById('settingsBtn');
        this.settingsModal = document.getElementById('settingsModal');

        this.conversationHistory = [];
        this.uploadedDocuments = [];
        this.currentUploadProgress = null;
        this.currentViewMode = 'list'; // 'list' or 'reader'
        this.currentDocument = null;
        this.documentChunks = [];
        this.citationMapping = new Map(); // 存储引用号到chunk的映射
        this.pdfViewer = null; // PDF渲染器实例

        this.init();
    }

    async init() {
        // 初始化数据库
        await initializeApp();

        // 加载已上传的文档
        await this.loadDocuments();

        // 获取URL参数中的问题
        const urlParams = new URLSearchParams(window.location.search);
        const question = urlParams.get('question');

        if (question) {
            this.startConversation(question);
        } else {
            this.showWelcomeMessage();
        }

        // 绑定事件
        this.bindEvents();

        // 更新API状态
        this.updateAPIStatus();
    }

    bindEvents() {
        // 展开/折叠左侧面板
        this.expandBtn?.addEventListener('click', () => {
            this.toggleSourcesPanel();
        });

        // 输入框回车事件
        this.chatInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 建议问题点击事件
        document.querySelectorAll('.suggestion-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.chatInput.value = btn.textContent;
                this.sendMessage();
            });
        });

        // 引用标记点击事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('citation')) {
                this.handleCitationClick(e.target.dataset.ref);
            }
        });

        // 文件上传事件
        this.uploadBtn?.addEventListener('click', () => {
            this.fileInput?.click();
        });

        this.fileInput?.addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });

        // 设置弹窗事件
        this.settingsBtn?.addEventListener('click', () => {
            this.showSettings();
        });

        // 设置弹窗关闭事件
        document.getElementById('closeSettings')?.addEventListener('click', () => {
            this.hideSettings();
        });

        // 设置保存事件
        document.getElementById('saveSettings')?.addEventListener('click', () => {
            this.saveSettings();
        });

        // 测试连接事件
        document.getElementById('testConnection')?.addEventListener('click', () => {
            this.testConnection();
        });

        // 点击模态框背景关闭
        this.settingsModal?.addEventListener('click', (e) => {
            if (e.target === this.settingsModal) {
                this.hideSettings();
            }
        });
    }

    showWelcomeMessage() {
        const welcomeHtml = `
            <div class="welcome-section">
                <h3>👋 欢迎使用AI体验设计问答助手</h3>
                <p>我可以帮你回答关于AI体验设计的各种问题。你可以：</p>
                <ul>
                    <li>📄 上传文档资料让我学习</li>
                    <li>💬 直接提问AI设计相关问题</li>
                    <li>⚙️ 配置OpenRouter API使用真实AI模型</li>
                </ul>
                <div class="quick-actions">
                    <button class="suggestion-btn">AI在金融应用中的作用是什么？</button>
                    <button class="suggestion-btn">如何优化投资应用的用户体验？</button>
                    <button class="suggestion-btn">设计AI产品时需要考虑哪些因素？</button>
                </div>
            </div>
        `;

        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant welcome';
        messageDiv.innerHTML = `<div class="message-content">${welcomeHtml}</div>`;
        this.chatContent.appendChild(messageDiv);

        // 重新绑定建议按钮事件
        messageDiv.querySelectorAll('.suggestion-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.chatInput.value = btn.textContent;
                this.sendMessage();
            });
        });

        this.scrollToBottom();
    }

    async startConversation(question) {
        // 显示用户问题
        this.addMessage('user', question);

        // 生成AI回答
        await this.generateAIResponse(question);
    }

    addMessage(role, content, withAnimation = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        if (withAnimation && role === 'assistant') {
            // 打字机效果
            this.typeWriter(contentDiv, content, 30);
        } else {
            if (role === 'assistant') {
                // 对AI回答应用Markdown渲染
                const renderedContent = window.markdownParser.parseWithCitations(content);
                contentDiv.innerHTML = renderedContent;
            } else {
                contentDiv.innerHTML = content;
            }
        }

        messageDiv.appendChild(contentDiv);
        this.chatContent.appendChild(messageDiv);

        // 滚动到底部
        this.scrollToBottom();

        // 保存到对话历史
        this.conversationHistory.push({ role, content });
    }

    async generateAIResponse(question) {
        try {
            // 显示加载动画
            this.showTypingIndicator();

            // 检查API配置
            const apiStatus = window.openrouterAPI.getConnectionStatus();
            if (!apiStatus.hasApiKey) {
                this.hideTypingIndicator();
                this.addMessage('assistant', '⚠️ Please configure OpenRouter API key in settings to use real AI chat functionality.', false);
                return;
            }

            // 使用RAG检索相关上下文
            const context = await window.ragEngine.buildContext(question);

            // 构建消息历史
            const messages = this.conversationHistory.map(msg => ({
                role: msg.role,
                content: msg.content
            }));

            // 调用API
            await window.openrouterAPI.chat(
                messages,
                (chunk, isDone) => {
                    if (isDone) {
                        this.hideTypingIndicator();

                        // 保存原始内容到对话历史
                        if (this.currentAssistantMessage && this.currentAssistantMessage.rawContent) {
                            this.conversationHistory.push({
                                role: 'assistant',
                                content: this.currentAssistantMessage.rawContent
                            });
                        }

                        this.currentAssistantMessage = null;
                        this.updateSourcesPanel(context);
                        return;
                    }

                    // 处理流式响应
                    if (!this.currentAssistantMessage) {
                        this.hideTypingIndicator();
                        this.currentAssistantMessage = this.addStreamMessage();
                    }

                    this.appendToStreamMessage(chunk);
                },
                context
            );

        } catch (error) {
            this.hideTypingIndicator();
            console.error('AI回答生成失败:', error);

            const errorMessage = window.openrouterAPI.formatError(error);
            this.addMessage('assistant', `❌ ${errorMessage}`, false);
        }
    }

    addStreamMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = '';

        messageDiv.appendChild(contentDiv);
        this.chatContent.appendChild(messageDiv);

        this.scrollToBottom();
        return contentDiv;
    }

    appendToStreamMessage(chunk) {
        if (this.currentAssistantMessage) {
            // 获取当前累积的原始内容
            if (!this.currentAssistantMessage.rawContent) {
                this.currentAssistantMessage.rawContent = '';
            }
            this.currentAssistantMessage.rawContent += chunk;

            // 应用Markdown渲染
            const renderedContent = window.markdownParser.parseWithCitations(this.currentAssistantMessage.rawContent);
            this.currentAssistantMessage.innerHTML = renderedContent;

            this.scrollToBottom();
        }
    }

    typeWriter(element, text, speed = 50) {
        let i = 0;
        element.innerHTML = '';

        const timer = setInterval(() => {
            if (i < text.length) {
                element.innerHTML = text.substring(0, i + 1);
                i++;
                this.scrollToBottom();
            } else {
                clearInterval(timer);
            }
        }, speed);
    }

    showTypingIndicator() {
        this.currentAssistantMessage = null;

        const typingDiv = document.createElement('div');
        typingDiv.className = 'message assistant';
        typingDiv.id = 'typingIndicator';

        const indicatorDiv = document.createElement('div');
        indicatorDiv.className = 'typing-indicator';
        indicatorDiv.innerHTML = `
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        `;

        typingDiv.appendChild(indicatorDiv);
        this.chatContent.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        if (indicator) {
            indicator.remove();
        }
    }

    async sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message) return;

        // 显示用户消息
        this.addMessage('user', message);

        // 清空输入框
        this.chatInput.value = '';

        // 生成AI回答
        await this.generateAIResponse(message);
    }

    // 文件上传相关方法
    async handleFileUpload(files) {
        if (!files || files.length === 0) return;

        const fileList = Array.from(files);
        this.showUploadProgress(fileList);

        try {
            const result = await window.fileProcessor.processMultipleFiles(
                fileList,
                (progress, message) => {
                    this.updateUploadProgress(progress, message);
                },
                (result, error) => {
                    if (result) {
                        this.uploadedDocuments.push(result);
                        this.updateDocumentItem(result.filename, '上传成功', 'success');
                    } else if (error) {
                        this.updateDocumentItem(error.filename, error.error, 'error');
                    }
                }
            );

            // 刷新文档列表
            setTimeout(() => {
                this.loadDocuments();
                this.hideUploadProgress();
            }, 1500);

        } catch (error) {
            console.error('文件上传失败:', error);
            this.updateUploadProgress(0, `上传失败: ${error.message}`);
        }

        // 清空文件输入
        this.fileInput.value = '';
    }

    showUploadProgress(files) {
        let progressHtml = '<div class="upload-progress"><h4>上传进度</h4>';

        files.forEach(file => {
            const fileIcon = this.getFileIcon(file.type || file.name);
            progressHtml += `
                <div class="upload-item" data-filename="${file.name}">
                    <div class="file-icon">${fileIcon}</div>
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-status">准备上传...</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            `;
        });

        progressHtml += '</div>';

        const existingProgress = this.sourcesContent.querySelector('.upload-progress');
        if (existingProgress) {
            existingProgress.remove();
        }

        this.sourcesContent.insertAdjacentHTML('afterbegin', progressHtml);
    }

    updateUploadProgress(progress, message) {
        const progressElements = this.sourcesContent.querySelectorAll('.progress-fill');
        const statusElements = this.sourcesContent.querySelectorAll('.file-status');

        progressElements.forEach(el => {
            el.style.width = `${progress}%`;
        });

        statusElements.forEach(el => {
            el.textContent = message;
        });
    }

    updateDocumentItem(filename, message, type) {
        const item = this.sourcesContent.querySelector(`[data-filename="${filename}"]`);
        if (!item) return;

        const statusEl = item.querySelector('.file-status');
        const progressBar = item.querySelector('.progress-bar');

        if (type === 'success') {
            statusEl.textContent = message;
            statusEl.className = 'file-status success-message';
            progressBar.style.display = 'none';
        } else if (type === 'error') {
            statusEl.textContent = message;
            statusEl.className = 'file-status error-message';
            progressBar.style.display = 'none';
        } else {
            statusEl.textContent = message;
        }
    }

    hideUploadProgress() {
        const progressEl = this.sourcesContent.querySelector('.upload-progress');
        if (progressEl) {
            progressEl.remove();
        }
    }

    getFileIcon(type) {
        if (type.includes('pdf')) return '📄';
        if (type.includes('text') || type.includes('markdown')) return '📝';
        if (type.includes('word')) return '📄';
        return '📄';
    }

    // 文档管理
    async loadDocuments() {
        try {
            const documents = await window.dbManager.getAllDocuments();
            this.uploadedDocuments = documents;
            this.renderDocuments();
        } catch (error) {
            console.error('加载文档失败:', error);
        }
    }

    renderDocuments() {
        const existingProgress = this.sourcesContent.querySelector('.upload-progress');
        if (existingProgress) return; // 如果正在上传，不要替换内容

        if (this.uploadedDocuments.length === 0) {
            this.sourcesContent.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">📚</div>
                    <div class="empty-state-text">暂无上传的文档</div>
                    <div class="empty-state-subtext">点击上传按钮添加文档资料</div>
                </div>
            `;
            return;
        }

        let documentsHtml = '<div class="documents-list">';

        this.uploadedDocuments.forEach(doc => {
            const icon = this.getFileIcon(doc.type);
            const size = this.formatFileSize(doc.size);
            const date = new Date(doc.uploadTime).toLocaleDateString();

            documentsHtml += `
                <div class="document-item" data-doc-id="${doc.id}" onclick="chatApp.openDocumentReader(${doc.id})" style="cursor: pointer;">
                    <div class="document-icon">${icon}</div>
                    <div class="document-info">
                        <div class="document-name">${doc.filename}</div>
                        <div class="document-meta">${size} • ${date}</div>
                    </div>
                    <div class="document-actions">
                        <button class="doc-action-btn" onclick="event.stopPropagation(); chatApp.deleteDocument(${doc.id})" title="删除">🗑️</button>
                    </div>
                </div>
            `;
        });

        documentsHtml += '</div>';
        this.sourcesContent.innerHTML = documentsHtml;
    }

    async deleteDocument(docId) {
        if (!confirm('确定要删除这个文档吗？')) return;

        try {
            await window.dbManager.deleteDocument(docId);
            await this.loadDocuments();
        } catch (error) {
            console.error('删除文档失败:', error);
            alert('删除失败: ' + error.message);
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 检查文档是否为PDF文件
    isPDFFile(document) {
        if (!document) return false;

        // 首先检查MIME类型
        if (document.type === 'application/pdf') {
            return true;
        }

        // 检查文件扩展名
        if (document.filename) {
            const extension = document.filename.toLowerCase().split('.').pop();
            return extension === 'pdf';
        }

        return false;
    }

    // 文档阅读器相关方法
    async openDocumentReader(docId) {
        try {
            // 获取文档信息
            const document = this.uploadedDocuments.find(doc => doc.id === docId);
            if (!document) {
                console.error('找不到文档:', docId);
                return;
            }

            console.log('打开文档:', document.filename, '类型:', document.type, 'ID:', docId);

            // 获取文档的所有chunks
            const chunks = await window.dbManager.getChunksByDocId(docId);

            this.currentDocument = document;
            this.documentChunks = chunks;
            this.currentViewMode = 'reader';

            // 使用增强的文件类型判断
            if (this.isPDFFile(document)) {
                console.log('识别为PDF文件，开始渲染PDF阅读器');
                await this.renderPDFReader(docId);
            } else {
                console.log('识别为文本文件，使用文本阅读器');
                this.renderDocumentReader();
            }
        } catch (error) {
            console.error('打开文档阅读器失败:', error);
            alert('打开文档失败: ' + error.message);
        }
    }

    async renderPDFReader(docId) {
        try {
            // 获取原始PDF文件数据
            const fileData = await window.dbManager.getDocumentFileData(docId);
            console.log('获取PDF文件数据:', fileData ? '成功' : '失败');

            if (!fileData) {
                console.warn('旧文档缺少PDF原始数据，降级为文本显示');
                this.renderPDFErrorWithFallback('PDF原始数据缺失',
                    '这是一个较早上传的文档，缺少PDF原始数据。请重新上传此PDF文档以享受完整的PDF查看体验。',
                    true);
                return;
            }

            // 创建PDF阅读器界面
            let readerHtml = `
                <div class="pdf-document-reader">
                    <div class="document-reader-header">
                        <button class="back-btn" onclick="chatApp.closeDocumentReader()">
                            ← 返回
                        </button>
                        <div class="document-reader-title">${this.currentDocument.filename}</div>
                    </div>
                    <div class="pdf-reader-container" id="pdfReaderContainer">
                        <!-- PDF查看器将在这里渲染 -->
                    </div>
                </div>
            `;

            this.sourcesContent.innerHTML = readerHtml;

            // 初始化PDF查看器
            const container = document.getElementById('pdfReaderContainer');
            this.pdfViewer = new window.PDFViewer(container);

            // 加载PDF
            await this.pdfViewer.loadPDF(fileData);

        } catch (error) {
            console.error('PDF渲染失败:', error);
            this.renderPDFErrorWithFallback('PDF渲染失败', error.message, false);
        }
    }

    // 渲染PDF错误页面，支持降级到文本显示
    renderPDFErrorWithFallback(title, message, showTextFallback) {
        let fallbackContent = '';

        if (showTextFallback) {
            fallbackContent = `
                <div class="pdf-fallback">
                    <h4>文本内容预览：</h4>
                    <div class="pdf-text-content">
                        ${this.currentDocument.content ?
                            this.currentDocument.content.substring(0, 1000) + '...' :
                            '无法显示内容'}
                    </div>
                    <button class="reupload-btn" onclick="chatApp.triggerReupload('${this.currentDocument.id}')">
                        📄 重新上传PDF文档
                    </button>
                </div>
            `;
        }

        this.sourcesContent.innerHTML = `
            <div class="pdf-error">
                <div class="document-reader-header">
                    <button class="back-btn" onclick="chatApp.closeDocumentReader()">
                        ← 返回
                    </button>
                    <div class="document-reader-title">${this.currentDocument.filename}</div>
                </div>
                <div class="error-content">
                    <div class="error-icon">⚠️</div>
                    <div class="error-title">${title}</div>
                    <div class="error-message">${message}</div>
                    ${fallbackContent}
                </div>
            </div>
        `;
    }

    renderDocumentReader() {
        if (!this.currentDocument || !this.documentChunks) return;

        let readerHtml = `
            <div class="document-reader">
                <div class="document-reader-header">
                    <button class="back-btn" onclick="chatApp.closeDocumentReader()">
                        ← 返回
                    </button>
                    <div class="document-reader-title">${this.currentDocument.filename}</div>
                </div>
                <div class="document-reader-content">
        `;

        this.documentChunks.forEach((chunk, index) => {
            readerHtml += `
                <div class="document-chunk" data-chunk-index="${index}" id="chunk-${index}">
                    ${chunk.content}
                </div>
            `;
        });

        readerHtml += `
                </div>
            </div>
        `;

        this.sourcesContent.innerHTML = readerHtml;
    }

    closeDocumentReader() {
        // 清理PDF查看器
        if (this.pdfViewer) {
            this.pdfViewer.destroy();
            this.pdfViewer = null;
        }

        this.currentViewMode = 'list';
        this.currentDocument = null;
        this.documentChunks = [];
        this.renderDocuments();
    }

    // 跳转到指定chunk并高亮
    scrollToChunk(chunkIndex) {
        if (this.currentViewMode !== 'reader') return;

        // 检查是否是PDF查看器模式
        if (this.pdfViewer && this.currentDocument.type === 'application/pdf') {
            this.scrollToPDFChunk(chunkIndex);
        } else {
            this.scrollToTextChunk(chunkIndex);
        }
    }

    scrollToTextChunk(chunkIndex) {
        const chunkElement = document.getElementById(`chunk-${chunkIndex}`);
        if (!chunkElement) return;

        // 滚动到chunk位置
        chunkElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // 添加高亮效果
        chunkElement.classList.add('highlighted');

        // 3秒后移除高亮
        setTimeout(() => {
            chunkElement.classList.remove('highlighted');
        }, 3000);
    }

    scrollToPDFChunk(chunkIndex) {
        console.log('PDF chunk跳转:', {
            chunkIndex,
            pdfViewerExists: !!this.pdfViewer,
            chunksLength: this.documentChunks.length,
            chunkExists: !!this.documentChunks[chunkIndex]
        });

        if (!this.pdfViewer) {
            console.warn('PDF查看器不存在');
            return;
        }

        if (!this.documentChunks[chunkIndex]) {
            console.warn('找不到指定的chunk:', chunkIndex);
            return;
        }

        const chunk = this.documentChunks[chunkIndex];
        const pageNumber = chunk.pageNumber || chunk.page || 1;

        console.log('跳转到PDF页面:', pageNumber, '块信息:', chunk);

        // 跳转到PDF页面
        this.pdfViewer.scrollToPage(pageNumber);

        // 如果有chunk内容，尝试在PDF中高亮
        if (chunk.content) {
            // 提取chunk的前50个字符作为搜索关键词，清理多余的空白字符
            const searchText = chunk.content
                .replace(/\s+/g, ' ')  // 将多个空白字符替换为单个空格
                .substring(0, 50)
                .trim();

            console.log('高亮搜索文本:', searchText);

            setTimeout(() => {
                if (this.pdfViewer) {
                    this.pdfViewer.highlightText(pageNumber, searchText, {
                        animate: true,
                        color: '#fff3cd'
                    });
                }
            }, 800); // 增加延迟，确保PDF完全渲染
        }
    }

    // 处理引用点击
    async handleCitationClick(refNumber) {
        console.log('点击引用:', refNumber, '当前引用映射:', this.citationMapping);

        const citationData = this.citationMapping.get(parseInt(refNumber));

        if (!citationData) {
            console.warn('找不到引用数据:', refNumber, '可用引用:', Array.from(this.citationMapping.keys()));
            alert(`无法找到引用 [${refNumber}] 的相关文档信息`);
            return;
        }

        const { docId, chunkIndex } = citationData;
        console.log('引用指向:', { docId, chunkIndex, refNumber });

        try {
            // 如果当前不在阅读器模式，或者不是相同文档，切换到阅读器
            if (this.currentViewMode !== 'reader' || !this.currentDocument || this.currentDocument.id !== docId) {
                console.log('需要打开文档阅读器，当前状态:', {
                    currentViewMode: this.currentViewMode,
                    currentDocId: this.currentDocument?.id,
                    targetDocId: docId
                });
                await this.openDocumentReader(docId);
            } else {
                console.log('文档已打开，直接跳转到chunk');
            }

            // 跳转到对应chunk
            setTimeout(() => {
                console.log('执行chunk跳转:', chunkIndex);
                this.scrollToChunk(chunkIndex);
            }, 500); // 增加延迟，确保文档完全加载
        } catch (error) {
            console.error('处理引用点击时发生错误:', error);
            alert(`跳转到引用失败: ${error.message}`);
        }
    }

    // 来源面板更新
    updateSourcesPanel(context) {
        console.log('更新来源面板，上下文数据:', context);

        if (!context || context.length === 0) {
            console.log('没有上下文数据，跳过来源面板更新');
            return;
        }

        // 清空之前的引用映射
        this.citationMapping.clear();

        // 建立引用映射关系
        context.forEach((source, index) => {
            const refNumber = index + 1;

            console.log(`建立引用映射 [${refNumber}]:`, {
                docId: source.docId,
                chunkIndex: source.chunkIndex,
                filename: source.filename,
                hasDocId: source.docId !== undefined,
                hasChunkIndex: source.chunkIndex !== undefined
            });

            if (source.docId !== undefined && source.chunkIndex !== undefined) {
                this.citationMapping.set(refNumber, {
                    docId: source.docId,
                    chunkIndex: source.chunkIndex
                });
                console.log(`引用 [${refNumber}] 映射成功:`, { docId: source.docId, chunkIndex: source.chunkIndex });
            } else {
                console.warn(`引用 [${refNumber}] 映射失败，缺少必要信息:`, { docId: source.docId, chunkIndex: source.chunkIndex });
            }
        });

        console.log('最终引用映射:', Array.from(this.citationMapping.entries()));

        // 检查当前是否在文档阅读器模式
        if (this.currentViewMode === 'reader' && this.currentDocument) {
            console.log('当前在文档阅读器模式，保持PDF阅读器不被替换');
            // 只更新引用映射，不改变界面显示
            return;
        }

        // 如果不在文档阅读器模式，显示参考来源面板
        let sourcesHtml = '<div class="context-sources"><h4>参考来源</h4>';

        context.forEach((source, index) => {
            const refNumber = index + 1;

            sourcesHtml += `
                <div class="source-item">
                    <div class="source-title">
                        <span class="source-icon">📄</span>
                        <span>${source.filename}</span>
                        <sup class="citation" data-ref="${refNumber}">${refNumber}</sup>
                    </div>
                    <div class="source-text">
                        ${source.content.substring(0, 200)}${source.content.length > 200 ? '...' : ''}
                    </div>
                </div>
            `;
        });

        sourcesHtml += '</div>';

        // 显示参考来源面板
        this.sourcesContent.innerHTML = sourcesHtml;

        // 5秒后恢复文档列表（缩短显示时间）
        setTimeout(() => {
            if (this.currentViewMode !== 'reader') {
                this.renderDocuments();
            }
        }, 5000);
    }

    // API设置相关方法
    showSettings() {
        const apiKey = window.openrouterAPI.getStoredApiKey();
        const model = window.openrouterAPI.getStoredModel();

        document.getElementById('apiKey').value = apiKey;
        document.getElementById('modelSelect').value = model;

        this.updateAPIStatus();
        this.settingsModal.classList.add('show');
    }

    hideSettings() {
        this.settingsModal.classList.remove('show');
    }

    async saveSettings() {
        const apiKey = document.getElementById('apiKey').value.trim();
        const model = document.getElementById('modelSelect').value;

        if (!apiKey) {
            alert('请输入API密钥');
            return;
        }

        window.openrouterAPI.setApiKey(apiKey);
        window.openrouterAPI.setModel(model);

        this.hideSettings();
        this.updateAPIStatus();
    }

    async testConnection() {
        const testBtn = document.getElementById('testConnection');
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');

        testBtn.disabled = true;
        testBtn.textContent = '测试中...';
        statusIndicator.className = 'status-indicator connecting';
        statusText.textContent = '连接中...';

        try {
            await window.openrouterAPI.testConnection();
            statusIndicator.className = 'status-indicator connected';
            statusText.textContent = '连接成功';
        } catch (error) {
            statusIndicator.className = 'status-indicator';
            statusText.textContent = '连接失败: ' + error.message;
        }

        testBtn.disabled = false;
        testBtn.textContent = '测试连接';
    }

    updateAPIStatus() {
        const status = window.openrouterAPI.getConnectionStatus();
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');

        if (!statusIndicator || !statusText) return;

        if (status.hasApiKey) {
            statusIndicator.className = 'status-indicator connected';
            statusText.textContent = `已配置 (${status.model})`;
        } else {
            statusIndicator.className = 'status-indicator';
            statusText.textContent = '未配置';
        }
    }

    // 通用方法
    toggleSourcesPanel() {
        this.sourcesPanel.classList.toggle('collapsed');

        const arrow = this.expandBtn.querySelector('svg');
        if (this.sourcesPanel.classList.contains('collapsed')) {
            arrow.style.transform = 'rotate(-90deg)';
        } else {
            arrow.style.transform = 'rotate(0deg)';
        }
    }

    highlightSource(refNumber) {
        const sourceTexts = document.querySelectorAll('.source-text');
        sourceTexts.forEach((text, index) => {
            if (index + 1 == refNumber) {
                text.style.backgroundColor = '#fff3cd';
                text.style.borderLeftColor = '#ffc107';

                setTimeout(() => {
                    text.style.backgroundColor = '#f8f9fa';
                    text.style.borderLeftColor = '#007aff';
                }, 3000);
            }
        });

        const sourcesContent = document.getElementById('sourcesContent');
        sourcesContent.scrollTop = 0;
    }

    // 触发重新上传文档
    triggerReupload(docId) {
        console.log('触发重新上传文档:', docId);

        // 创建临时文件输入
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.pdf';
        fileInput.style.display = 'none';

        fileInput.addEventListener('change', async (e) => {
            const file = e.target.files[0];
            if (!file) return;

            if (file.type !== 'application/pdf') {
                alert('请选择PDF文件');
                return;
            }

            try {
                console.log('开始重新上传PDF文件:', file.name);

                // 获取文件的ArrayBuffer
                const fileData = await this.fileToArrayBuffer(file);

                // 更新数据库中的文档数据
                await window.dbManager.updateDocumentFileData(docId, fileData);

                console.log('PDF文件数据更新成功');
                alert('PDF文档更新成功！现在可以享受完整的PDF查看体验了。');

                // 如果当前正在查看这个文档，重新加载
                if (this.currentDocument && this.currentDocument.id === docId) {
                    await this.renderPDFReader(docId);
                }

            } catch (error) {
                console.error('更新PDF文件失败:', error);
                alert('更新失败: ' + error.message);
            } finally {
                // 清理临时元素
                document.body.removeChild(fileInput);
            }
        });

        // 添加到文档并触发点击
        document.body.appendChild(fileInput);
        fileInput.click();
    }

    // 辅助方法：文件转ArrayBuffer
    fileToArrayBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsArrayBuffer(file);
        });
    }

    scrollToBottom() {
        this.chatContent.scrollTop = this.chatContent.scrollHeight;
    }
}

// 全局实例
let chatApp;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    chatApp = new ChatApp();
});