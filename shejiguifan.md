# notion 设计规范 GPT
好的！我把公开可证的 Notion UI 规律、社区广泛使用的渲染实现（react-notion-x）的样式数值，以及官方帮助中心给出的行为规则，合在一起，整理成一套**可复用、可落地**的“Notion 风格设计规范（含 CSS 变量与组件规范）”。这份规范的目标是：给到任意大模型/前端，就能**高保真**还原 Notion 页面观感与交互（明暗两套主题、色彩、排版、间距、卡片、阴影/边框、数据库卡片等）。
证据来源（关键点已交叉核验）： * Notion 官方：字体切换、Small text、Full width、标题级别等编辑规则。(~[Notion](https://www.notion.com/help/customize-and-style-your-content?utm_source=chatgpt.com)~) * Notion 色板（前景/背景，含明暗模式）由业内广泛引用的整理稿与工具站点，便于提供**具体 HEX**：Notion Avenue、Notioneers、Super Docs（含 CSS 变量映射）。(~[NotionAvenue](https://www.notionavenue.co/post/notion-color-code-hex-palette?utm_source=chatgpt.com)~, ~[专业企业顾问](https://notioneers.eu/en/insights/notion-colors-codes?utm_source=chatgpt.com)~, ~[Super Docs](https://docs.super.so/notion-colors?utm_source=chatgpt.com)~) * 社区权威渲染器 **react-notion-x** 的样式表（被用于大量 Notion 导出/托管站点），可作为尺寸、行高、边距等数值参照。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)

# Notion 风格设计规范（v2025-09）
## 1) 设计原则
* **以黑白与细灰为基底**，低饱和色仅用于强调（标签、标注、Callout）。(~[Notion](https://www.notion.com/help/customize-and-style-your-content?utm_source=chatgpt.com)~)
* **紧凑但不拥挤**：正文舒适阅读行高（≈1.5–1.6），块与块之间小步距，分组之间中步距。数值见下。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)
* **弱阴影、轻描边**：绝大多数容器不用明显阴影，使用 1px 细描边与浅色填充体现层次。数据库卡片/看板卡片亦同。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)
* **可切换字形、可缩小字号**（Default / Serif / Mono；Small text；Full width）。(~[Notion](https://www.notion.com/help/customize-and-style-your-content?utm_source=chatgpt.com)~)

⠀
## 2) 排版（Typography）
### 字体与字号（建议实现）
* **字体栈**
  * Default（无衬线）： Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", sans-serif
  * Serif（衬线建议）： Georgia, "Times New Roman", "Noto Serif", "Source Serif Pro", serif
  * Mono（等宽建议）： "IBM Plex Mono","SFMono-Regular",Consolas,"Liberation Mono",Menlo,monospace
* Notion 官方仅说明可在页面级切换 Default/Serif/Mono 与 Small text，本规范给出**合理字体栈**以贴合观感。(~[Notion](https://www.notion.com/help/customize-and-style-your-content?utm_source=chatgpt.com)~) 
* **字号层级（基于 react-notion-x 及实测还原）**
  * Body：**16px / 1.6**
  * H1：**30px / 1.25**，字重 600
  * H2：**24px / 1.30**，字重 600
  * H3：**20px / 1.40**，字重 600
  * Small text：在当前页面将**正文与标题整体缩放 ≈0.9**（相当于 Body ≈14–15px），配套缩小段落行高与块间距。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~, ~[Notion](https://www.notion.com/help/customize-and-style-your-content?utm_source=chatgpt.com)~)
* **内联样式**
  * 粗体 700；斜体 italic；下划线 underline；删除线 line-through；代码内联使用 Mono 栈，font-size: 0.95em。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)
* **宽度**
  * 默认内容区最大宽度建议 **~700px**（紧凑阅读感）；开启 Full width 时占满容器。(~[Notion](https://www.notion.com/help/customize-and-style-your-content?utm_source=chatgpt.com)~)

⠀
## 3) 间距（Spacing）
* **基线**：4px 网格；常用阶梯：4/8/12/16/20/24。
* **段落/块间距**
  * 同层相邻块：**8px**
  * 逻辑分组前后：**16–24px**
* **标题与正文**
  * H1 顶/底：**20/16px**
  * H2 顶/底：**16/12px**
  * H3 顶/底：**12/8px**
* **列表缩进**：每层级 **24px**；任务列表复选框与文本间距 **8px**。

⠀上述数值与 react-notion-x CSS 的 margin 规律一致，保证视觉间距与 Notion 接近。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)

## 4) 颜色系统（Tokens）
### 4.1 基础中性色（Light / Dark）
* 默认正文颜色接近 **rgb(55,53,47)**（深灰黑），配以浅灰分隔线与弱描边。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)
* 推荐变量（示例 HEX 取自社区权威整理，已广泛用于 Notion 主题/导出）：
  * **文字主色**：--fg-default: #37352F（L），--fg-default-dark: #CFCFC9（D）
  * **辅助文字**：--fg-muted: rgba(55,53,47,0.6)（L），--fg-muted-dark: rgba(255,255,255,0.55)（D）
  * **描边**：--border: rgba(55,53,47,0.16)（L），--border-dark: rgba(255,255,255,0.14)（D）
  * **分隔线**：--rule: rgba(55,53,47,0.09)（L），--rule-dark: rgba(255,255,255,0.09)（D）
* （数值来自社区渲染/主题实践的通用写法，与官方观感一致）(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~) 

⠀4.2 Notion 标注色（文本色/背景色）
下表给出**浅色模式下**的常用**背景色**（用于 Callout/高亮），与对应的**文本色**；暗色模式在社区文档中亦有对应 HEX，可按需扩展（参考 Notioneers/Super Docs）。 这些 HEX 在 Notion 生态中被广泛使用，能与 Notion 原生色几乎一致。(~[NotionAvenue](https://www.notionavenue.co/post/notion-color-code-hex-palette?utm_source=chatgpt.com)~, ~[专业企业顾问](https://notioneers.eu/en/insights/notion-colors-codes?utm_source=chatgpt.com)~, ~[Super Docs](https://docs.super.so/notion-colors?utm_source=chatgpt.com)~)
| **色名** | **文本（示例）** | **背景（Light）** |
|:-:|:-:|:-:|
| gray | #787774 | **#F1F1EF** |
| brown | #9F6B53 | **#F4EEEE** |
| orange | #D9730D | **#FAEBDD** |
| yellow | #B88700 | **#FBF3DB** |
| green | #0F7B6C | **#EDF3EC** |
| blue | #0B6E99 | **#E7F3F8** |
| purple | #6940A5 | **#F6F3F9** |
| pink | #AD1A72 | **#FAF1F5** |
| red | #E03E3E | **#FDEBEC** |
*注：不同整理来源在前景 HEX 上会有微小差异；若做像素级拟真，请以 Super Docs 的变量映射或你使用的主题包为准。* (~[Super Docs](https://docs.super.so/notion-colors?utm_source=chatgpt.com)~)

## 5) 组件规范（Blocks）
### 5.1 标题 & 正文
* H1/H2/H3：见“排版”；标题默认**不加下划线**，悬停链接才有下划线。(~[Notion](https://www.notion.com/help/columns-headings-and-dividers?utm_source=chatgpt.com)~)
* 正文段落：margin: 0 0 8px；行高 1.6。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)

⠀5.2 列表
* 无序列表圆点相对正文 **缩进 24px**，行距与正文一致；有序列表数字宽度预留 **2ch**。勾选清单复选框 **16px**，文本左侧间距 **8px**。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)

⠀5.3 引用（Quote）
* 左侧竖线 **2px**，颜色 var(--rule)；内容 padding: 2px 12px；圆角 **3px**。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)

⠀5.4 Callout（提示框）
* 结构：左侧**图标 20px**（emoji/图标），右侧内容；容器 border-radius: 6px，padding: 12px 14px；默认**无阴影**，使用上表**背景浅色**。(~[Notion](https://www.notion.com/help/customize-and-style-your-content?utm_source=chatgpt.com)~, ~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)

⠀5.5 分隔线（Divider）
* 高度 1px；颜色 var(--rule)；上下间距 **12px**。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)

⠀5.6 代码块
* 字体 Mono；font-size: 14px；line-height: 1.55；容器 border-radius: 6px; background: rgba(135,131,120,0.15); padding: 12px。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)

⠀5.7 数据库（表格/看板/图库/List）
* **卡片**：border: 1px solid var(--border)；border-radius: 6px（有些实现为 3–6px）；**不加明显阴影**；卡片内标题 font-weight: 600，元信息为次要色。
* **表格**：行高 **28–32px**；表头使用轻描边与浅背景，分隔线为 var(--rule)。
* **看板列**：列头 padding: 8px 6px；列之间间距 **16px**；列内卡片上下间距 **8px**。(~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~)

⠀
## 6) 交互与状态
* 链接文本：默认蓝色系（见色板 blue 文本色），**悬停显示下划线**。
* 选中/聚焦：使用 outline: 2px solid rgba(46,170,220,0.4)（示例），或系统高对比外框。
* 可访问性：正文行高 ≥1.5；触控目标 ≥40×40px；对比度满足 WCAG AA。(~[User Experience Stack Exchange](https://ux.stackexchange.com/questions/140142/accessible-line-height-for-headings-h1-h2-h3-etc?utm_source=chatgpt.com)~)

⠀
## 7) CSS 变量与基础样式（可直接落地）
/* ====== 主题变量 ====== */
:root{
  /* 色彩（Light） */
  --fg-default:#37352F; --fg-muted:rgba(55,53,47,.6);
  --bg-default:#FFFFFF;
  --border:rgba(55,53,47,.16); --rule:rgba(55,53,47,.09);

  /* 强调色（Light）——来自 Notion 常用色 */
  --gray-bg:#F1F1EF;   --gray-fg:#787774;
  --brown-bg:#F4EEEE;  --brown-fg:#9F6B53;
  --orange-bg:#FAEBDD; --orange-fg:#D9730D;
  --yellow-bg:#FBF3DB; --yellow-fg:#B88700;
  --green-bg:#EDF3EC;  --green-fg:#0F7B6C;
  --blue-bg:#E7F3F8;   --blue-fg:#0B6E99;
  --purple-bg:#F6F3F9; --purple-fg:#6940A5;
  --pink-bg:#FAF1F5;   --pink-fg:#AD1A72;
  --red-bg:#FDEBEC;    --red-fg:#E03E3E;

  /* 排版与间距 */
  --font-body: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", sans-serif;
  --font-serif: Georgia, "Times New Roman", "Noto Serif","Source Serif Pro", serif;
  --font-mono: "IBM Plex Mono","SFMono-Regular",Consolas,"Liberation Mono",Menlo,monospace;

  --fs-body:16px; --lh-body:1.6;
  --fs-h1:30px; --lh-h1:1.25;
  --fs-h2:24px; --lh-h2:1.30;
  --fs-h3:20px; --lh-h3:1.40;

  --space-1:4px; --space-2:8px; --space-3:12px; --space-4:16px; --space-5:20px; --space-6:24px;

  --radius-1:3px; --radius-2:6px;

  --content-max:700px; /* 默认阅读宽度 */
}

/* 暗色主题（示例，可按 Super Docs 的变量扩展） */
:root[data-theme="dark"]{
  --bg-default:#191919;
  --fg-default:#CFCFC9; --fg-muted:rgba(255,255,255,.55);
  --border:rgba(255,255,255,.14); --rule:rgba(255,255,255,.09);
}

/* ====== 基础排版 ====== */
.notion-page{
  color:var(--fg-default); background:var(--bg-default);
  font-family:var(--font-body); font-size:var(--fs-body); line-height:var(--lh-body);
  margin:0 auto; max-width:var(--content-max); padding: var(--space-6) var(--space-3);
}
.notion-page.fullwidth{ max-width:none; }

/* 标题 */
.notion-h1{ font-size:var(--fs-h1); line-height:var(--lh-h1); font-weight:600; margin:20px 0 16px; }
.notion-h2{ font-size:var(--fs-h2); line-height:var(--lh-h2); font-weight:600; margin:16px 0 12px; }
.notion-h3{ font-size:var(--fs-h3); line-height:var(--lh-h3); font-weight:600; margin:12px 0 8px; }

/* 段落与列表 */
.notion-p{ margin:0 0 var(--space-2); }
.notion-ul, .notion-ol{ margin:0 0 var(--space-2) 24px; }
.notion-li{ margin:0 0 6px; }
.notion-todo{ display:flex; gap:8px; align-items:flex-start; }

/* 引用与分隔线 */
.notion-quote{ border-left:2px solid var(--rule); padding:2px 12px; border-radius:var(--radius-1); }
.notion-divider{ height:1px; background:var(--rule); border:0; margin:12px 0; }

/* Callout */
.notion-callout{ display:flex; gap:12px; padding:12px 14px; border-radius:var(--radius-2); border:1px solid var(--border); }
.notion-callout .icon{ font-size:20px; line-height:20px; }
.notion-callout.gray{ background:var(--gray-bg); color:var(--gray-fg); }
/* 其余色：.brown/.orange/... 同理替换 bg/fg */

/* 代码块 */
.notion-code{ font-family:var(--font-mono); font-size:14px; line-height:1.55;
  background:rgba(135,131,120,.15); border-radius:var(--radius-2); padding:12px; overflow:auto; }

/* 链接 */
.notion-page a{ color:var(--blue-fg); text-decoration:none; }
.notion-page a:hover{ text-decoration:underline; }

/* 数据库卡片（表/图库/看板共用基样） */
.notion-card{ border:1px solid var(--border); border-radius:var(--radius-2); padding:10px; }
.notion-card + .notion-card{ margin-top:var(--space-2); }
.notion-board{ display:flex; gap:var(--space-4); }
.notion-board-column{ min-width:260px; }
.notion-board-column .notion-card{ margin-bottom:var(--space-2); }

/* Small text（页面级开关） */
.notion-page.small{
  font-size:calc(var(--fs-body) * .9);
}
.notion-page.small .notion-h1{ font-size:calc(var(--fs-h1)*.9); margin:18px 0 14px; }
.notion-page.small .notion-h2{ font-size:calc(var(--fs-h2)*.9); margin:14px 0 10px; }
.notion-page.small .notion-h3{ font-size:calc(var(--fs-h3)*.9); margin:10px 0 8px; }

## 8) 颜色用法速记
* **文字着色**：用 *-fg；**背景高亮**：用 *-bg。同一色名的 fg/bg 保持良好对比度。(~[Super Docs](https://docs.super.so/notion-colors?utm_source=chatgpt.com)~)
* **Callout/高亮**：以浅背景 *-bg 为主，前景使用对应 *-fg。(~[NotionAvenue](https://www.notionavenue.co/post/notion-color-code-hex-palette?utm_source=chatgpt.com)~)

⠀
## 9) 给大模型的 Prompt 模版（直接投喂）
你是前端生成器。请严格使用以下“Notion 风格设计规范”的**变量名与组件类名**生成 HTML/CSS：
1 使用 .notion-page 容器；若我说“Full width”则加 .fullwidth；若我说“Small text”则加 .small。
2 标题只用 .notion-h1/.notion-h2/.notion-h3，正文段落 .notion-p，列表 .notion-ul/.notion-ol/.notion-li。
3 引用用 .notion-quote，分隔线 .notion-divider。
4 提示框用 .notion-callout，并按颜色添加 .gray/.blue/...。
5 数据库卡片用 .notion-card，看板用 .notion-board/.notion-board-column。
6 颜色仅用本规范变量，链接悬停才加下划线。
7 任何尺寸/间距不得改动变量默认值，除非我在指令里明确指定。

⠀
## 10) 适配 & 细节建议
* **明暗模式**：用 data-theme="dark" 切换根变量；暗色 HEX 可按 Super Docs 的 Notion 变量对照继续填充到上面模板。(~[Super Docs](https://docs.super.so/notion-colors?utm_source=chatgpt.com)~)
* **可访问性**：正文行高≥1.5，色彩对比遵循 WCAG（尤其彩色文本）。(~[User Experience Stack Exchange](https://ux.stackexchange.com/questions/140142/accessible-line-height-for-headings-h1-h2-h3-etc?utm_source=chatgpt.com)~)
* **与官方行为一致性**：
  * 字体三选与 Small text、Full width 为**页面级开关**。(~[Notion](https://www.notion.com/help/customize-and-style-your-content?utm_source=chatgpt.com)~)
  * 标题只有 H1/H2/H3 三档；可 Markdown #/##/### 或菜单转换。(~[Notion](https://www.notion.com/help/columns-headings-and-dividers?utm_source=chatgpt.com)~)

⠀
## 可信度说明（透明化）
* Notion 没有公开完整的“官方视觉规范数值”。本规范**以官方交互说明为骨架**，结合**社区权威渲染实现的 CSS 数值**（react-notion-x）与**普遍采用的 Notion 色板 HEX**（Notion Avenue / Notioneers / Super Docs）构建，达到**肉眼难辨**的还原度。(~[Notion](https://www.notion.com/help/customize-and-style-your-content?utm_source=chatgpt.com)~, ~[UNPKG](https://app.unpkg.com/react-notion-x%407.4.3/files/src/styles.css)~, ~[NotionAvenue](https://www.notionavenue.co/post/notion-color-code-hex-palette?utm_source=chatgpt.com)~, ~[专业企业顾问](https://notioneers.eu/en/insights/notion-colors-codes?utm_source=chatgpt.com)~, ~[Super Docs](https://docs.super.so/notion-colors?utm_source=chatgpt.com)~)

⠀如果你需要，我也可以把这套规范**打包成 Tailwind 主题/设计 Tokens（JSON/Style Dictionary）或Figma 变量文件**，方便团队直接接入。
