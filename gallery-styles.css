/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f7;
    color: #1d1d1f;
    line-height: 1.6;
}

/* 顶部导航样式 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: white;
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.back-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1d1d1f;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background-color: #f8f8f8;
}

.page-title {
    flex: 1;
    text-align: center;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0.2px;
}
@media (min-width: 768px) {
    .page-title { font-size: 22px; }
}
@media (min-width: 1200px) {
    .page-title { font-size: 24px; }
}

/* 设置按钮 */
.settings-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1d1d1f;
    transition: all 0.3s ease;
}

.settings-btn:hover {
    background-color: #f8f8f8;
    transform: rotate(90deg);
}

/* 主容器 */
.gallery-container {
    padding: 80px 20px 20px;
    max-width: 1600px;
    margin: 0 auto;
    min-height: calc(100vh - 80px);
}

/* 瀑布流网格 */
.masonry-grid {
    column-count: 2;
    column-gap: 20px;
    break-inside: avoid;
}

@media (min-width: 768px) {
    .masonry-grid {
        column-count: 3;
    }
}

@media (min-width: 1024px) {
    .masonry-grid {
        column-count: 4;
    }
}

@media (min-width: 1400px) {
    .masonry-grid {
        column-count: 5;
    }
}

/* 少量图片时的网格布局 */
.masonry-grid-small {
    display: grid;
    gap: 24px;
    justify-content: center;
    align-items: start;
    grid-template-columns: repeat(auto-fit, minmax(300px, 380px));
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 针对不同图片数量的特殊布局 */
.masonry-grid-small.images-1 {
    grid-template-columns: minmax(320px, 450px);
    max-width: 500px;
}

.masonry-grid-small.images-2 {
    grid-template-columns: repeat(2, minmax(300px, 400px));
    max-width: 850px;
}

.masonry-grid-small.images-3 {
    grid-template-columns: repeat(3, minmax(280px, 350px));
    max-width: 1100px;
}

.masonry-grid-small.images-4 {
    grid-template-columns: repeat(2, minmax(300px, 400px));
    max-width: 850px;
}

.masonry-grid-small.images-5 {
    grid-template-columns: repeat(3, minmax(280px, 350px));
    max-width: 1100px;
}

.masonry-grid-small.images-6 {
    grid-template-columns: repeat(3, minmax(280px, 350px));
    max-width: 1100px;
}

/* 大屏幕优化 */
@media (min-width: 1600px) {
    .masonry-grid-small.images-4,
    .masonry-grid-small.images-5,
    .masonry-grid-small.images-6 {
        grid-template-columns: repeat(4, minmax(280px, 350px));
        max-width: 1450px;
    }
}

/* 中等屏幕调整 */
@media (max-width: 1024px) {
    .masonry-grid-small {
        gap: 20px;
        padding: 0 15px;
    }

    .masonry-grid-small.images-3,
    .masonry-grid-small.images-5,
    .masonry-grid-small.images-6 {
        grid-template-columns: repeat(2, minmax(280px, 1fr));
        max-width: 700px;
    }
}

/* 小屏幕下的响应式调整 */
@media (max-width: 768px) {
    .masonry-grid-small {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        max-width: none;
        margin: 0;
        padding: 0 10px;
        gap: 18px;
    }

    .masonry-grid-small.images-1,
    .masonry-grid-small.images-2,
    .masonry-grid-small.images-3,
    .masonry-grid-small.images-4,
    .masonry-grid-small.images-5,
    .masonry-grid-small.images-6 {
        grid-template-columns: 1fr;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .masonry-grid-small {
        gap: 15px;
        grid-template-columns: 1fr;
        padding: 0 5px;
    }
}

/* 瀑布流图片项 */
.masonry-item {
    display: inline-block;
    width: 100%;
    margin-bottom: 20px;
    break-inside: avoid;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.masonry-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.masonry-item img {
    width: 100%;
    height: auto;
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.masonry-item:hover img {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 小网格布局中的图片项样式 */
.masonry-grid-small .masonry-item {
    display: block;
    margin-bottom: 0;
    break-inside: initial;
    position: relative;
    overflow: hidden;
    opacity: 0;
    animation: fadeInUp 0.6s ease forwards;
}

/* 为不同的图片项添加延迟动画 */
.masonry-grid-small .masonry-item:nth-child(1) { animation-delay: 0.1s; }
.masonry-grid-small .masonry-item:nth-child(2) { animation-delay: 0.2s; }
.masonry-grid-small .masonry-item:nth-child(3) { animation-delay: 0.3s; }
.masonry-grid-small .masonry-item:nth-child(4) { animation-delay: 0.4s; }
.masonry-grid-small .masonry-item:nth-child(5) { animation-delay: 0.5s; }
.masonry-grid-small .masonry-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.masonry-grid-small .masonry-item img {
    max-height: 700px;
    object-fit: contain;
    background-color: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.masonry-grid-small .masonry-item:hover img {
    transform: scale(1.02);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

/* 单张图片时的特殊样式 */
.masonry-grid-small.images-1 .masonry-item img {
    max-height: 800px;
    border-radius: 16px;
}

/* 两张图片时的特殊样式 */
.masonry-grid-small.images-2 .masonry-item img {
    max-height: 650px;
    border-radius: 14px;
}

/* 图片标题（如果需要） */
.masonry-item .item-title {
    padding: 10px 5px;
    font-size: 14px;
    color: #666;
    text-align: center;
}

/* 空状态样式（无结果/无内容）*/
.empty-state {
    text-align: center;
    padding: 80px 24px;
    color: #4b5563;
    grid-column: 1 / -1;
    background: linear-gradient(180deg, #ffffff, #fbfbfd);
    border: 1px solid #eef0f3;
    border-radius: 16px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.06);
}

.empty-state p {
    font-size: 20px;
    margin-bottom: 12px;
    color: #1f2937;
    font-weight: 600;
}

.empty-state small {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.7;
}

@media (min-width: 768px) {
    .empty-state { padding: 96px 32px; }
    .empty-state p { font-size: 22px; }
}

/* 搜索建议样式 */
.search-suggestions-empty {
    margin-top: 20px;
    text-align: left;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.search-suggestions-empty p {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.search-suggestions-empty ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.search-suggestions-empty li {
    padding: 8px 0;
    font-size: 14px;
    color: #666;
    border-left: 3px solid #007AFF;
    padding-left: 12px;
    margin-bottom: 6px;
}

/* 加载动画 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
}

.loading::after {
    content: '';
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1d1d1f;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    position: relative;
    width: 95%;
    max-width: 1400px;
    height: 92%;
    max-height: none;
    background: #FFFFFF;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid rgba(55,53,47,0.16);
    box-shadow: 0 8px 24px rgba(55,53,47,0.12);
    transform: scale(0.96) translateY(20px);
    transition: all 0.3s ease;
}

.modal-overlay.show .modal-container {
    transform: scale(1) translateY(0);
}

/* 模态弹窗控制按钮容器 */
.modal-controls {
    position: absolute;
    top: 16px;
    right: 16px;
    display: flex;
    gap: 8px;
    z-index: 10;
}

.modal-control-btn {
    width: 32px;
    height: 32px;
    background: rgba(55,53,47,0.06);
    border: none;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #37352F;
}

.modal-control-btn:hover {
    background: rgba(55,53,47,0.12);
}

.modal-control-btn svg {
    width: 18px;
    height: 18px;
}

.modal-refresh-btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.modal-refresh-btn.is-loading svg {
    animation: spin 0.9s linear infinite;
}

.modal-content {
    display: flex;
    height: 100%;
}

/* 左侧图片区域 */
.modal-image-section {
    flex: 0 0 68%;
    position: relative;
    background: #F1F1EF;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.modal-image-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(55,53,47,0.08);
    transition: all 0.3s ease;
}

.modal-image-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 右侧信息区域 */
.modal-info-section {
    flex: 1;
    background: #FFFFFF;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-left: 1px solid rgba(55,53,47,0.09);
}

.modal-info-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid rgba(55,53,47,0.09);
    background: #FFFFFF;
    color: #37352F;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
}

.modal-info-header h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.modal-info-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.info-block {
    margin-bottom: 24px;
}

.info-block:last-child {
    margin-bottom: 0;
}

.info-block h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #37352F;
    letter-spacing: 0.01em;
}

.description-content {
    background: rgba(55,53,47,0.06);
    border-radius: 6px;
    padding: 16px;
    line-height: 1.6;
    color: #37352F;
    font-size: 14px;
    border: 1px solid rgba(55,53,47,0.12);
}

.description-content p {
    margin: 0;
    font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.tags-content {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    background: rgba(55,53,47,0.09);
    color: #37352F;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    transition: background-color 0.2s ease;
    border: 1px solid rgba(55,53,47,0.16);
    font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.tag:hover {
    background: rgba(55,53,47,0.12);
}

/* 加载动画 */
.analysis-loading {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(55,53,47,0.6);
    font-style: normal;
    font-size: 14px;
    font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.loading-dots {
    display: inline-block;
    width: 20px;
    height: 20px;
    position: relative;
}

.loading-dots::before {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #37352F;
    animation: loading-dots 1.4s infinite ease-in-out both;
}

.loading-dots::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #37352F;
    left: 8px;
    animation: loading-dots 1.4s infinite ease-in-out both;
    animation-delay: -0.16s;
}

.loading-dots::before {
    left: 0;
    animation-delay: -0.32s;
}

@keyframes loading-dots {
    0%, 80%, 100% {
        opacity: 0;
        transform: scale(0.5);
    }
    40% {
        opacity: 1;
        transform: scale(1);
    }
}

/* API设置区域 */
.api-settings {
    background: rgba(55,53,47,0.06);
    border-radius: 6px;
    padding: 16px;
    border: 1px solid rgba(55,53,47,0.12);
}

.api-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 12px;
    color: #37352F;
    font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.status-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.online {
    background: #0F7B6C;
}

.status-indicator.offline {
    background: #787774;
}

.config-api-btn, .primary-btn {
    background: #37352F;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.config-api-btn:hover, .primary-btn:hover {
    background: #000000;
}

/* 错误和提示信息 */
.error-message {
    color: #E03E3E;
    background: #FDEBEC;
    border: 1px solid rgba(224,62,62,0.2);
    border-radius: 6px;
    padding: 12px;
    font-size: 13px;
    font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.api-prompt {
    text-align: center;
    color: rgba(55,53,47,0.6);
    font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.api-prompt p {
    margin-bottom: 12px;
    font-size: 14px;
}

/* 防止页面滚动 */
body.modal-open {
    overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .gallery-container {
        padding: 80px 15px 20px;
    }

    .masonry-grid {
        column-gap: 15px;
    }

    .masonry-item {
        margin-bottom: 15px;
    }

    .header {
        padding: 0 15px;
    }

    .modal-container {
        width: 95%;
        height: 90%;
        border-radius: 15px;
    }

    .modal-content {
        flex-direction: column;
    }

    .modal-image-section {
        flex: 0 0 60%;
    }

    .modal-image-container {
        padding: 20px;
    }

    .modal-info-header {
        padding: 20px;
    }

    .modal-info-header h3 {
        font-size: 18px;
    }

    .modal-info-content {
        padding: 20px;
    }

    .modal-controls {
        top: 15px;
        right: 15px;
    }
    .modal-regenerate-btn,
    .modal-close-btn {
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 480px) {
    .modal-container {
        width: 98%;
        height: 95%;
        border-radius: 10px;
    }

    .modal-image-section {
        flex: 0 0 50%;
    }

    .modal-info-header {
        padding: 15px;
    }

    .modal-info-content {
        padding: 15px;
    }

    .info-block {
        margin-bottom: 20px;
    }
}

/* 设置弹窗样式 */
.settings-modal-container {
    position: relative;
    width: 90%;
    max-width: 500px;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: scale(0.9) translateY(30px);
    transition: all 0.3s ease;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-overlay.show .settings-modal-container {
    transform: scale(1) translateY(0);
}

/* 设置弹窗头部 */
.settings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.settings-header h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.settings-header .modal-close-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    width: 32px;
    height: 32px;
}

.settings-header .modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 设置内容区域 */
.settings-content {
    flex: 1;
    padding: 25px;
    overflow-y: auto;
}

.settings-section {
    margin-bottom: 25px;
}

.settings-section:last-child {
    margin-bottom: 0;
}

/* 标签样式 */
.settings-label {
    display: block;
    position: relative;
}

.label-text {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.label-description {
    display: block;
    font-weight: 400;
    color: #666;
    font-size: 12px;
    margin-top: 2px;
}

/* 输入框样式 */
.settings-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #f8f9fa;
    padding-right: 45px;
}

.settings-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 密码切换按钮 */
.toggle-password {
    position: absolute;
    right: 12px;
    top: 32px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    color: #666;
    transition: all 0.2s ease;
}

.toggle-password:hover {
    background: #f0f0f0;
    color: #333;
}

/* 下拉选择框样式 */
.settings-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.settings-select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* API测试区域 */
.api-test-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.api-connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-indicator.testing {
    background: #ffc107;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.test-connection-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.test-connection-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.test-connection-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 帮助区域 */
.help-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #17a2b8;
}

.help-section h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #333;
}

.help-steps {
    margin: 12px 0;
    padding-left: 20px;
    font-size: 13px;
    color: #666;
    line-height: 1.6;
}

.help-steps li {
    margin-bottom: 6px;
}

.help-steps a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.help-steps a:hover {
    text-decoration: underline;
}

.help-note {
    font-size: 12px;
    color: #666;
    margin: 12px 0 0 0;
    font-style: italic;
}

/* 设置底部按钮 */
.settings-footer {
    padding: 20px 25px;
    background: #f8f9fa;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    border-top: 1px solid #e9ecef;
}

.settings-btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.settings-btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.settings-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.settings-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* 设置弹窗响应式 */
@media (max-width: 768px) {
    .settings-modal-container {
        width: 95%;
        border-radius: 12px;
    }

    .settings-header {
        padding: 15px 20px;
    }

    .settings-header h3 {
        font-size: 16px;
    }

    .settings-content {
        padding: 20px;
    }

    .settings-section {
        margin-bottom: 20px;
    }

    .settings-footer {
        padding: 15px 20px;
        flex-direction: column;
    }

    .settings-btn-secondary,
    .settings-btn-primary {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .api-test-section {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
        text-align: center;
    }

    .test-connection-btn {
        width: 100%;
    }
}
