<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>图片索引表格编辑器（Jspreadsheet）</title>
  <link rel="stylesheet" href="styles.css" />

  <!-- jsuites + jspreadsheet 样式（CDN） -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/jsuites@5.2.12/dist/jsuites.css" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/jspreadsheet-ce@4.13.1/dist/jspreadsheet.css" />

  <style>
    body { background: #f5f6f8; }
    .page-wrap { max-width: 1400px; margin: 0 auto; padding: 20px; }
    .header { display: flex; align-items: center; gap: 12px; margin-bottom: 16px; }
    .header h1 { font-size: 20px; margin: 0; }
    .toolbar { display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 12px; }
    .toolbar .btn { background: #007aff; color: #fff; border: 0; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 13px; }
    .toolbar .btn.secondary { background: #6c757d; }
    .toolbar .btn.success { background: #28a745; }
    .toolbar .btn.warning { background: #f39c12; }
    .toolbar .btn.danger { background: #dc3545; }
    .toolbar .btn:disabled { opacity: .6; cursor: not-allowed; }
    .status { margin: 8px 0 12px; font-size: 13px; color: #555; }
    .sheet-wrap { background: #fff; border-radius: 10px; padding: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.06); }
    .note { color: #666; font-size: 12px; margin-top: 6px; }
    .search-wrap { margin-left: auto; display: flex; align-items: center; gap: 8px; }
    .search-wrap input { padding: 8px 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 13px; width: 280px; }
    .back-btn { background: rgba(0,0,0,0.06); border: none; border-radius: 8px; padding: 8px 10px; cursor: pointer; }
    .hint { font-size: 12px; color: #888; }
    .legend { margin-top: 8px; font-size: 12px; color: #666; }
    .progress { display:none; margin-top:8px; }
    .progress .bar { height: 6px; background: #e9ecef; border-radius: 6px; overflow: hidden; }
    .progress .fill { height: 100%; width: 0%; background: #007aff; transition: width .3s ease; }

    /* Fallback 简易表格样式 */
    .fallback-table { width: 100%; border-collapse: collapse; font-size: 13px; }
    .fallback-table th, .fallback-table td { border: 1px solid #eee; padding: 8px; vertical-align: top; }
    .fallback-table th { background: #fafafa; position: sticky; top: 0; z-index: 1; }
    .fallback-table td[contenteditable="true"] { background: #fffef7; }
    .fallback-table img { max-width: 56px; max-height: 56px; border-radius: 6px; display: block; }
    .fallback-wrap { max-height: 70vh; overflow: auto; border: 1px solid #eee; border-radius: 8px; }
    .file-btn { position: relative; display: inline-block; }
    .file-btn .file-overlay { position: absolute; inset: 0; width: 100%; height: 100%; opacity: 0; cursor: pointer; z-index: 2; display: block; }
  </style>
</head>
<body>
  <div class="page-wrap">
    <div class="header">
      <button class="back-btn" onclick="window.location.href='index.html'">返回</button>
      <h1>图片索引表格编辑器（Jspreadsheet）</h1>
      <div class="search-wrap">
        <input id="quickSearch" placeholder="搜索 路径/描述/标签/分类..." />
      </div>
    </div>

    <div class="toolbar">
      <button class="btn" onclick="reloadData()">刷新数据</button>
      <button class="btn secondary" onclick="importFromJson()">从JSON导入</button>
      <button class="btn secondary" onclick="importFromCsv()">从CSV导入</button>
      <button class="btn success" onclick="exportToJson()">导出JSON</button>
      <button class="btn success" onclick="exportToCsv()">导出CSV</button>

      <span class="hint">|</span>
      <button class="btn" onclick="selectExcelFile()">选择Excel</button>
      <button class="btn" onclick="syncToExcel()">写入Excel</button>
      <button class="btn" onclick="startExcelMonitoring()" id="btnStartMon" disabled>开始监控</button>
      <button class="btn" onclick="stopExcelMonitoring()" id="btnStopMon" disabled>停止监控</button>

      <span class="hint">|</span>
      <span class="file-btn">
        <button class="btn" id="btnPickFolder" type="button">选择图片文件夹</button>
        <input type="file" id="folderPicker" class="file-overlay" webkitdirectory directory multiple accept="image/*" />
      </span>
      <span class="hint" id="folderInfo">未选择文件夹</span>
      <button class="btn warning" onclick="startIndexing()">索引未收录图片</button>
      <button class="btn danger" onclick="rebuildIndexing()">重建索引</button>
    </div>

    <!-- 目录选择 input 已内嵌在按钮上方作为透明覆盖层 -->

    <div class="status" id="status">初始化中...</div>
    <div class="progress" id="indexProgress">
      <div class="bar"><div class="fill" id="progressFill"></div></div>
      <div class="hint" id="progressText">准备中...</div>
    </div>

    <div class="sheet-wrap">
      <div id="sheet"></div>
      <div class="legend">说明：
        预览只读；路径只读；标签/分类使用分号分隔；状态为复选。
      </div>
    </div>
  </div>

  <!-- 依赖：Excel、下载辅助、jsuites/jspreadsheet（CDN） -->
  <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  <script src="https://unpkg.com/file-saver@2.0.5/dist/FileSaver.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/jsuites@5.2.12/dist/jsuites.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/jspreadsheet-ce@4.13.1/dist/jspreadsheet.js"></script>

  <!-- 项目脚本 -->
  <script src="file-storage-manager.js"></script>
  <script src="excel-sync-manager.js"></script>
  <script src="openrouter-api.js"></script>
  <script src="image-classifier.js"></script>
  <script src="images-metadata.js"></script>
  <script src="image-indexer.js"></script>

  <script>
    let sheet = null;
    let rawImages = [];
    let sources = { tags: [], app: [], page: [], control: [] };

    function setStatus(msg) {
      const el = document.getElementById('status');
      el.textContent = msg;
    }

    function uniq(arr) { return Array.from(new Set(arr.filter(Boolean))); }

    function categoriesToString(list) {
      if (!Array.isArray(list)) return '';
      return list.map(x => x && x.category).filter(Boolean).join(';');
    }

    function stringToCategories(str) {
      if (!str) return [];
      return str.split(';').map(s => s.trim()).filter(Boolean).map(s => ({ category: s, score: 1, keywords: [] }));
    }

    function mapToRows(images) {
      return images.map(meta => {
        return [
          (meta.preview || meta.path), // 0 预览：优先缩略图
          meta.path, // 1 路径
          meta.description || '', // 2 描述
          (meta.tags || []).join(';'), // 3 标签
          categoriesToString(meta.classifications?.appCategories), // 4 APP分类
          categoriesToString(meta.classifications?.pageTypes), // 5 页面类型
          categoriesToString(meta.classifications?.controlTypes), // 6 控件类型
          meta.lastUpdated || '', // 7 更新时间
          !!meta.indexed // 8 状态（布尔）
        ];
      });
    }

    function gatherSources(images) {
      const allTags = [];
      const app = new Set();
      const page = new Set();
      const control = new Set();
      images.forEach(img => {
        (img.tags || []).forEach(t => allTags.push(t));
        (img.classifications?.appCategories || []).forEach(c => app.add(c.category));
        (img.classifications?.pageTypes || []).forEach(c => page.add(c.category));
        (img.classifications?.controlTypes || []).forEach(c => control.add(c.category));
      });
      sources.tags = uniq(allTags);
      sources.app = Array.from(app);
      sources.page = Array.from(page);
      sources.control = Array.from(control);
    }

    function loadExternalScript(src) {
      return new Promise((resolve, reject) => {
        const s = document.createElement('script');
        s.src = src;
        s.async = true;
        s.onload = () => resolve(true);
        s.onerror = () => reject(new Error('脚本加载失败: ' + src));
        document.head.appendChild(s);
      });
    }

    async function tryLoadScripts(urls) {
      for (const url of urls) {
        try {
          // 先用 HEAD 探测是否存在，避免产生大量 404 噪音
          const ok = await fetch(url, { method: 'HEAD' }).then(r => r.ok).catch(() => false);
          if (!ok) continue;
          await loadExternalScript(url);
          return true;
        } catch (_) {}
      }
      return false;
    }

    async function ensureJspreadsheet() {
      if (window.jspreadsheet) return;
      // 依次尝试：CDN -> 本地 vendor -> 本地 libs
      const jsuitesTried = await (async () => {
        if (window.jSuites) return true;
        const okCdn = await tryLoadScripts([
          'https://cdn.jsdelivr.net/npm/jsuites@5.2.12/dist/jsuites.js'
        ]);
        if (okCdn || window.jSuites) return true;
        const okLocal = await tryLoadScripts([
          'vendor/jsuites.js',
          'vendor/jsuites.min.js',
          'libs/jsuites.js',
          'libs/jsuites.min.js'
        ]);
        return okLocal || !!window.jSuites;
      })();

      const jspOk = await (async () => {
        if (window.jspreadsheet) return true;
        const okCdn = await tryLoadScripts([
          'https://cdn.jsdelivr.net/npm/jspreadsheet-ce@4.13.1/dist/jspreadsheet.js'
        ]);
        if (okCdn || window.jspreadsheet) return true;
        const okLocal = await tryLoadScripts([
          'vendor/jspreadsheet.js',
          'vendor/jspreadsheet.min.js',
          'libs/jspreadsheet.js',
          'libs/jspreadsheet.min.js'
        ]);
        return okLocal || !!window.jspreadsheet;
      })();

      if (!jsuitesTried || !jspOk || !window.jspreadsheet) {
        console.warn('[TableEditor] CDN/本地均未加载成功，切换到简易表格模式');
        window.__JSS_OFFLINE__ = true;
      }
    }

    function safeImageUrl(p) {
      try {
        if (!p) return '';
        if (typeof p !== 'string') return '';
        // 1) dataURL 或 http(s) 直接使用
        if (p.startsWith('data:') || p.startsWith('http://') || p.startsWith('https://')) return p;
        // 2) 已经包含百分号编码，认为已编码，避免重复编码
        if (/%[0-9A-Fa-f]{2}/.test(p)) return p;
        const i = p.lastIndexOf('/');
        if (i === -1) return encodeURIComponent(p);
        const dir = p.slice(0, i + 1);
        const name = p.slice(i + 1);
        return encodeURI(dir) + encodeURIComponent(name);
      } catch { return p; }
    }

    function createFallbackGrid(dataRows) {
      const mount = document.getElementById('sheet');
      mount.innerHTML = '';
      const wrap = document.createElement('div');
      wrap.className = 'fallback-wrap';
      const table = document.createElement('table');
      table.className = 'fallback-table';
      const thead = document.createElement('thead');
      thead.innerHTML = '<tr>' + ['预览','路径','描述','标签','APP分类','页面类型','控件类型','最后更新时间','已索引']
        .map(h=>`<th>${h}</th>`).join('') + '</tr>';
      table.appendChild(thead);
      const tbody = document.createElement('tbody');
      dataRows.forEach(row => {
        const tr = document.createElement('tr');
        tr.dataset.path = row[1];
        const displayPath = (() => { try { return decodeURI(row[1]); } catch { return row[1]; } })();
        tr.innerHTML = `
          <td><img src="${safeImageUrl(row[0])}" alt="preview"></td>
          <td>${displayPath}</td>
          <td contenteditable="true">${row[2] || ''}</td>
          <td contenteditable="true">${row[3] || ''}</td>
          <td contenteditable="true">${row[4] || ''}</td>
          <td contenteditable="true">${row[5] || ''}</td>
          <td contenteditable="true">${row[6] || ''}</td>
          <td>${row[7] || ''}</td>
          <td><input type="checkbox" ${row[8] ? 'checked' : ''}></td>
        `;
        tbody.appendChild(tr);
      });
      table.appendChild(tbody);
      wrap.appendChild(table);
      mount.appendChild(wrap);

      // 事件绑定：编辑/勾选后保存
      wrap.addEventListener('blur', (e) => {
        const cell = e.target;
        if (cell.tagName === 'TD' && cell.isContentEditable) {
          const tr = cell.parentElement;
          saveFallbackRow(tr);
        }
      }, true);
      wrap.addEventListener('change', (e) => {
        if (e.target && e.target.type === 'checkbox') {
          const tr = e.target.closest('tr');
          saveFallbackRow(tr);
        }
      });
    }

    function saveFallbackRow(tr) {
      try {
        const tds = tr.children;
        const path = tr.dataset.path;
        const description = tds[2].textContent.trim();
        const tags = (tds[3].textContent || '').split(';').map(s=>s.trim()).filter(Boolean);
        const app = tds[4].textContent; const page = tds[5].textContent; const control = tds[6].textContent;
        const indexed = tds[8].querySelector('input')?.checked || false;
        const updates = {
          description,
          tags,
          classifications: {
            appCategories: stringToCategories(app),
            pageTypes: stringToCategories(page),
            controlTypes: stringToCategories(control)
          },
          indexed
        };
        const ok = window.imagesMetadata.updateImage(path, updates);
        if (ok) { tds[7].textContent = new Date().toISOString(); setStatus('已保存（离线简易表格）'); }
        else { setStatus('保存失败'); }
      } catch (e) {
        console.warn('保存失败:', e);
        setStatus('保存失败：' + e.message);
      }
    }

    async function createSheet() {
      if (sheet) {
        sheet.destroy();
        sheet = null;
      }

      const data = mapToRows(rawImages);

      await ensureJspreadsheet();
      if (!window.jspreadsheet) {
        createFallbackGrid(data);
        setStatus('已加载简易表格（离线模式）');
        return;
      }

      sheet = window.jspreadsheet(document.getElementById('sheet'), {
        data,
        freezeColumns: 2,
        tableOverflow: true,
        tableHeight: '70vh',
        defaultColWidth: 160,
        allowInsertRow: false,
        allowManualInsertRow: false,
        allowInsertColumn: false,
        allowDeleteRow: false,
        columns: [
          { title: '预览', type: 'image', width: 80, readOnly: true },
          { title: '路径', type: 'text', width: 360, readOnly: true },
          { title: '描述', type: 'text', width: 520 },
          { title: '标签', type: 'dropdown', multiple: true, width: 240, source: sources.tags, delimiter: ';' },
          { title: 'APP分类', type: 'dropdown', multiple: true, width: 180, source: sources.app, delimiter: ';' },
          { title: '页面类型', type: 'dropdown', multiple: true, width: 180, source: sources.page, delimiter: ';' },
          { title: '控件类型', type: 'dropdown', multiple: true, width: 180, source: sources.control, delimiter: ';' },
          { title: '最后更新时间', type: 'text', width: 180, readOnly: true },
          { title: '已索引', type: 'checkbox', width: 90 }
        ],
        onchange: (instance, cell, x, y, value) => {
          try {
            const row = instance.getRowData(y);
            const path = row[1];
            const updates = {
              description: row[2] || '',
              tags: (row[3] || '').split(';').map(s => s.trim()).filter(Boolean),
              classifications: {
                appCategories: stringToCategories(row[4]),
                pageTypes: stringToCategories(row[5]),
                controlTypes: stringToCategories(row[6])
              },
              indexed: !!row[8]
            };

            const ok = window.imagesMetadata.updateImage(path, updates);
            const now = new Date().toISOString();
            instance.setValueFromCoords(7, y, now, false);
            setStatus(ok ? '已保存更改并同步（localStorage/Excel）' : '保存失败');
          } catch (e) {
            console.warn('保存更新失败:', e);
            setStatus('保存失败：' + e.message);
          }
        }
      });
    }

    let selectedFiles = null; // 最近一次选择的文件夹内文件（FileList）

    async function reloadData() {
      setStatus('正在加载数据...');
      const images = (window.imagesMetadata && window.imagesMetadata.getAllImages()) || [];
      rawImages = images;
      gatherSources(images);
      await createSheet();
      setStatus(`已加载 ${images.length} 条记录`);
      refreshExcelButtons();
    }

    // ===== 选择文件夹并准备以该文件夹进行索引 =====
    // 如果需要，也可以在支持的浏览器上接入 File System Access API。
    // 这里选择最稳的方式：直接使用覆盖在按钮上的 <input type="file" webkitdirectory>。

    function folderNameFromFiles(fileList) {
      if (!fileList || fileList.length === 0) return null;
      const p = fileList[0].webkitRelativePath || fileList[0].name;
      const idx = p.indexOf('/');
      return idx > 0 ? p.slice(0, idx) : '(根目录)';
    }

    function handleFolderPickerChange(e) {
      const files = e.target.files;
      if (!files || files.length === 0) {
        selectedFiles = null;
        document.getElementById('folderInfo').textContent = '未选择文件夹';
        return;
      }
      selectedFiles = files; // 原生 FileList
      const name = folderNameFromFiles(files) || '已选文件夹';
      const imgCount = Array.from(files).filter(f => f.type.startsWith('image/')).length;
      document.getElementById('folderInfo').textContent = `${name}（共 ${imgCount} 个文件）`;
      setStatus('已选择文件夹：' + name);
    }

    // 快速搜索（前端过滤）
    function applySearch(term) {
      const q = (term || '').toLowerCase();
      const filtered = (!q ? rawImages : rawImages.filter(img => {
        const hay = [
          img.path,
          img.description,
          ...(img.tags || []),
          ...(img.classifications?.appCategories || []).map(x => x.category),
          ...(img.classifications?.pageTypes || []).map(x => x.category),
          ...(img.classifications?.controlTypes || []).map(x => x.category)
        ].join(' ').toLowerCase();
        return hay.includes(q);
      }));
      const data = mapToRows(filtered);
      if (sheet && sheet.setData) {
        sheet.setData(data);
      } else {
        createFallbackGrid(data);
      }
      setStatus(`已加载 ${filtered.length} 条记录（过滤）`);
    }

    // ===== 导入/导出 =====
    async function importFromJson() {
      try {
        setStatus('请选择JSON文件...');
        const ok = await window.imagesMetadata.importFromJson();
        if (ok) { reloadData(); setStatus('JSON数据导入成功'); } else { setStatus('导入取消或失败'); }
      } catch (e) { setStatus('导入失败：' + e.message); }
    }
    async function importFromCsv() {
      try {
        setStatus('请选择CSV文件...');
        const ok = await window.imagesMetadata.importFromCsv();
        if (ok) { reloadData(); setStatus('CSV数据导入成功'); } else { setStatus('导入取消或失败'); }
      } catch (e) { setStatus('导入失败：' + e.message); }
    }
    async function exportToJson() { try { await window.imagesMetadata.exportToJson(); setStatus('已导出JSON'); } catch(e){ setStatus('导出失败：'+e.message); } }
    async function exportToCsv() { try { await window.imagesMetadata.exportToCsv(); setStatus('已导出CSV'); } catch(e){ setStatus('导出失败：'+e.message); } }

    // ===== Excel 同步 =====
    function refreshExcelButtons() {
      const status = window.excelSyncManager?.getSyncStatus?.() || {};
      const startBtn = document.getElementById('btnStartMon');
      const stopBtn = document.getElementById('btnStopMon');
      startBtn.disabled = !status.hasFile || status.isMonitoring || !status.canWrite;
      stopBtn.disabled = !status.isMonitoring;
    }
    async function selectExcelFile() {
      try { await window.excelSyncManager.selectExcelFile(); refreshExcelButtons(); setStatus('Excel文件已选择'); }
      catch(e){ setStatus('选择Excel失败：'+e.message); }
    }
    async function syncToExcel() {
      try { await window.excelSyncManager.writeExcelData(window.imagesMetadata.images); setStatus('已写入Excel'); }
      catch(e){ setStatus('写入Excel失败：'+e.message); }
    }
    function startExcelMonitoring() {
      try {
        window.excelSyncManager.startMonitoring(async (newMap) => {
          window.imagesMetadata.images = newMap;
          await window.imagesMetadata.saveToStorage();
          reloadData();
          setStatus(`检测到Excel变化，已同步 ${newMap.size} 条记录`);
        });
        refreshExcelButtons();
        setStatus('Excel监控已开始');
      } catch (e) { setStatus('开始监控失败：' + e.message); }
    }
    function stopExcelMonitoring() { try { window.excelSyncManager.stopMonitoring(); refreshExcelButtons(); setStatus('Excel监控已停止'); } catch(e){ setStatus('停止监控失败：'+e.message); } }

    // ===== 调用索引器（API分析） =====
    function showProgress(show) {
      document.getElementById('indexProgress').style.display = show ? 'block' : 'none';
    }
    function updateProgress(p) {
      const fill = document.getElementById('progressFill');
      const text = document.getElementById('progressText');
      const total = p.total || 0; const done = p.processed || 0;
      const pct = total ? Math.round(done * 100 / total) : 0;
      fill.style.width = pct + '%';
      text.textContent = `进度：${done}/${total}（${pct}%） 当前：${p.currentFile || ''}`;
    }
    async function startIndexing() {
      try {
        setStatus('开始索引未收录图片...');
        showProgress(true);
        window.imageIndexer.setCallbacks(updateProgress, (r) => { showProgress(false); setStatus('索引完成'); reloadData(); });
        if (selectedFiles && selectedFiles.length) {
          await window.imageIndexer.startIndexingFromFiles(selectedFiles);
        } else {
          await window.imageIndexer.startIndexing('示例图片/');
        }
        // 回调里会刷新
      } catch (e) { showProgress(false); setStatus('索引失败：' + e.message); }
    }
    async function rebuildIndexing() {
      try {
        if (!confirm('将重建所有图片索引，耗时较长。确定继续？')) return;
        setStatus('开始重建索引...');
        showProgress(true);
        window.imageIndexer.setCallbacks(updateProgress, (r) => { showProgress(false); setStatus('重建完成'); reloadData(); });
        if (selectedFiles && selectedFiles.length) {
          await window.imageIndexer.rebuildIndexingFromFiles(selectedFiles);
        } else {
          await window.imageIndexer.rebuildIndexing('示例图片/');
        }
      } catch (e) { showProgress(false); setStatus('重建失败：' + e.message); }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', async () => {
      try {
        setStatus('初始化中...');
        if (window.fileStorageManager) await window.fileStorageManager.init();
        if (window.excelSyncManager) await window.excelSyncManager.init();
        const fp = document.getElementById('folderPicker');
        if (fp) fp.addEventListener('change', handleFolderPickerChange);
        // 兜底：按钮显式触发 input.click()，避免某些浏览器对覆盖 input 的点击不生效
        const pickBtn = document.getElementById('btnPickFolder');
        if (pickBtn && fp) {
          pickBtn.addEventListener('click', () => fp.click());
        }
        // 等待索引元数据完全就绪，防止读取到默认数据
        if (window.imagesMetadata && typeof window.imagesMetadata.waitForReady === 'function') {
          await window.imagesMetadata.waitForReady();
        }
        await reloadData();
      } catch (e) {
        setStatus('初始化失败：' + e.message);
      }
    });

    // 搜索输入
    document.addEventListener('input', (e) => {
      if (e.target && e.target.id === 'quickSearch') {
        applySearch(e.target.value);
      }
    });
  </script>
</body>
</html>
