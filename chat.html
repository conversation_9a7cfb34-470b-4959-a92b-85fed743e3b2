<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI体验设计问答 - NotebookLM风格</title>
    <link rel="stylesheet" href="chat-styles.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="top-nav">
        <div class="nav-content">
            <div class="nav-left">
                <div class="logo">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"></circle>
                        <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24"></path>
                    </svg>
                </div>
            </div>
            <div class="nav-right">
                <button class="nav-btn" id="settingsBtn">设置</button>
                <button class="nav-btn">断开</button>
            </div>
        </div>
    </nav>

    <!-- 主体内容 -->
    <div class="main-container">
        <!-- 左侧来源面板 -->
        <div class="sources-panel" id="sourcesPanel">
            <div class="sources-header">
                <h3>来源</h3>
                <div class="sources-actions">
                    <button class="upload-btn" id="uploadBtn" title="上传文档">
                        📄 上传
                    </button>
                    <button class="expand-btn" id="expandBtn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                </div>
                <input type="file" id="fileInput" multiple accept=".txt,.md,.pdf,.docx" style="display: none;">
            </div>

            <div class="sources-content" id="sourcesContent">
                <!-- 文档列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 右侧对话区域 -->
        <div class="chat-panel">
            <div class="chat-header">
                <h2>对话</h2>
            </div>

            <div class="chat-content" id="chatContent">
                <!-- 初始问题和回答将通过JavaScript动态生成 -->
            </div>

            <!-- 底部输入区域 -->
            <div class="chat-input-container">
                <div class="chat-input-box">
                    <input type="text"
                           id="chatInput"
                           placeholder="开始输入..."
                           class="chat-input" />
                    <div class="input-suggestions">
                        <button class="suggestion-btn">AI在金融应用中的作用是什么？</button>
                        <button class="suggestion-btn">如何优化投资应用的用户体验？</button>
                        <button class="suggestion-btn">设计AI产品时需要考虑哪些因素？</button>
                    </div>
                    <div class="input-counter" id="inputCounter">开始对话</div>
                </div>
            </div>
        </div>
    </div>

    <!-- API配置弹窗 -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>API设置</h3>
                <button class="close-btn" id="closeSettings">&times;</button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label for="apiKey">OpenRouter API Key</label>
                    <input type="password" id="apiKey" placeholder="输入你的OpenRouter API Key" />
                    <small class="help-text">从 <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a> 获取你的API密钥</small>
                </div>
                <div class="setting-group">
                    <label for="modelSelect">选择模型</label>
                    <select id="modelSelect">
                        <option value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</option>
                        <option value="google/gemini-2.5-flash-preview-09-2025">Gemini 2.5 Flash Preview</option>
                        <option value="openai/gpt-4-turbo">GPT-4 Turbo</option>
                        <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        <option value="google/gemini-pro">Gemini Pro</option>
                    </select>
                </div>
                <div class="setting-group">
                    <div class="api-status" id="apiStatus">
                        <span class="status-indicator"></span>
                        <span class="status-text">未连接</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="testConnection">测试连接</button>
                <button class="btn-primary" id="saveSettings">保存设置</button>
            </div>
        </div>
    </div>

    <!-- 加载PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        if (typeof pdfjsLib !== 'undefined') {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        }
    </script>

    <!-- 引入JavaScript文件 -->
    <script src="db-manager.js"></script>
    <script src="file-processor.js"></script>
    <script src="openrouter-api.js"></script>
    <script src="rag-engine.js"></script>
    <script src="markdown-parser.js"></script>
    <script src="pdf-viewer.js"></script>
    <script src="chat-script.js"></script>
</body>
</html>