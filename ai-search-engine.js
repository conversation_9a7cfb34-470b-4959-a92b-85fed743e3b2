/**
 * AI搜索引擎
 * 封装所有AI驱动的搜索功能，提供统一的AI搜索接口
 */
(function(global) {
    // 防止重复声明
    if (global.AISearchEngine) {
        console.warn('AISearchEngine already defined, skipping redefinition');
        return;
    }

class AISearchEngine {
    constructor() {
        this.isInitialized = false;
        this.searchCache = new Map();
        this.queryHistory = [];
        this.performanceMetrics = {
            totalSearches: 0,
            avgResponseTime: 0,
            successRate: 0,
            userSatisfaction: 0
        };

        // 搜索配置
        this.config = {
            maxResults: 20,
            minRelevanceScore: 0.1,
            cacheExpiry: 300000, // 5分钟
            enableLearning: true,
            fallbackToTraditional: true
        };
    }

    /**
     * 初始化AI搜索引擎
     */
    async initialize() {
        if (this.isInitialized) return;

        try {
            // 检查依赖项
            await this.checkDependencies();

            // 加载搜索偏好
            this.loadUserPreferences();

            // 初始化性能监控
            this.initializeMetrics();

            this.isInitialized = true;
            console.log('AI搜索引擎初始化完成');
        } catch (error) {
            console.error('AI搜索引擎初始化失败:', error);
            throw error;
        }
    }

    /**
     * 检查依赖项
     */
    async checkDependencies() {
        const dependencies = [
            'window.openrouterAPI',
            'window.semanticSearchEngine',
            'window.imagesMetadata',
            'window.promptTemplates'
        ];

        for (const dep of dependencies) {
            if (!this.getNestedProperty(window, dep)) {
                throw new Error(`缺少依赖项: ${dep}`);
            }
        }

        // 检查AI API可用性
        if (!window.openrouterAPI.apiKey) {
            console.warn('AI API未配置，部分功能将受限');
        }
    }

    /**
     * 智能搜索主入口
     * @param {string} query - 用户查询
     * @param {Object} options - 搜索选项
     * @returns {Promise<Object>} 搜索结果
     */
    async search(query, options = {}) {
        const startTime = performance.now();

        try {
            // 前处理
            const normalizedQuery = this.preprocessQuery(query);
            const searchOptions = { ...this.config, ...options };

            // 检查缓存
            const cacheKey = this.generateCacheKey(normalizedQuery, searchOptions);
            const cachedResult = this.getFromCache(cacheKey);
            if (cachedResult) {
                console.log('使用缓存搜索结果');
                this.recordMetrics(startTime, true, 'cache');
                return cachedResult;
            }

            // 获取图片数据
            const imageData = this.getImageData();
            if (!imageData || imageData.length === 0) {
                throw new Error('没有可搜索的图片数据');
            }

            // 智能搜索策略选择
            const searchStrategy = this.selectSearchStrategy(normalizedQuery, searchOptions);
            console.log(`使用搜索策略: ${searchStrategy}`);

            let searchResults;

            switch (searchStrategy) {
                case 'ai_semantic':
                    searchResults = await this.performAISemanticSearch(normalizedQuery, imageData, searchOptions);
                    break;
                case 'hybrid':
                    searchResults = await this.performHybridSearch(normalizedQuery, imageData, searchOptions);
                    break;
                case 'traditional':
                default:
                    searchResults = await this.performTraditionalSearch(normalizedQuery, imageData, searchOptions);
                    break;
            }

            // 后处理
            const finalResults = this.postProcessResults(searchResults, normalizedQuery, searchOptions);

            // 缓存结果
            this.saveToCache(cacheKey, finalResults);

            // 记录搜索历史
            this.recordSearch(normalizedQuery, finalResults, searchStrategy);

            // 性能统计
            this.recordMetrics(startTime, true, searchStrategy);

            return finalResults;

        } catch (error) {
            console.error('AI搜索失败:', error);
            this.recordMetrics(startTime, false, 'error');

            // 降级到传统搜索
            if (options.fallbackToTraditional !== false) {
                console.log('降级到传统搜索');
                return await this.performTraditionalSearch(query, this.getImageData(), options);
            }

            throw error;
        }
    }

    /**
     * AI语义搜索
     */
    async performAISemanticSearch(query, imageData, options) {
        try {
            const api = window.openrouterAPI;

            // 使用AI分析查询意图
            const queryAnalysis = await api.analyzeQueryIntent(query);
            console.log('查询意图分析:', queryAnalysis);

            // AI语义匹配
            const searchResults = await api.semanticSearch(query, imageData);

            // 语义搜索引擎增强
            const semanticResults = await window.semanticSearchEngine.semanticSearch(query, imageData);

            // 融合AI和语义搜索结果
            const fusedResults = this.fuseSearchResults([
                { results: searchResults.matches || [], weight: 0.6, source: 'ai' },
                { results: semanticResults, weight: 0.4, source: 'semantic' }
            ]);

            return {
                query: query,
                results: fusedResults,
                strategy: 'ai_semantic',
                queryAnalysis: queryAnalysis,
                metadata: {
                    totalResults: fusedResults.length,
                    searchTime: performance.now(),
                    sources: ['ai', 'semantic']
                }
            };

        } catch (error) {
            console.error('AI语义搜索失败:', error);
            throw error;
        }
    }

    /**
     * 混合搜索
     */
    async performHybridSearch(query, imageData, options) {
        try {
            // 并行执行多种搜索
            const [aiResults, semanticResults, traditionalResults] = await Promise.allSettled([
                this.performAISemanticSearch(query, imageData, options).catch(() => null),
                window.semanticSearchEngine.semanticSearch(query, imageData),
                window.semanticSearchEngine.search(query, imageData)
            ]);

            // 提取成功的结果
            const validResults = [];

            if (aiResults.status === 'fulfilled' && aiResults.value) {
                validResults.push({
                    results: aiResults.value.results,
                    weight: 0.5,
                    source: 'ai'
                });
            }

            if (semanticResults.status === 'fulfilled') {
                validResults.push({
                    results: semanticResults.value,
                    weight: 0.3,
                    source: 'semantic'
                });
            }

            if (traditionalResults.status === 'fulfilled') {
                validResults.push({
                    results: traditionalResults.value,
                    weight: 0.2,
                    source: 'traditional'
                });
            }

            // 融合结果
            const fusedResults = this.fuseSearchResults(validResults);

            return {
                query: query,
                results: fusedResults,
                strategy: 'hybrid',
                metadata: {
                    totalResults: fusedResults.length,
                    searchTime: performance.now(),
                    sources: validResults.map(r => r.source)
                }
            };

        } catch (error) {
            console.error('混合搜索失败:', error);
            throw error;
        }
    }

    /**
     * 传统搜索
     */
    async performTraditionalSearch(query, imageData, options) {
        try {
            const results = await window.semanticSearchEngine.search(query, imageData);

            return {
                query: query,
                results: results,
                strategy: 'traditional',
                metadata: {
                    totalResults: results.length,
                    searchTime: performance.now(),
                    sources: ['traditional']
                }
            };

        } catch (error) {
            console.error('传统搜索失败:', error);
            throw error;
        }
    }

    /**
     * 查询预处理
     */
    preprocessQuery(query) {
        if (!query || typeof query !== 'string') {
            return '';
        }

        // 去除多余空格
        let processed = query.trim().replace(/\s+/g, ' ');

        // 纠正常见拼写错误
        const corrections = {
            '支付宝': '支付',
            '微信': '支付',
            '登陆': '登录',
            '设定': '设置'
        };

        for (const [wrong, correct] of Object.entries(corrections)) {
            processed = processed.replace(new RegExp(wrong, 'gi'), correct);
        }

        return processed;
    }

    /**
     * 搜索策略选择
     */
    selectSearchStrategy(query, options) {
        // 强制指定策略
        if (options.strategy) {
            return options.strategy;
        }

        // AI不可用时使用传统搜索
        if (!window.openrouterAPI.apiKey) {
            return 'traditional';
        }

        // 分析查询复杂度
        const complexity = this.analyzeQueryComplexity(query);

        if (complexity.score > 0.7) {
            return 'ai_semantic';
        } else if (complexity.score > 0.4) {
            return 'hybrid';
        } else {
            return 'traditional';
        }
    }

    /**
     * 分析查询复杂度
     */
    analyzeQueryComplexity(query) {
        let score = 0;
        const features = {
            length: 0,
            conjunctions: 0,
            modifiers: 0,
            specificity: 0
        };

        // 长度因子
        features.length = Math.min(query.length / 50, 1);
        score += features.length * 0.2;

        // 连接词
        const conjunctions = ['和', '或', '的', '与', '及', '以及'];
        features.conjunctions = conjunctions.filter(c => query.includes(c)).length;
        score += Math.min(features.conjunctions / 3, 1) * 0.3;

        // 修饰词
        const modifiers = ['深色', '浅色', '现代', '简洁', '复杂', '高端', '简单'];
        features.modifiers = modifiers.filter(m => query.includes(m)).length;
        score += Math.min(features.modifiers / 3, 1) * 0.3;

        // 具体性
        const specificTerms = ['流程', '界面', '页面', '功能', '设计', '布局'];
        features.specificity = specificTerms.filter(t => query.includes(t)).length;
        score += Math.min(features.specificity / 3, 1) * 0.2;

        return {
            score: Math.min(score, 1),
            features
        };
    }

    /**
     * 融合多个搜索结果
     */
    fuseSearchResults(resultSets) {
        const fusedMap = new Map();
        let totalWeight = 0;

        // 计算总权重
        resultSets.forEach(set => {
            totalWeight += set.weight;
        });

        // 归一化权重并融合结果
        resultSets.forEach(set => {
            const normalizedWeight = set.weight / totalWeight;

            set.results.forEach(result => {
                const key = result.path || result.id || result.src;
                if (!key) return;

                if (fusedMap.has(key)) {
                    const existing = fusedMap.get(key);
                    existing.fusedScore += (result.relevanceScore || result.semanticScore || 0.5) * normalizedWeight;
                    existing.sources.push(set.source);
                    existing.matchReasons = [
                        ...(existing.matchReasons || []),
                        ...(result.matchReasons || result.semanticReasons || [])
                    ];
                } else {
                    fusedMap.set(key, {
                        ...result,
                        fusedScore: (result.relevanceScore || result.semanticScore || 0.5) * normalizedWeight,
                        sources: [set.source],
                        matchReasons: result.matchReasons || result.semanticReasons || []
                    });
                }
            });
        });

        // 转换为数组并排序
        const fusedResults = Array.from(fusedMap.values())
            .filter(result => result.fusedScore > this.config.minRelevanceScore)
            .sort((a, b) => b.fusedScore - a.fusedScore)
            .slice(0, this.config.maxResults);

        return fusedResults;
    }

    /**
     * 结果后处理
     */
    postProcessResults(searchResults, query, options) {
        const results = searchResults.results || [];

        // 增强结果信息
        const enhancedResults = results.map(result => ({
            ...result,
            searchQuery: query,
            timestamp: Date.now(),
            explanation: this.generateResultExplanation(result, query),
            confidence: result.fusedScore || result.relevanceScore || result.semanticScore || 0
        }));

        // 多样性处理
        const diversifiedResults = this.diversifyResults(enhancedResults, options);

        return {
            ...searchResults,
            results: diversifiedResults,
            summary: this.generateSearchSummary(diversifiedResults, query),
            suggestions: this.generateSearchSuggestions(query, diversifiedResults)
        };
    }

    /**
     * 生成结果解释
     */
    generateResultExplanation(result, query) {
        const reasons = [];

        if (result.matchReasons && result.matchReasons.length > 0) {
            reasons.push(...result.matchReasons);
        }

        if (result.sources && result.sources.length > 1) {
            reasons.push(`多源匹配(${result.sources.join(', ')})`);
        }

        if (result.semanticSimilarity > 0.8) {
            reasons.push('高语义相似度');
        }

        return reasons.length > 0 ? reasons.join('; ') : '内容匹配';
    }

    /**
     * 结果多样性处理
     */
    diversifyResults(results, options) {
        if (!options.enableDiversity) {
            return results;
        }

        const diversified = [];
        const seenCategories = new Set();
        const categoryLimit = 3; // 每个分类最多保留的结果数

        for (const result of results) {
            const categories = [
                ...(result.classifications?.app || []),
                ...(result.classifications?.page || []),
                ...(result.classifications?.control || [])
            ];

            let shouldAdd = true;
            let categoryCount = 0;

            for (const category of categories) {
                if (seenCategories.has(category)) {
                    categoryCount++;
                    if (categoryCount >= categoryLimit) {
                        shouldAdd = false;
                        break;
                    }
                }
            }

            if (shouldAdd) {
                diversified.push(result);
                categories.forEach(cat => seenCategories.add(cat));
            }

            if (diversified.length >= this.config.maxResults * 0.8) {
                break;
            }
        }

        // 如果多样性处理后结果太少，补充原始结果
        if (diversified.length < Math.min(5, results.length)) {
            const remaining = results.filter(r => !diversified.some(d => d.path === r.path));
            diversified.push(...remaining.slice(0, Math.min(5, this.config.maxResults) - diversified.length));
        }

        return diversified;
    }

    /**
     * 生成搜索摘要
     */
    generateSearchSummary(results, query) {
        const totalResults = results.length;
        const avgConfidence = results.reduce((sum, r) => sum + (r.confidence || 0), 0) / totalResults;

        const categories = new Set();
        results.forEach(result => {
            if (result.classifications) {
                [...(result.classifications.app || []),
                 ...(result.classifications.page || []),
                 ...(result.classifications.control || [])].forEach(cat => categories.add(cat));
            }
        });

        return {
            totalResults,
            avgConfidence: Math.round(avgConfidence * 100) / 100,
            topCategories: Array.from(categories).slice(0, 5),
            searchTips: this.generateSearchTips(query, results)
        };
    }

    /**
     * 生成搜索建议
     */
    generateSearchSuggestions(query, results) {
        const suggestions = [];

        // 基于结果的相关建议
        const relatedTerms = new Set();
        results.slice(0, 5).forEach(result => {
            (result.tags || []).forEach(tag => {
                if (tag.length > 1 && !query.includes(tag)) {
                    relatedTerms.add(tag);
                }
            });
        });

        Array.from(relatedTerms).slice(0, 3).forEach(term => {
            suggestions.push({
                text: `${query} ${term}`,
                type: 'related',
                reason: '基于搜索结果的相关建议'
            });
        });

        // 搜索优化建议
        if (results.length === 0) {
            suggestions.push({
                text: query.split(' ')[0], // 简化查询
                type: 'simplify',
                reason: '尝试更简单的关键词'
            });
        } else if (results.length > 15) {
            suggestions.push({
                text: `${query} 详细`,
                type: 'refine',
                reason: '添加更多限定词以缩小范围'
            });
        }

        return suggestions.slice(0, 5);
    }

    /**
     * 生成搜索提示
     */
    generateSearchTips(query, results) {
        const tips = [];

        if (results.length === 0) {
            tips.push('尝试使用更通用的关键词');
            tips.push('检查是否有拼写错误');
        } else if (results.length > 20) {
            tips.push('添加更多限定词以获得更精确的结果');
            tips.push('使用"和"连接多个关键词');
        }

        if (query.length < 3) {
            tips.push('使用更详细的描述可以获得更好的结果');
        }

        return tips;
    }

    /**
     * 获取图片数据
     */
    getImageData() {
        if (window.imagesMetadata && typeof window.imagesMetadata.getAllImages === 'function') {
            return window.imagesMetadata.getAllImages();
        }
        return [];
    }

    /**
     * 缓存管理
     */
    generateCacheKey(query, options) {
        return `search_${btoa(query)}_${JSON.stringify(options)}`;
    }

    getFromCache(key) {
        const cached = this.searchCache.get(key);
        if (cached && Date.now() - cached.timestamp < this.config.cacheExpiry) {
            return cached.data;
        }
        return null;
    }

    saveToCache(key, data) {
        this.searchCache.set(key, {
            data: data,
            timestamp: Date.now()
        });

        // 清理过期缓存
        if (this.searchCache.size > 50) {
            this.cleanupCache();
        }
    }

    cleanupCache() {
        const now = Date.now();
        for (const [key, value] of this.searchCache.entries()) {
            if (now - value.timestamp > this.config.cacheExpiry) {
                this.searchCache.delete(key);
            }
        }
    }

    /**
     * 搜索历史记录
     */
    recordSearch(query, results, strategy) {
        this.queryHistory.unshift({
            query,
            timestamp: Date.now(),
            resultCount: results.results ? results.results.length : 0,
            strategy,
            success: true
        });

        // 保持历史记录在合理范围内
        if (this.queryHistory.length > 100) {
            this.queryHistory = this.queryHistory.slice(0, 100);
        }

        // 保存到本地存储
        try {
            localStorage.setItem('ai_search_history', JSON.stringify(this.queryHistory.slice(0, 20)));
        } catch (error) {
            console.warn('保存搜索历史失败:', error);
        }
    }

    /**
     * 性能指标记录
     */
    recordMetrics(startTime, success, strategy) {
        const responseTime = performance.now() - startTime;

        this.performanceMetrics.totalSearches++;
        this.performanceMetrics.avgResponseTime =
            (this.performanceMetrics.avgResponseTime * (this.performanceMetrics.totalSearches - 1) + responseTime) /
            this.performanceMetrics.totalSearches;

        if (success) {
            this.performanceMetrics.successRate =
                (this.performanceMetrics.successRate * (this.performanceMetrics.totalSearches - 1) + 1) /
                this.performanceMetrics.totalSearches;
        } else {
            this.performanceMetrics.successRate =
                (this.performanceMetrics.successRate * (this.performanceMetrics.totalSearches - 1)) /
                this.performanceMetrics.totalSearches;
        }
    }

    /**
     * 初始化性能监控
     */
    initializeMetrics() {
        try {
            const saved = localStorage.getItem('ai_search_metrics');
            if (saved) {
                this.performanceMetrics = { ...this.performanceMetrics, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.warn('加载性能指标失败:', error);
        }
    }

    /**
     * 加载用户偏好
     */
    loadUserPreferences() {
        try {
            const preferences = localStorage.getItem('ai_search_preferences');
            if (preferences) {
                this.config = { ...this.config, ...JSON.parse(preferences) };
            }
        } catch (error) {
            console.warn('加载用户偏好失败:', error);
        }
    }

    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };

        try {
            localStorage.setItem('ai_search_preferences', JSON.stringify(this.config));
        } catch (error) {
            console.warn('保存用户偏好失败:', error);
        }
    }

    /**
     * 获取搜索统计
     */
    getSearchStats() {
        return {
            ...this.performanceMetrics,
            cacheSize: this.searchCache.size,
            historySize: this.queryHistory.length,
            isInitialized: this.isInitialized
        };
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.searchCache.clear();
        this.queryHistory = [];
        this.isInitialized = false;
    }

    /**
     * 辅助方法：获取嵌套属性
     */
    getNestedProperty(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }
}

// 导出全局实例
global.AISearchEngine = AISearchEngine;
global.aiSearchEngine = new AISearchEngine();
})(window);
