// 图片画廊管理器
class GalleryManager {
    constructor() {
        this.currentType = null;
        this.currentCategory = null;
        this.currentImages = [];
        this.currentIndex = 0;

        // 兼容旧版本的静态数据
        this.fallbackCategoryImages = {
            '投资': ['示例图片/Kit iOS 157.png', '示例图片/Kit iOS 77.png'],
            '详情页': ['示例图片/MLS iOS 13.png', '示例图片/MLS iOS 53.png'],
            '日历': ['示例图片/Ultrahuman iOS 26.png', '示例图片/Ultrahuman iOS 27.png'],
            '教育': ['示例图片/Duolingo iOS 29451.png', '示例图片/Atoms iOS 74.png'],
            '订阅': ['示例图片/Fuse iOS 115.png', '示例图片/Fuse iOS 15.png'],
            '支付': ['示例图片/Fuse iOS 61.png', '示例图片/Fuse iOS 62.png'],
            '广告促销': ['示例图片/Fuse iOS 66.png', '示例图片/Rivian iOS 55.png'],
            '运营': ['示例图片/Ultrahuman iOS 32.png', '示例图片/Ultrahuman iOS 59.png'],
            '弹窗': ['示例图片/Kit iOS 157.png', '示例图片/Kit iOS 77.png'],
            '列表': ['示例图片/MLS iOS 13.png', '示例图片/MLS iOS 53.png'],
            '按钮': ['示例图片/Fuse iOS 61.png', '示例图片/Fuse iOS 62.png'],
            '导航': ['示例图片/Ultrahuman iOS 26.png', '示例图片/Ultrahuman iOS 27.png'],
            '注册流程': ['示例图片/Duolingo iOS 29451.png', '示例图片/Atoms iOS 74.png'],
            '支付流程': ['示例图片/Fuse iOS 115.png', '示例图片/Fuse iOS 15.png'],
            '引导流程': ['示例图片/Fuse iOS 66.png', '示例图片/Rivian iOS 55.png']
        };
    }

    // 初始化画廊
    async initialize() {
        await this.loadDependencies();

        // 确保分类数据是最新的
        if (window.imageClassifier && window.imagesMetadata && window.imagesMetadata.recalculateClassifications) {
            console.log('Gallery: 重新计算图片分类...');
            window.imagesMetadata.recalculateClassifications();
        }

        this.parseUrlParams();
        await this.loadImages();
    }

    // 加载依赖脚本
    async loadDependencies() {
        const scripts = [
            'openrouter-api.js',
            'image-classifier.js',
            'images-metadata.js',
            'image-indexer.js',
            'semantic-search.js',
            'prompt-templates.js',
            'ai-search-engine.js'
        ];

        for (const script of scripts) {
            if (!document.querySelector(`script[src="${script}"]`)) {
                await this.loadScript(script);
            }
        }

        // 等待所有全局实例初始化完成
        await this.waitForDependencies();
    }

    // 动态加载脚本
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = () => {
                console.warn(`Failed to load ${src}, using fallback`);
                resolve();
            };
            document.head.appendChild(script);
        });
    }

    // 等待依赖项初始化
    async waitForDependencies() {
        const maxWaitTime = 5000; // 最大等待5秒
        const checkInterval = 100; // 每100毫秒检查一次
        const startTime = Date.now();

        const requiredDependencies = [
            'window.semanticSearchEngine',
            'window.promptTemplates',
            'window.imagesMetadata',
            'window.imageClassifier'
        ];

        while (Date.now() - startTime < maxWaitTime) {
            const allReady = requiredDependencies.every(dep => {
                const obj = this.getNestedProperty(window, dep);
                return obj !== undefined && obj !== null;
            });

            if (allReady) {
                console.log('Gallery: 所有依赖项已就绪');
                return;
            }

            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }

        console.warn('Gallery: 依赖项等待超时，继续执行');
    }

    // 获取嵌套属性的辅助方法
    getNestedProperty(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    // 解析URL参数
    parseUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        this.currentType = urlParams.get('type');
        this.currentCategory = urlParams.get('category');
        this.searchQuery = urlParams.get('search');
        this.searchMode = urlParams.get('mode') || 'auto';
        this.originalQuery = urlParams.get('original');

        console.log('解析URL参数:', {
            type: this.currentType,
            category: this.currentCategory,
            search: this.searchQuery,
            mode: this.searchMode,
            original: this.originalQuery
        });

        // 兼容旧版本参数
        if (!this.currentType && !this.currentCategory && !this.searchQuery) {
            this.currentCategory = urlParams.get('category') || '图片展示';
        }
    }

    // 加载图片数据
    async loadImages() {
        console.log(`加载图片数据 - Type: ${this.currentType}, Category: ${this.currentCategory}, Search: ${this.searchQuery}`);

        // 如果是搜索模式
        if (this.searchQuery) {
            await this.performSearch(this.searchQuery);
            return;
        }

        if (window.imagesMetadata && this.currentType && this.currentCategory) {
            // 使用新的分类系统
            const images = window.imagesMetadata.getImagesByCategory(this.currentType, this.currentCategory);
            console.log(`找到 ${images.length} 张分类图片:`, images.map(img => img.path));
            this.currentImages = this.dedupePaths(images.map(img => img.path));

            // 如果找到了分类图片，就不要补充其他图片
            if (this.currentImages.length > 0) {
                console.log('使用分类图片，不补充其他图片');
                return;
            }
        }

        if (this.currentCategory && this.fallbackCategoryImages[this.currentCategory]) {
            // 使用兼容数据
            console.log('使用兼容分类数据');
            this.currentImages = this.fallbackCategoryImages[this.currentCategory];
            return;
        }

        // 只有在没有找到任何分类图片时才显示所有图片
        console.log('没有找到分类图片，显示所有图片');
        if (window.imagesMetadata) {
            const allImages = window.imagesMetadata.getAllImages();
            this.currentImages = allImages.map(img => img.path);
        } else {
            this.currentImages = this.getAllFallbackImages();
        }
    }

    // 执行搜索
    async performSearch(query) {
        console.log(`执行搜索: "${query}", 模式: ${this.searchMode}`);

        try {
            // 获取所有图片数据
            let allImages = [];
            if (window.imagesMetadata) {
                allImages = window.imagesMetadata.getAllImages();
            } else {
                // 使用兼容数据
                allImages = this.getAllFallbackImages().map(path => ({
                    path,
                    description: '图片描述',
                    tags: ['应用界面']
                }));
            }

            // 使用新的AI搜索引擎（如果可用）
            if (window.AISearchEngine && this.searchMode !== 'traditional') {
                try {
                    // 复用全局AI搜索引擎实例，避免重复构造
                    if (!window.aiSearchEngine) {
                        window.aiSearchEngine = new window.AISearchEngine();
                    }
                    const aiSearchEngine = window.aiSearchEngine;

                    // 确保AI搜索引擎已初始化
                    if (!aiSearchEngine.isInitialized) {
                        await aiSearchEngine.initialize();
                    }

                    const searchResults = await aiSearchEngine.search(query, {
                        images: allImages,
                        mode: this.searchMode,
                        maxResults: 50
                    });

                    this.searchResults = searchResults.results || searchResults;
                    this.currentImages = this.dedupePaths(this.searchResults.map(result => result.path));

                    console.log(`AI搜索完成，找到 ${this.searchResults.length} 个结果`, {
                        mode: this.searchMode,
                        hasConfidence: this.searchResults.some(r => r.confidence)
                    });
                    return;
                } catch (error) {
                    console.error('AI搜索引擎初始化或执行失败:', error);
                    // 继续执行后续的回退逻辑
                }
            }

            // 回退到语义搜索引擎
            if (window.semanticSearchEngine) {
                const searchResults = await window.semanticSearchEngine.search(query, allImages);
                this.searchResults = searchResults;
                this.currentImages = this.dedupePaths(searchResults.map(result => result.path));
                console.log(`语义搜索完成，找到 ${searchResults.length} 个结果`);
                return;
            }

            // 最后的回退方案
            console.warn('所有搜索引擎都不可用，使用基础搜索');
            this.performBasicSearch(query);

        } catch (error) {
            console.error('搜索失败:', error);
            this.performBasicSearch(query);
        }
    }

    // 基础搜索（兼容模式）
    performBasicSearch(query) {
        console.log(`执行基础搜索: "${query}"`);

        const lowerQuery = query.toLowerCase();
        const allImages = this.getAllFallbackImages();

        // 简单的文件名匹配 + 去重
        const matched = allImages.filter(imagePath => {
            const fileName = imagePath.toLowerCase();
            return fileName.includes(lowerQuery) ||
                   lowerQuery.split(' ').some(word => fileName.includes(word));
        });
        this.currentImages = this.dedupePaths(matched);

        console.log(`基础搜索完成，找到 ${this.currentImages.length} 个结果`);
    }

    // 获取所有兼容图片
    getAllFallbackImages() {
        return [
            '示例图片/Atoms iOS 74.png',
            '示例图片/Duolingo iOS 29451.png',
            '示例图片/Fuse iOS 115.png',
            '示例图片/Fuse iOS 15.png',
            '示例图片/Fuse iOS 61.png',
            '示例图片/Fuse iOS 62.png',
            '示例图片/Fuse iOS 66.png',
            '示例图片/Kit iOS 157.png',
            '示例图片/Kit iOS 77.png',
            '示例图片/MLS iOS 13.png',
            '示例图片/MLS iOS 53.png',
            '示例图片/Rivian iOS 55.png',
            '示例图片/Ultrahuman iOS 26.png',
            '示例图片/Ultrahuman iOS 27.png',
            '示例图片/Ultrahuman iOS 32.png',
            '示例图片/Ultrahuman iOS 59.png'
        ];
    }

    // 获取页面标题
    getPageTitle() {
        if (this.searchQuery) {
            const imageCount = this.currentImages.length;
            const modeText = this.searchMode === 'ai_semantic' ? ' (AI语义)' :
                            this.searchMode === 'hybrid' ? ' (混合)' :
                            this.searchMode === 'traditional' ? ' (传统)' : '';

            let title = `搜索结果 - "${this.searchQuery}"${modeText} (${imageCount}张图片)`;

            // 如果有原始查询，显示两者的对比
            if (this.originalQuery && this.originalQuery !== this.searchQuery) {
                title = `搜索结果 - "${this.originalQuery}" → "${this.searchQuery}"${modeText} (${imageCount}张图片)`;
            }

            return title;
        }
        if (this.currentType && this.currentCategory) {
            const typeNames = { 'app': 'APP分类', 'page': '页面类型', 'control': '控件' };
            const imageCount = this.currentImages.length;
            return `${typeNames[this.currentType]} - ${this.currentCategory} (${imageCount}张图片)`;
        }
        return this.currentCategory || '图片展示';
    }

    // 获取当前图片列表
    getCurrentImages() {
        return this.currentImages;
    }

    // 获取图片元数据
    getImageMetadata(imagePath) {
        if (window.imagesMetadata) {
            return window.imagesMetadata.getImage(imagePath);
        }
        return null;
    }

    // 去重工具：按小写规范化路径去重，保持首次出现顺序
    dedupePaths(paths) {
        const seen = new Set();
        const result = [];
        for (const p of paths || []) {
            const key = String(p).trim().toLowerCase();
            if (!seen.has(key)) { seen.add(key); result.push(p); }
        }
        return result;
    }

}

// 创建全局实例
const galleryManager = new GalleryManager();

// 兼容函数
function getAllImages() {
    return galleryManager.getAllFallbackImages();
}

// 获取URL参数中的类别信息（兼容函数）
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// 设置页面标题
function setPageTitle() {
    const pageTitle = document.getElementById('pageTitle');
    pageTitle.textContent = galleryManager.getPageTitle();
}

// 创建瀑布流图片项
function createMasonryItem(imagePath, index) {
    const item = document.createElement('div');
    item.className = 'masonry-item';
    item.dataset.index = index;

    // 检查是否有搜索结果的置信度信息
    const searchResult = galleryManager.searchResults?.find(r => r.path === imagePath);
    if (searchResult && searchResult.confidence) {
        item.dataset.confidence = searchResult.confidence;
        item.title = `相关度: ${Math.round(searchResult.confidence * 100)}%`;

        // 添加置信度指示器
        const confidenceBar = document.createElement('div');
        confidenceBar.className = 'confidence-indicator';
        confidenceBar.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right,
                #ff6b6b ${searchResult.confidence * 100}%,
                rgba(255,255,255,0.3) ${searchResult.confidence * 100}%);
            z-index: 2;
        `;
        item.appendChild(confidenceBar);
    }

    const img = document.createElement('img');
    img.src = imagePath;
    img.alt = `Gallery image ${index + 1}`;
    img.loading = 'lazy'; // 懒加载

    // 立即添加图片到DOM，这样点击事件就能正常工作
    item.appendChild(img);

    // 图片加载失败时的处理
    img.onerror = function() {
        console.warn(`Failed to load image: ${imagePath}`);
        item.style.display = 'none';
    };

    return item;
}

// 根据类别获取图片列表（兼容函数）
function getImagesForCategory(category) {
    return galleryManager.getCurrentImages();
}

// 渲染瀑布流
function renderMasonry() {
    const masonryGrid = document.getElementById('masonryGrid');
    masonryGrid.innerHTML = '';

    const currentImages = galleryManager.getCurrentImages();

    console.log(`渲染瀑布流 - 当前图片数量: ${currentImages.length}`, currentImages);

    if (currentImages.length === 0) {
        // 重置为默认瀑布流布局
        masonryGrid.className = 'masonry-grid';

        // 根据是否为搜索模式显示不同的提示
        if (galleryManager.searchQuery) {
            const modeDescription = galleryManager.searchMode === 'ai_semantic' ? 'AI语义分析' :
                                  galleryManager.searchMode === 'hybrid' ? '混合搜索算法' :
                                  galleryManager.searchMode === 'traditional' ? '传统关键词匹配' :
                                  '智能搜索系统';

            masonryGrid.innerHTML = `
                <div class="empty-state">
                    <p>未找到相关图片</p>
                    <small>使用${modeDescription}没有找到与"${galleryManager.searchQuery}"相关的图片</small>
                    <div class="search-suggestions-empty">
                        <p>优化建议：</p>
                        <ul>
                            <li><strong>具体功能</strong>：“支付界面”、“设置页面”、“登录流程”</li>
                            <li><strong>视觉特征</strong>：“深色主题”、“卡片布局”、“数据图表”</li>
                            <li><strong>应用类型</strong>：“金融应用”、“健康监测”、“教育平台”</li>
                            <li><strong>复合查询</strong>：“蓝色的金融支付流程”、“带进度条的设置页”</li>
                        </ul>
                        <p><em>当前使用: ${modeDescription}</em></p>
                    </div>
                </div>
            `;
        } else {
            masonryGrid.innerHTML = `
                <div class="empty-state">
                    <p>暂无图片</p>
                    <small>该分类下暂无图片内容，请尝试索引图片或检查分类配置</small>
                </div>
            `;
        }
        return;
    }

    // 根据图片数量选择布局方式
    if (currentImages.length <= 6) {
        // 少量图片使用网格布局
        masonryGrid.className = `masonry-grid-small images-${currentImages.length}`;
        console.log(`使用小网格布局，图片数量: ${currentImages.length}`);
    } else {
        // 大量图片使用瀑布流布局
        masonryGrid.className = 'masonry-grid';
        console.log(`使用瀑布流布局，图片数量: ${currentImages.length}`);
    }

    // 显示实际存在的图片（对搜索结果按置信度排序）
    let imagesToRender = currentImages;

    // 如果有搜索结果和置信度信息，按置信度排序
    if (galleryManager.searchResults && galleryManager.searchResults.some(r => r.confidence)) {
        const sortedResults = galleryManager.searchResults
            .filter(r => r.confidence > 0.1) // 过滤低相关度结果
            .sort((a, b) => (b.confidence || 0) - (a.confidence || 0));

        imagesToRender = sortedResults.map(r => r.path);
        console.log(`按置信度排序显示 ${imagesToRender.length} 张图片`);
    }

    imagesToRender.forEach((imagePath, index) => {
        const masonryItem = createMasonryItem(imagePath, index);
        masonryGrid.appendChild(masonryItem);
    });
}

// 返回首页功能
function goBack() {
    window.history.back();
}

// 图片点击事件 - 打开模态弹窗
function initializeImageClick() {
    const masonryGrid = document.getElementById('masonryGrid');

    masonryGrid.addEventListener('click', (e) => {
        const masonryItem = e.target.closest('.masonry-item');
        if (masonryItem) {
            const img = masonryItem.querySelector('img');
            if (img && img.src) {
                // 使用原始属性值以避免浏览器将相对路径转换为绝对URL
                const rawSrc = img.getAttribute('src') || img.src;
                openImageModal(rawSrc);
            }
        }
    });
}

// 模态弹窗管理类
class ImageModal {
    constructor() {
        this.modal = document.getElementById('imageModal');
        this.modalImage = document.getElementById('modalImage');
        this.closeBtn = document.getElementById('closeModal');
        this.refreshBtn = document.getElementById('refreshAnalysisBtn');
        this.description = document.getElementById('imageDescription');
        this.tags = document.getElementById('imageTags');

        this.currentImageSrc = null;
        this.isAnalyzing = false;

        // 确保所有元素都存在
        if (!this.modal || !this.modalImage || !this.closeBtn || !this.description || !this.tags) {
            console.error('ImageModal: 必要的DOM元素未找到');
            return;
        }

        this.initializeEventListeners();
    }

    // 规范化图片路径，统一使用站内相对路径作为主键
    normalizeImagePath(path) {
        try {
            // 生成绝对URL后取pathname作为相对路径，再去掉开头的斜杠
            const url = new URL(path, window.location.href);
            return decodeURIComponent(url.pathname.replace(/^\/+/, ''));
        } catch (e) {
            // 已经是相对路径或包含中文/空格时，保持原样但去除前缀 './' 或 '/'
            return String(path).replace(/^(\.\/)+|^\/+/, '');
        }
    }

    initializeEventListeners() {
        // 关闭按钮
        this.closeBtn.addEventListener('click', () => this.close());

        // 重新生成按钮
        if (this.refreshBtn) {
            this.refreshBtn.addEventListener('click', () => this.regenerateAnalysis());
        }

        // 点击背景关闭
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.close();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.close();
            }
        });

    }

    setRefreshLoading(isLoading) {
        if (!this.refreshBtn) return;
        this.refreshBtn.disabled = isLoading;
        this.refreshBtn.classList.toggle('is-loading', isLoading);
    }

    // 手动重新生成分析
    async regenerateAnalysis() {
        if (!this.currentImageSrc || this.isAnalyzing) return;
        this.setRefreshLoading(true);
        await this.startImageAnalysis({ force: true });
    }

    open(imageSrc) {
        // 规范化为相对路径，确保与索引/元数据的主键一致
        const normalized = this.normalizeImagePath(imageSrc);
        this.currentImageSrc = normalized;
        this.modalImage.src = normalized;
        this.modal.classList.add('show');
        document.body.classList.add('modal-open');

        // 开始AI分析（内部会根据是否命中缓存决定是否显示加载态）
        this.startImageAnalysis();
    }

    close() {
        this.modal.classList.remove('show');
        document.body.classList.remove('modal-open');
        this.currentImageSrc = null;
        this.isAnalyzing = false;
    }

    resetContent() {
        this.description.innerHTML = `
            <div class="analysis-loading">
                <div class="loading-dots"></div>
                <span>AI正在分析图片...</span>
            </div>
        `;

        this.tags.innerHTML = `
            <div class="analysis-loading">
                <div class="loading-dots"></div>
                <span>正在生成标签...</span>
            </div>
        `;
    }

    async startImageAnalysis(options = {}) {
        const { force = false } = options;
        if (this.isAnalyzing) {
            return;
        }

        this.isAnalyzing = true;

        try {
            console.log('开始分析图片：', this.currentImageSrc);

            // 首先检查是否已有元数据
            if (!force) {
                const existingMetadata = galleryManager.getImageMetadata(this.currentImageSrc);

                if (existingMetadata && existingMetadata.description && existingMetadata.tags) {
                    console.log('使用已有元数据：', existingMetadata);
                    const analysis = {
                        description: existingMetadata.description,
                        tags: existingMetadata.tags
                    };
                    this.displayAnalysisResult(analysis);
                    return;
                }
            }

            // 展示加载态并进行分析
            this.resetContent();

            // 如果没有已有数据，则使用AI分析或基础分析
            if (!window.openrouterAPI || !window.openrouterAPI.apiKey) {
                console.log('使用基础分析');
                const analysis = await this.analyzeImage(this.currentImageSrc);
                this.displayAnalysisResult(analysis);
                return;
            }

            // 调用AI识图API
            const analysis = await this.analyzeImage(this.currentImageSrc);

            if (!this.isAnalyzing) return; // 如果弹窗已关闭则不处理结果

            console.log('AI分析结果：', analysis);

            // 显示结果
            this.displayAnalysisResult(analysis);

        } catch (error) {
            console.error('图像分析失败:', error);
            this.showError(error.message);
        } finally {
            this.isAnalyzing = false;
            this.setRefreshLoading(false);
        }
    }

    async analyzeImage(imageSrc) {
        // 测试模式：仅当使用测试密钥时才启用（不再因为 localhost 而走模拟）
        const isTestMode = !!window.openrouterAPI.apiKey && window.openrouterAPI.apiKey.includes('test');

        if (isTestMode) {
            return this.getMockAnalysis(imageSrc);
        }

        // 确保索引器辅助能力可用（用于构建提示/增强标签）
        const indexer = window.imageIndexer;
        await indexer?.ensureTaxonomyLoaded?.();
        const taxonomy = (indexer && indexer.taxonomy) || { app: [], page: [], control: [] };
        const listToLine = (arr) => (arr || []).join('、');

        // 使用API类的辅助方法转换图片
        const base64Image = await window.openrouterAPI.imageUrlToBase64(this.normalizeImagePath(imageSrc));

        // 更丰富的提示，输出 JSON，优先基础分类并包含颜色
        const prompt = `请作为产品与视觉设计分析师，详细分析这张移动应用UI截图。
输出 JSON 对象：{"description":"...","tags":["标签1", ...]}
要求：
1) description 5-10 句话，具体、客观，提到关键功能、布局、风格与配色。
2) tags 8-15 个，使用中文，全部为名词或短语；可从下面"基础分类"中选择相关标签：
- APP分类: ${listToLine(taxonomy.app)}
- 页面类型: ${listToLine(taxonomy.page)}
- 控件: ${listToLine(taxonomy.control)}
3) 颜色：在 tags 中加入主色/辅色，如：蓝色、绿色、黑色、白色、深色主题/浅色主题。
4) 允许加入其他有区分度的标签（如：卡片式布局、数据可视化、健康监测、支付流程、图标风格、现代、极简等）。
仅返回 JSON，不要解释。`;

        const response = await window.openrouterAPI.analyzeImage(base64Image, prompt);

        // 解析并统一增强：JSON 解析 + 颜色抽取 + 基础分类优先 + 数量达标
        let parsed;
        if (indexer?.parseAIResponse) {
            parsed = indexer.parseAIResponse(response);
        } else {
            parsed = this.parseAnalysisResponse(response);
        }

        if (indexer?.enrichTagsAndDescription) {
            return await indexer.enrichTagsAndDescription(imageSrc, parsed);
        }
        return parsed;
    }

    getMockAnalysis(imageSrc) {
        // 根据图片路径判断图片类型，返回对应的分析结果
        const fileName = imageSrc.split('/').pop();

        if (fileName.includes('MLS')) {
            return {
                description: '这是一个体育比赛管理应用的界面，显示了足球赛事的详细信息。界面采用深色主题设计，展示比赛时间、队伍信息和相关操作按钮。',
                tags: ['体育应用', 'MLS', '足球', '赛事管理', '深色主题']
            };
        } else if (fileName.includes('Atoms')) {
            return {
                description: '这是一个习惯追踪应用的界面，采用简洁的卡片式设计。显示了用户的日常习惯项目，包含进度指示和操作建议，界面风格现代化。',
                tags: ['习惯追踪', '健康应用', '简约设计', '进度管理', '生活方式']
            };
        } else if (fileName.includes('Fuse')) {
            return {
                description: '这是一个金融钱包应用的主界面，显示账户余额为$23.42。界面包含收款、转账、发送等核心功能按钮，以及资产走势图表，采用现代化的卡片布局设计。',
                tags: ['金融应用', '数字钱包', '资产管理', '交易功能', '数据可视化']
            };
        } else if (fileName.includes('Kit')) {
            return {
                description: '这是一个移动应用的界面截图，采用卡片式设计布局。界面风格简洁现代，具有良好的信息层次结构和用户体验设计。',
                tags: ['移动应用', 'UI设计', '卡片布局', '用户界面', '现代设计']
            };
        } else if (fileName.includes('Rivian')) {
            return {
                description: '这是一个汽车相关应用的界面，可能涉及电动车辆管理或汽车服务。界面设计注重功能性和用户体验。',
                tags: ['汽车应用', '电动车', '车辆管理', '移动服务', '智能出行']
            };
        } else if (fileName.includes('Ultrahuman')) {
            return {
                description: '这是一个健康监测应用的界面，显示了用户的生物指标和健康数据。界面采用深色主题，具有专业的数据可视化元素。',
                tags: ['健康监测', '生物指标', '健康数据', '深色主题', '数据可视化']
            };
        } else if (fileName.includes('Duolingo')) {
            return {
                description: '这是语言学习应用Duolingo的界面，展示了学习进度和课程内容。界面设计色彩丰富，具有游戏化学习元素。',
                tags: ['语言学习', '教育应用', '游戏化', '进度追踪', '在线学习']
            };
        } else {
            return {
                description: '这是一个移动应用界面的截图，展示了现代化的用户界面设计。界面布局合理，具有良好的视觉层次和用户体验。',
                tags: ['移动应用', 'UI设计', '用户体验', '界面设计', '应用截图']
            };
        }
    }

    parseAnalysisResponse(response) {
        try {
            const lines = response.split('\n');
            let description = '';
            let tags = [];

            for (const line of lines) {
                if (line.startsWith('描述：') || line.startsWith('描述:')) {
                    description = line.substring(3).trim();
                } else if (line.startsWith('标签：') || line.startsWith('标签:')) {
                    const tagStr = line.substring(3).trim();
                    tags = tagStr.split(',').map(tag => tag.trim()).filter(tag => tag);
                }
            }

            // 如果解析失败，使用整个回复作为描述
            if (!description) {
                description = response;
                tags = ['AI应用', '界面设计', '用户体验'];
            }

            return { description, tags };
        } catch (error) {
            return {
                description: response || '无法分析此图片',
                tags: ['AI应用', '界面设计']
            };
        }
    }

    displayAnalysisResult(analysis) {
        // 显示描述
        this.description.innerHTML = `<p>${analysis.description}</p>`;

        // 显示标签
        const tagsHTML = analysis.tags.map(tag =>
            `<span class="tag">#${tag}</span>`
        ).join('');
        this.tags.innerHTML = tagsHTML;

        // 将结果写回元数据，便于持久化与后续复用
        try {
            if (window.imagesMetadata) {
                const exists = window.imagesMetadata.getImage(this.currentImageSrc);
                if (exists) {
                    window.imagesMetadata.updateImage(this.currentImageSrc, {
                        description: analysis.description,
                        tags: analysis.tags
                    });
                } else {
                    window.imagesMetadata.addImage({
                        path: this.currentImageSrc,
                        description: analysis.description,
                        tags: analysis.tags
                    });
                }
            }
        } catch (e) {
            console.warn('写回元数据失败:', e);
        }
    }

    showError(message) {
        this.description.innerHTML = `
            <div class="error-message">
                分析失败：${message}
                <br><small>请检查浏览器控制台获取详细信息</small>
            </div>
        `;
        this.tags.innerHTML = `<div class="error-message">标签生成失败</div>`;
    }

    showApiConfigPrompt() {
        this.description.innerHTML = `
            <div class="api-prompt">
                <p>AI图像分析功能暂时不可用</p>
            </div>
        `;
        this.tags.innerHTML = `<div class="api-prompt">AI分析暂不可用</div>`;
    }
}

// 二级页面设置面板已移除：统一使用首页保存的 openrouter_api_key / openrouter_model

// 全局弹窗实例
let imageModal;

// 打开图片模态弹窗的函数
function openImageModal(imageSrc) {
    if (!imageModal) {
        imageModal = new ImageModal();
    }
    imageModal.open(imageSrc);
}

// 已移除二级页面的独立设置入口，统一使用首页设置

// 无限滚动加载更多图片（仅在显示所有图片时启用）
function initializeInfiniteScroll() {
    // 如果是分类筛选页面，不启用无限滚动
    if (galleryManager.currentType && galleryManager.currentCategory) {
        console.log('分类页面，禁用无限滚动');
        return;
    }

    let loading = false;

    window.addEventListener('scroll', () => {
        if (loading) return;

        const scrollTop = window.pageYOffset;
        const windowHeight = window.innerHeight;
        const docHeight = document.documentElement.scrollHeight;

        // 当滚动到距离底部200px时加载更多
        if (scrollTop + windowHeight >= docHeight - 200) {
            loading = true;
            loadMoreImages();

            setTimeout(() => {
                loading = false;
            }, 1000);
        }
    });
}

// 加载更多图片
function loadMoreImages() {
    const masonryGrid = document.getElementById('masonryGrid');
    const currentCount = masonryGrid.children.length;

    // 获取当前类别的图片
    const currentImages = galleryManager.getCurrentImages();

    // 只有在显示所有图片且图片数量很多时才允许重复加载
    if (galleryManager.currentType && galleryManager.currentCategory) {
        // 分类页面不加载更多
        return;
    }

    // 计算还可以加载多少张不重复的图片
    const maxImages = currentImages.length * 3; // 最多重复3次
    if (currentCount >= maxImages) {
        return; // 已经加载够了
    }

    // 每次加载8张图片（减少重复）
    const loadCount = Math.min(8, maxImages - currentCount);
    for (let i = 0; i < loadCount; i++) {
        const imageIndex = (currentCount + i) % currentImages.length;
        const imagePath = currentImages[imageIndex];
        const masonryItem = createMasonryItem(imagePath, currentCount + i);
        masonryGrid.appendChild(masonryItem);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('初始化画廊页面...');

    try {
        // 初始化画廊管理器
        console.log('加载画廊依赖模块...');
        await galleryManager.initialize();

        // 设置页面标题
        console.log('设置页面标题...');
        setPageTitle();

        // 渲染瀑布流
        console.log('渲染图片瀑布流...');
        renderMasonry();

        // 初始化交互功能
        console.log('初始化交互功能...');
        initializeImageClick();
        initializeInfiniteScroll();

        // 输出最终状态
        const summary = {
            mode: galleryManager.searchQuery ? '搜索模式' : '分类模式',
            searchQuery: galleryManager.searchQuery,
            searchMode: galleryManager.searchMode,
            currentType: galleryManager.currentType,
            category: galleryManager.currentCategory,
            imageCount: galleryManager.currentImages.length,
            hasAISearch: !!window.AISearchEngine,
            hasSemanticSearch: !!window.semanticSearchEngine
        };
        console.log('画廊页面初始化完成:', summary);

    } catch (error) {
        console.error('画廊页面初始化失败:', error);

        // 显示错误状态
        const masonryGrid = document.getElementById('masonryGrid');
        if (masonryGrid) {
            masonryGrid.innerHTML = `
                <div class="empty-state error-state">
                    <p>⚠️ 页面初始化失败</p>
                    <small>错误: ${error.message}</small>
                    <button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #007AFF; color: white; border: none; border-radius: 4px; cursor: pointer;">重新加载</button>
                </div>
            `;
        }
    }
});

// 处理浏览器返回事件
window.addEventListener('popstate', () => {
    window.location.href = 'index.html';
});
