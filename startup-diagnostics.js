/**
 * 启动诊断模块
 * 检测和报告应用启动过程中的问题
 */
class StartupDiagnostics {
    constructor() {
        this.diagnostics = [];
        this.startTime = Date.now();
        this.checkpoints = new Map();
        this.errors = [];
        this.warnings = [];

        this.runDiagnostics();
    }

    /**
     * 运行启动诊断
     */
    async runDiagnostics() {
        console.log('🔍 开始启动诊断...');

        // 检查基础环境
        this.checkBasicEnvironment();

        // 检查脚本加载
        this.checkScriptLoading();

        // 检查图片资源
        await this.checkImageResources();

        // 检查本地存储
        this.checkLocalStorage();

        // 生成诊断报告
        this.generateReport();
    }

    /**
     * 检查基础环境
     */
    checkBasicEnvironment() {
        this.checkpoint('基础环境检查');

        // 检查浏览器兼容性
        const features = {
            'localStorage': typeof Storage !== 'undefined',
            'fetch': typeof fetch !== 'undefined',
            'Promise': typeof Promise !== 'undefined',
            'Map': typeof Map !== 'undefined',
            'Set': typeof Set !== 'undefined'
        };

        Object.entries(features).forEach(([feature, supported]) => {
            if (!supported) {
                this.addError(`浏览器不支持 ${feature}`);
            } else {
                this.addInfo(`✓ ${feature} 支持`);
            }
        });
    }

    /**
     * 检查脚本加载状态
     */
    checkScriptLoading() {
        this.checkpoint('脚本加载检查');

        const requiredModules = {
            'ImagesMetadata': window.ImagesMetadata,
            'imagesMetadata': window.imagesMetadata,
            'imageClassifier': window.imageClassifier,
            'openRouterAPI': window.openRouterAPI
        };

        const optionalModules = {
            'aiSearchEngine': window.aiSearchEngine,
            'searchInterface': window.searchInterface,
            'fileStorageManager': window.fileStorageManager
        };

        // 检查必需模块
        Object.entries(requiredModules).forEach(([name, module]) => {
            if (!module) {
                this.addError(`必需模块 ${name} 未加载`);
            } else {
                this.addInfo(`✓ ${name} 已加载`);
            }
        });

        // 检查可选模块
        Object.entries(optionalModules).forEach(([name, module]) => {
            if (!module) {
                this.addWarning(`可选模块 ${name} 未加载`);
            } else {
                this.addInfo(`✓ ${name} 已加载`);
            }
        });
    }

    /**
     * 检查图片资源
     */
    async checkImageResources() {
        this.checkpoint('图片资源检查');

        try {
            // 检查manifest文件
            const manifestResponse = await fetch('示例图片/manifest.json');
            if (!manifestResponse.ok) {
                this.addError('manifest.json 文件无法访问');
                return;
            }

            const manifest = await manifestResponse.json();
            this.addInfo(`✓ manifest.json 已加载，包含 ${manifest.files.length} 个文件`);

            // 随机检查几个图片文件
            const sampleFiles = manifest.files.slice(0, 5);
            let existingCount = 0;

            for (const file of sampleFiles) {
                try {
                    const response = await fetch(`示例图片/${file}`, { method: 'HEAD' });
                    if (response.ok) {
                        existingCount++;
                    }
                } catch (error) {
                    // 忽略单个文件错误
                }
            }

            if (existingCount === 0) {
                this.addError('所有采样图片文件都无法访问');
            } else if (existingCount < sampleFiles.length) {
                this.addWarning(`部分图片文件缺失 (${existingCount}/${sampleFiles.length} 可访问)`);
            } else {
                this.addInfo(`✓ 图片文件可正常访问`);
            }

        } catch (error) {
            this.addError(`图片资源检查失败: ${error.message}`);
        }
    }

    /**
     * 检查本地存储
     */
    checkLocalStorage() {
        this.checkpoint('本地存储检查');

        try {
            // 检查localStorage可用性
            const testKey = 'diagnostic_test';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            this.addInfo('✓ localStorage 可用');

            // 检查现有数据
            const metadataKey = 'images_metadata_v1';
            const existingData = localStorage.getItem(metadataKey);

            if (existingData) {
                try {
                    const parsed = JSON.parse(existingData);
                    const imageCount = Object.keys(parsed.images || parsed || {}).length;
                    this.addInfo(`✓ 发现已存储的图片元数据 (${imageCount} 条记录)`);
                } catch (parseError) {
                    this.addWarning('本地存储数据格式异常');
                }
            } else {
                this.addInfo('本地存储为空，将使用默认数据');
            }

        } catch (error) {
            this.addError(`本地存储检查失败: ${error.message}`);
        }
    }

    /**
     * 添加检查点
     */
    checkpoint(name) {
        this.checkpoints.set(name, Date.now() - this.startTime);
    }

    /**
     * 添加错误
     */
    addError(message) {
        this.errors.push(message);
        console.error(`❌ ${message}`);
    }

    /**
     * 添加警告
     */
    addWarning(message) {
        this.warnings.push(message);
        console.warn(`⚠️ ${message}`);
    }

    /**
     * 添加信息
     */
    addInfo(message) {
        this.diagnostics.push(message);
        console.log(`ℹ️ ${message}`);
    }

    /**
     * 生成诊断报告
     */
    generateReport() {
        const totalTime = Date.now() - this.startTime;

        console.log('\n📋 启动诊断报告');
        console.log('='.repeat(50));
        console.log(`总耗时: ${totalTime}ms`);
        console.log(`错误数: ${this.errors.length}`);
        console.log(`警告数: ${this.warnings.length}`);

        if (this.errors.length > 0) {
            console.log('\n❌ 错误列表:');
            this.errors.forEach(error => console.log(`  - ${error}`));
        }

        if (this.warnings.length > 0) {
            console.log('\n⚠️ 警告列表:');
            this.warnings.forEach(warning => console.log(`  - ${warning}`));
        }

        console.log('\n⏱️ 检查点时间:');
        this.checkpoints.forEach((time, name) => {
            console.log(`  ${name}: ${time}ms`);
        });

        // 提供修复建议
        this.provideSuggestions();

        console.log('='.repeat(50));
    }

    /**
     * 提供修复建议
     */
    provideSuggestions() {
        console.log('\n💡 修复建议:');

        if (this.errors.some(e => e.includes('ImagesMetadata'))) {
            console.log('  - 检查 images-metadata.js 文件是否存在语法错误');
        }

        if (this.errors.some(e => e.includes('图片文件'))) {
            console.log('  - 确保示例图片目录存在且包含图片文件');
            console.log('  - 检查文件路径和权限设置');
        }

        if (this.warnings.some(w => w.includes('可选模块'))) {
            console.log('  - AI搜索功能可能不可用，但基础功能应该正常');
        }

        if (this.errors.length === 0 && this.warnings.length === 0) {
            console.log('  - 所有检查通过，应用应该可以正常运行');
        }
    }

    /**
     * 获取诊断结果
     */
    getResults() {
        return {
            errors: this.errors,
            warnings: this.warnings,
            info: this.diagnostics,
            checkpoints: Object.fromEntries(this.checkpoints),
            totalTime: Date.now() - this.startTime
        };
    }
}

// 导出全局实例
window.StartupDiagnostics = StartupDiagnostics;

// 自动运行诊断
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.startupDiagnostics = new StartupDiagnostics();
    });
} else {
    window.startupDiagnostics = new StartupDiagnostics();
}