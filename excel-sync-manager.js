/**
 * Excel同步管理器
 * 实现与本地Excel文件的直接同步，无需导出导入操作
 */
class ExcelSyncManager {
    constructor() {
        this.excelFile = null;
        this.fileHandle = null;
        this.isMonitoring = false;
        this.lastModified = null;
        this.checkInterval = null;
        this.onDataChange = null;
        
        // Excel列映射
        this.columnMap = {
            'A': 'path',
            'B': 'description', 
            'C': 'tags',
            'D': 'appCategories',
            'E': 'pageTypes',
            'F': 'controlTypes',
            'G': 'lastUpdated',
            'H': 'status'
        };
        
        this.headers = [
            '路径',
            '描述', 
            '标签',
            'APP分类',
            '页面类型',
            '控件类型',
            '最后更新时间',
            '状态'
        ];
    }

    /**
     * 初始化Excel同步管理器
     */
    async init() {
        console.log('[ExcelSyncManager] 初始化Excel同步管理器');
        
        // 检查浏览器支持
        if (!window.showOpenFilePicker) {
            console.warn('[ExcelSyncManager] 浏览器不支持File System Access API，将使用兼容模式');
        }
        
        // 检查是否已有保存的文件句柄
        await this.loadSavedFileHandle();
    }

    /**
     * 选择Excel文件进行同步
     */
    async selectExcelFile() {
        try {
            if (window.showOpenFilePicker) {
                // 使用File System Access API
                const [fileHandle] = await window.showOpenFilePicker({
                    types: [{
                        description: 'Excel文件',
                        accept: {
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
                            'application/vnd.ms-excel': ['.xls']
                        }
                    }],
                    excludeAcceptAllOption: true,
                    multiple: false,
                });
                
                this.fileHandle = fileHandle;
                this.excelFile = await fileHandle.getFile();
                
                // 保存文件句柄（如果支持）
                await this.saveFileHandle();
                
            } else {
                // 兼容模式：使用传统文件选择
                return new Promise((resolve) => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.xlsx,.xls';
                    input.style.display = 'none';
                    
                    input.onchange = (e) => {
                        const file = e.target.files[0];
                        if (file) {
                            this.excelFile = file;
                            this.fileHandle = null; // 兼容模式下无法获取句柄
                            resolve(file);
                        }
                    };
                    
                    document.body.appendChild(input);
                    input.click();
                    document.body.removeChild(input);
                });
            }
            
            this.lastModified = this.excelFile.lastModified;
            console.log(`[ExcelSyncManager] 已选择Excel文件: ${this.excelFile.name}`);
            
            return this.excelFile;
            
        } catch (error) {
            console.error('[ExcelSyncManager] 选择Excel文件失败:', error);
            throw error;
        }
    }

    /**
     * 从Excel文件读取数据
     */
    async readExcelData() {
        if (!this.excelFile) {
            throw new Error('未选择Excel文件');
        }

        try {
            console.log('[ExcelSyncManager] 开始读取Excel数据...');
            
            // 读取文件内容
            const arrayBuffer = await this.excelFile.arrayBuffer();
            
            // 使用SheetJS解析Excel文件
            const workbook = XLSX.read(arrayBuffer, { type: 'array' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            
            // 转换为JSON数据
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
                header: 1,
                defval: ''
            });
            
            if (jsonData.length === 0) {
                throw new Error('Excel文件为空');
            }
            
            // 解析数据
            const imagesMap = new Map();
            const headers = jsonData[0];
            
            for (let i = 1; i < jsonData.length; i++) {
                const row = jsonData[i];
                if (!row[0]) continue; // 跳过空行
                
                const path = row[0];
                const metadata = {
                    path: path,
                    description: row[1] || '',
                    tags: row[2] ? row[2].split(';').filter(t => t.trim()) : [],
                    classifications: {
                        appCategories: this.parseCategories(row[3]),
                        pageTypes: this.parseCategories(row[4]),
                        controlTypes: this.parseCategories(row[5])
                    },
                    lastUpdated: row[6] || new Date().toISOString(),
                    indexed: row[7] === '已索引'
                };
                
                imagesMap.set(path, metadata);
            }
            
            console.log(`[ExcelSyncManager] 从Excel读取了 ${imagesMap.size} 条记录`);
            return imagesMap;
            
        } catch (error) {
            console.error('[ExcelSyncManager] 读取Excel数据失败:', error);
            throw error;
        }
    }

    /**
     * 将数据写入Excel文件
     */
    async writeExcelData(imagesMap) {
        if (!this.fileHandle && !window.showSaveFilePicker) {
            throw new Error('无法写入Excel文件：浏览器不支持或未选择文件');
        }

        try {
            console.log('[ExcelSyncManager] 开始写入Excel数据...');
            
            // 准备数据
            const data = [this.headers];
            
            imagesMap.forEach((metadata, path) => {
                const row = [
                    path,
                    metadata.description || '',
                    (metadata.tags || []).join(';'),
                    this.extractTopCategories(metadata.classifications?.appCategories),
                    this.extractTopCategories(metadata.classifications?.pageTypes),
                    this.extractTopCategories(metadata.classifications?.controlTypes),
                    metadata.lastUpdated || '',
                    metadata.indexed ? '已索引' : '未索引'
                ];
                data.push(row);
            });
            
            // 创建工作簿
            const workbook = XLSX.utils.book_new();
            const worksheet = XLSX.utils.aoa_to_sheet(data);
            
            // 设置列宽
            const colWidths = [
                { wch: 40 }, // 路径
                { wch: 50 }, // 描述
                { wch: 30 }, // 标签
                { wch: 20 }, // APP分类
                { wch: 20 }, // 页面类型
                { wch: 20 }, // 控件类型
                { wch: 20 }, // 更新时间
                { wch: 10 }  // 状态
            ];
            worksheet['!cols'] = colWidths;
            
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Images');
            
            // 生成Excel文件
            const excelBuffer = XLSX.write(workbook, {
                bookType: 'xlsx',
                type: 'array'
            });
            
            if (this.fileHandle) {
                // 直接写入到选择的文件
                const writable = await this.fileHandle.createWritable();
                await writable.write(excelBuffer);
                await writable.close();
                console.log(`[ExcelSyncManager] 数据已写入Excel文件`);
            } else {
                // 下载新文件
                await this.downloadExcelFile(excelBuffer, 'images_index.xlsx');
            }
            
            return true;
            
        } catch (error) {
            console.error('[ExcelSyncManager] 写入Excel数据失败:', error);
            throw error;
        }
    }

    /**
     * 开始监控Excel文件变化
     */
    startMonitoring(onDataChangeCallback) {
        if (!this.excelFile) {
            console.warn('[ExcelSyncManager] 未选择Excel文件，无法开始监控');
            return;
        }

        this.onDataChange = onDataChangeCallback;
        this.isMonitoring = true;
        
        // 每3秒检查一次文件修改时间
        this.checkInterval = setInterval(async () => {
            await this.checkFileChanges();
        }, 3000);
        
        console.log('[ExcelSyncManager] 开始监控Excel文件变化');
    }

    /**
     * 停止监控Excel文件
     */
    stopMonitoring() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
        this.isMonitoring = false;
        console.log('[ExcelSyncManager] 停止监控Excel文件');
    }

    /**
     * 检查文件变化
     */
    async checkFileChanges() {
        if (!this.fileHandle) return;
        
        try {
            const currentFile = await this.fileHandle.getFile();
            
            if (currentFile.lastModified > this.lastModified) {
                console.log('[ExcelSyncManager] 检测到Excel文件变化，正在同步...');
                this.lastModified = currentFile.lastModified;
                this.excelFile = currentFile;
                
                // 读取新数据并触发回调
                if (this.onDataChange) {
                    const newData = await this.readExcelData();
                    await this.onDataChange(newData);
                }
            }
        } catch (error) {
            console.error('[ExcelSyncManager] 检查文件变化失败:', error);
        }
    }

    /**
     * 创建初始Excel文件
     */
    async createInitialExcelFile(imagesMap) {
        try {
            console.log('[ExcelSyncManager] 创建初始Excel文件...');
            
            const data = [this.headers];
            
            imagesMap.forEach((metadata, path) => {
                const row = [
                    path,
                    metadata.description || '',
                    (metadata.tags || []).join(';'),
                    this.extractTopCategories(metadata.classifications?.appCategories),
                    this.extractTopCategories(metadata.classifications?.pageTypes),
                    this.extractTopCategories(metadata.classifications?.controlTypes),
                    metadata.lastUpdated || '',
                    metadata.indexed ? '已索引' : '未索引'
                ];
                data.push(row);
            });
            
            const workbook = XLSX.utils.book_new();
            const worksheet = XLSX.utils.aoa_to_sheet(data);
            
            // 设置样式
            const colWidths = [
                { wch: 40 }, { wch: 50 }, { wch: 30 }, { wch: 20 },
                { wch: 20 }, { wch: 20 }, { wch: 20 }, { wch: 10 }
            ];
            worksheet['!cols'] = colWidths;
            
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Images');
            
            const excelBuffer = XLSX.write(workbook, {
                bookType: 'xlsx',
                type: 'array'
            });
            
            const filename = `images_index_${new Date().toISOString().slice(0,10).replace(/-/g,'')}.xlsx`;
            await this.downloadExcelFile(excelBuffer, filename);
            
            console.log('[ExcelSyncManager] 初始Excel文件创建成功');
            return true;
            
        } catch (error) {
            console.error('[ExcelSyncManager] 创建Excel文件失败:', error);
            throw error;
        }
    }

    /**
     * 获取同步状态
     */
    getSyncStatus() {
        return {
            hasFile: !!this.excelFile,
            fileName: this.excelFile?.name || null,
            isMonitoring: this.isMonitoring,
            lastModified: this.lastModified,
            canWrite: !!this.fileHandle
        };
    }

    // ================== 辅助方法 ==================

    /**
     * 解析分类字符串
     */
    parseCategories(categoriesStr) {
        if (!categoriesStr) return [];
        return categoriesStr.split(';').map(cat => ({
            category: cat.trim(),
            score: 1,
            keywords: []
        })).filter(c => c.category);
    }

    /**
     * 提取顶级分类
     */
    extractTopCategories(categories) {
        if (!categories || !Array.isArray(categories)) return '';
        return categories
            .sort((a, b) => b.score - a.score)
            .slice(0, 3)
            .map(c => c.category)
            .join(';');
    }

    /**
     * 下载Excel文件
     */
    async downloadExcelFile(buffer, filename) {
        try {
            const finalFilename = filename || 'images_index.xlsx';
            console.log(`[ExcelSyncManager] 下载文件: ${finalFilename}`);
            
            const blob = new Blob([buffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            
            // 优先使用FileSaver.js
            if (typeof saveAs === 'function') {
                console.log('[ExcelSyncManager] 使用FileSaver.js下载');
                saveAs(blob, finalFilename);
            } else {
                // 回退到传统方法
                console.log('[ExcelSyncManager] 使用传统方法下载');
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = finalFilename;
                a.style.display = 'none';
                
                document.body.appendChild(a);
                a.click();
                
                // 清理
                setTimeout(() => {
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 1000);
            }
            
        } catch (error) {
            console.error('[ExcelSyncManager] 下载文件失败:', error);
            throw error;
        }
    }

    /**
     * 保存文件句柄到本地存储
     */
    async saveFileHandle() {
        // 注意：由于安全限制，文件句柄无法直接序列化存储
        // 这里只能保存文件的基本信息
        if (this.excelFile) {
            localStorage.setItem('excel_sync_file_info', JSON.stringify({
                name: this.excelFile.name,
                size: this.excelFile.size,
                lastModified: this.excelFile.lastModified
            }));
        }
    }

    /**
     * 加载保存的文件句柄
     */
    async loadSavedFileHandle() {
        const fileInfo = localStorage.getItem('excel_sync_file_info');
        if (fileInfo) {
            const info = JSON.parse(fileInfo);
            console.log(`[ExcelSyncManager] 发现之前使用的Excel文件: ${info.name}`);
        }
    }
}

// 创建全局实例
window.ExcelSyncManager = ExcelSyncManager;
window.excelSyncManager = new ExcelSyncManager();