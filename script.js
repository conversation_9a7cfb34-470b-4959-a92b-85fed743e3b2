// 错误处理类
class ErrorHandler {
    static handleMissingDependency(name, fallback = null) {
        console.warn(`依赖 ${name} 未加载，使用降级功能`);
        if (fallback) fallback();
    }

    static showUserError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-toast';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        setTimeout(() => errorDiv.remove(), 5000);
    }
}

// 图片分类展示管理器
class ImageCategoryManager {
    constructor() {
        try {
            this.currentTab = 'category';
            this.categoryData = {
                app: [],
                page: [],
                control: []
            };
            this.isInitialized = false;
            this.imagesMetadata = window.imagesMetadata;
            if (!this.imagesMetadata) {
                throw new Error('ImagesMetadata not available');
            }
        } catch (error) {
            console.error('ImageCategoryManager初始化失败:', error);
            this.imagesMetadata = null;
            this.useFallbackData = true;
            this.currentTab = 'category';
            this.categoryData = {
                app: [],
                page: [],
                control: []
            };
            this.isInitialized = false;
        }
    }

    // 初始化分类数据
    async initialize() {
        if (this.isInitialized) return;

        // 等待依赖模块加载
        if (!window.imagesMetadata) {
            console.warn('ImagesMetadata未加载，使用静态数据');
            this.initializeStaticData();
            return;
        }

        // 如果使用降级数据
        if (this.useFallbackData) {
            console.warn('使用降级数据模式');
            this.initializeStaticData();
            return;
        }

        // 等待 ImagesMetadata 数据完全加载
        console.log('等待图片元数据加载...');
        await window.imagesMetadata.waitForReady();
        console.log('图片元数据已就绪，开始初始化分类数据');

        // 确保ImageClassifier已加载并重新计算分类
        if (window.imageClassifier && window.imagesMetadata.recalculateClassifications) {
            console.log('重新计算图片分类...');
            window.imagesMetadata.recalculateClassifications();
        }

        // 从元数据管理器获取动态分类数据
        try {
            this.categoryData.app = window.imagesMetadata.getCategoryStats('app');
            this.categoryData.page = window.imagesMetadata.getCategoryStats('page');
            this.categoryData.control = window.imagesMetadata.getCategoryStats('control');

            // 验证分类数据质量
            this.validateCategoryData();

            console.log('分类数据加载完成:', this.categoryData);
        } catch (error) {
            console.warn('动态分类数据加载失败，使用静态数据:', error);
            this.initializeStaticData();
            return;
        }

        this.isInitialized = true;
    }

    // 获取降级数据
    getFallbackCategories() {
        return {
            category: [
                { category: '金融应用', count: 5, type: 'app' },
                { category: '健康监测', count: 4, type: 'app' },
                { category: '体育应用', count: 2, type: 'app' },
                { category: '语言学习', count: 1, type: 'app' }
            ],
            component: [
                { category: '支付界面', count: 3, type: 'page' },
                { category: '数据展示', count: 4, type: 'page' },
                { category: '设置页面', count: 2, type: 'page' }
            ],
            flow: [
                { category: '按钮', count: 8, type: 'control' },
                { category: '卡片', count: 6, type: 'control' },
                { category: '表单', count: 4, type: 'control' }
            ]
        };
    }

    // 初始化静态数据（兼容模式）
    initializeStaticData() {
        if (this.useFallbackData) {
            const fallbackData = this.getFallbackCategories();
            this.categoryData = {
                app: fallbackData.category,
                page: fallbackData.component,
                control: fallbackData.flow
            };
        } else {
            this.categoryData = {
                app: [
                    { category: '金融', count: 6, type: 'app' },
                    { category: '健康与健身', count: 4, type: 'app' },
                    { category: '教育', count: 2, type: 'app' },
                    { category: '体育', count: 2, type: 'app' },
                    { category: '旅行与交通', count: 1, type: 'app' },
                    { category: '实用工具', count: 1, type: 'app' }
                ],
                page: [
                    { category: '支付方式', count: 4, type: 'page' },
                    { category: '订单详情', count: 2, type: 'page' },
                    { category: '我的账户与个人资料', count: 2, type: 'page' },
                    { category: '导览与教程', count: 1, type: 'page' },
                    { category: '促销与奖励', count: 1, type: 'page' },
                    { category: '视图', count: 6, type: 'page' }
                ],
                control: [
                    { category: '卡片', count: 8, type: 'control' },
                    { category: '按钮', count: 6, type: 'control' },
                    { category: '进度指示器', count: 4, type: 'control' },
                    { category: '标签', count: 3, type: 'control' },
                    { category: '列表', count: 2, type: 'control' }
                ]
            };
        }
        this.isInitialized = true;
    }

    // 获取当前tab的数据
    getCurrentTabData() {
        const tabMap = {
            'category': 'app',
            'component': 'control',
            'flow': 'page'
        };
        return this.categoryData[tabMap[this.currentTab]] || [];
    }

    // 设置当前tab
    setCurrentTab(tab) {
        this.currentTab = tab;
    }

    // 生成分类卡片的CSS类名
    generateCategoryClass(category) {
        return 'category-' + category.toLowerCase()
            .replace(/[\s&]/g, '-')
            .replace(/[^a-z0-9-]/g, '');
    }

    // 获取分类的缩略图
    getCategoryThumbnails(categoryType, categoryName) {
        if (!window.imagesMetadata) {
            // 返回默认图片
            return ['示例图片/Kit iOS 157.png', '示例图片/MLS iOS 13.png'];
        }

        const typeMap = {
            'app': 'app',
            'control': 'control',
            'page': 'page'
        };

        const images = window.imagesMetadata.getImagesByCategory(
            typeMap[categoryType],
            categoryName
        );

        // 返回前两张图片作为缩略图
        return images.slice(0, 2).map(img => (img.preview || img.path));
    }

    // 验证分类数据质量
    validateCategoryData() {
        const minValidCategories = 2; // 至少需要2个有效分类

        for (const [type, data] of Object.entries(this.categoryData)) {
            if (!Array.isArray(data) || data.length < minValidCategories) {
                throw new Error(`${type}分类数据不足，只有${data.length}个分类`);
            }

            // 验证每个分类项的完整性
            for (const item of data) {
                if (!item.category || typeof item.count !== 'number' || !item.type) {
                    throw new Error(`分类数据项不完整: ${JSON.stringify(item)}`);
                }
            }
        }
    }

    // 获取分类统计摘要
    getCategorySummary() {
        const summary = {
            totalCategories: 0,
            totalImages: 0,
            byType: {}
        };

        for (const [type, categories] of Object.entries(this.categoryData)) {
            const typeCount = categories.length;
            const typeImages = categories.reduce((sum, cat) => sum + cat.count, 0);

            summary.totalCategories += typeCount;
            summary.totalImages += typeImages;
            summary.byType[type] = {
                categories: typeCount,
                images: typeImages
            };
        }

        return summary;
    }

    // 刷新分类数据
    async refresh() {
        console.log('刷新分类数据...');
        this.isInitialized = false;

        // 确保数据已保存并重新加载
        if (window.imagesMetadata && window.imagesMetadata.isReady) {
            console.log('等待数据同步...');
            // 给一点时间让数据保存完成
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        await this.initialize();

        // 输出更新后的统计信息
        const summary = this.getCategorySummary();
        console.log('分类数据更新完成:', summary);
    }
}

// 全局实例
let imageCategoryManager;

// 在DOMContentLoaded中初始化
try {
    imageCategoryManager = new ImageCategoryManager();
    window.imageCategoryManager = imageCategoryManager;
} catch (error) {
    console.error('创建ImageCategoryManager失败:', error);
    // 创建降级版本
    window.imageCategoryManager = {
        getCurrentTabData: () => [],
        setCurrentTab: () => {},
        refresh: () => Promise.resolve(),
        useFallbackData: true
    };
}

// 获取当前tab的数据
function getCurrentTabData() {
    return window.imageCategoryManager ? window.imageCategoryManager.getCurrentTabData() : [];
}

// 生成分类CSS类名
function generateCategoryClass(category) {
    return 'category-' + category.toLowerCase()
        .replace(/[\s&]/g, '-')
        .replace(/[^a-z0-9-]/g, '');
}

// 获取分类缩略图
function getCategoryThumbnails(categoryType, categoryName) {
    if (!window.imagesMetadata) {
        // 返回默认图片
        return ['示例图片/Kit iOS 157.png', '示例图片/MLS iOS 13.png'];
    }

    const typeMap = {
        'app': 'app',
        'control': 'control',
        'page': 'page'
    };

    const images = window.imagesMetadata.getImagesByCategory(
        typeMap[categoryType],
        categoryName
    );

    // 返回前两张图片作为缩略图
    return images.slice(0, 2).map(img => img.path);
}

// 渲染分类卡片
function renderCategoryCards(dataToRender) {
    const projectsGrid = document.getElementById('projectsGrid');
    if (!projectsGrid) {
        console.error('项目网格元素未找到');
        return;
    }

    projectsGrid.innerHTML = '';

    if (!dataToRender || dataToRender.length === 0) {
        projectsGrid.innerHTML = `
            <div class="empty-state">
                <p>暂无分类数据</p>
                <small>请等待图片索引完成或检查数据配置</small>
            </div>
        `;
        return;
    }

    dataToRender.forEach(categoryItem => {
        try {
            const { category, count, type } = categoryItem;
            const categoryClass = generateCategoryClass(category);
            const thumbnails = getCategoryThumbnails(type, category);

            const projectCard = document.createElement('div');
            projectCard.className = 'project-card category-card';
            projectCard.dataset.category = category;
            projectCard.dataset.type = type;
            projectCard.dataset.count = count;

            projectCard.innerHTML = `
                <div class="project-images">
                    ${thumbnails.slice(0, 2).map((image, index) => `
                        <img src="${image}" alt="${category}" class="${index === 0 ? 'main-image' : 'side-image'}"
                             onerror="this.style.display='none'" />
                    `).join('')}
                    <div class="image-overlay">
                        <span class="image-count">${count} 张图片</span>
                    </div>
                </div>
                <div class="project-info">
                    <span class="project-category ${categoryClass}">${category}</span>
                    <span class="category-stats">${count} 个案例</span>
                </div>
            `;

            projectsGrid.appendChild(projectCard);
        } catch (error) {
            console.error('渲染分类卡片失败:', error, categoryItem);
        }
    });
}

// 兼容旧方法名
function renderProjects(categoriesToRender = null) {
    const dataToRender = categoriesToRender || getCurrentTabData();

    renderCategoryCards(dataToRender);
}

// Tab切换功能
function initializeTabs(categoryManager) {
    const tabBtns = document.querySelectorAll('.tab-btn');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // 移除所有active类
            tabBtns.forEach(b => b.classList.remove('active'));
            // 添加active类到当前按钮
            btn.classList.add('active');

            // 更新当前tab
            categoryManager.setCurrentTab(btn.dataset.tab);

            // 重新渲染内容
            renderProjects(categoryManager.getCurrentTabData());
        });
    });
}

// 智能图片搜索功能
function initializeImageSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');

    if (!searchInput || !searchBtn) {
        console.warn('搜索元素未找到');
        return;
    }

    // 检查AI搜索是否可用
    const hasAISearch = window.aiSearchEngine && window.searchInterface;

    if (hasAISearch) {
        console.log('使用AI增强搜索');
        initializeAISearch();
    } else {
        console.log('使用基础搜索功能');
        initializeBasicSearch();
    }
}

// 初始化AI搜索
function initializeAISearch() {
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const searchSuggestions = document.getElementById('searchSuggestions');
    const suggestionsList = document.getElementById('suggestionsList');
    const suggestionsHeader = document.getElementById('suggestionsHeader');
    const clearHistoryBtn = document.getElementById('clearHistoryBtn');

    let currentSuggestionIndex = -1;
    let currentSuggestions = [];
    let searchInterface = null;

    // 初始化搜索界面
    try {
        searchInterface = new window.SearchInterface({
            searchInput: searchInput,
            searchBtn: searchBtn,
            suggestionsContainer: searchSuggestions,
            onSearch: performSearch
        });
        console.log('AI搜索界面已启用');
    } catch (error) {
        console.warn('AI搜索界面初始化失败，降级到基础搜索:', error);
        initializeBasicSearch();
        return;
    }

    // 监听输入变化，动态切换按钮状态和显示建议
    function updateButtonState() {
        const hasContent = searchInput.value.trim().length > 0;

        if (hasContent) {
            searchBtn.classList.remove('inactive');
            searchBtn.classList.add('active');
        } else {
            searchBtn.classList.remove('active');
            searchBtn.classList.add('inactive');
        }
    }

    // 显示搜索建议
    async function showSuggestions(input) {
        // 优先使用新的搜索界面
        if (searchInterface) {
            try {
                await searchInterface.showSuggestions(input);
                return;
            } catch (error) {
                console.warn('AI搜索建议失败，回退到传统方式:', error);
            }
        }

        // 回退到传统搜索建议
        if (!window.semanticSearchEngine) {
            return;
        }

        const suggestions = window.semanticSearchEngine.getSearchSuggestions(input);
        currentSuggestions = suggestions;
        currentSuggestionIndex = -1;

        if (suggestions.length === 0) {
            hideSuggestions();
            return;
        }

        // 检查是否有历史记录
        const hasHistory = suggestions.some(s => s.type === 'history');
        if (hasHistory) {
            suggestionsHeader.style.display = 'flex';
        } else {
            suggestionsHeader.style.display = 'none';
        }

        suggestionsList.innerHTML = '';

        suggestions.forEach((suggestion, index) => {
            const item = document.createElement('button');
            item.className = 'suggestion-item';
            item.dataset.type = suggestion.type;
            item.dataset.index = index;

            const iconSvg = getSuggestionIcon(suggestion.type);

            item.innerHTML = `
                ${iconSvg}
                <span class="suggestion-text">${suggestion.text}</span>
                <span class="suggestion-type">${getSuggestionTypeText(suggestion.type)}</span>
            `;

            item.addEventListener('click', () => {
                searchInput.value = suggestion.text;
                hideSuggestions();
                performSearch();
            });

            suggestionsList.appendChild(item);
        });

        searchSuggestions.style.display = 'block';
    }

    // 隐藏搜索建议
    function hideSuggestions() {
        searchSuggestions.style.display = 'none';
        currentSuggestionIndex = -1;
    }

    // 获取建议类型图标
    function getSuggestionIcon(type) {
        const icons = {
            history: `<svg class="suggestion-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12,6 12,12 16,14"></polyline>
            </svg>`,
            suggestion: `<svg class="suggestion-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="9,12 l2,2 4,-4"></path>
            </svg>`,
            popular: `<svg class="suggestion-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
            </svg>`
        };
        return icons[type] || icons.suggestion;
    }

    // 获取建议类型文本
    function getSuggestionTypeText(type) {
        const texts = {
            history: '历史',
            suggestion: '建议',
            popular: '热门'
        };
        return texts[type] || '';
    }

    // 处理键盘导航
    function handleKeyNavigation(e) {
        if (searchSuggestions.style.display === 'none') {
            return;
        }

        const items = suggestionsList.querySelectorAll('.suggestion-item');

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            currentSuggestionIndex = Math.min(currentSuggestionIndex + 1, items.length - 1);
            updateSuggestionSelection(items);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            currentSuggestionIndex = Math.max(currentSuggestionIndex - 1, -1);
            updateSuggestionSelection(items);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (currentSuggestionIndex >= 0 && items[currentSuggestionIndex]) {
                const selectedText = currentSuggestions[currentSuggestionIndex].text;
                searchInput.value = selectedText;
                hideSuggestions();
                performSearch();
            } else {
                performSearch();
            }
        } else if (e.key === 'Escape') {
            hideSuggestions();
        }
    }

    // 更新建议选择状态
    function updateSuggestionSelection(items) {
        items.forEach((item, index) => {
            if (index === currentSuggestionIndex) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    // 执行搜索
    async function performSearch() {
        const query = searchInput.value.trim();

        if (query === '') {
            alert('请输入搜索内容');
            searchInput.focus();
            return;
        }

        // 使用AI搜索引擎预处理查询（如果可用）
        let processedQuery = query;
        let searchMode = 'auto';

        if (window.aiSearchEngine && typeof window.aiSearchEngine.search === 'function') {
            try {
                console.log('正在使用AI搜索引擎预处理查询...');
                const searchResult = await window.aiSearchEngine.search(query, {
                    strategy: 'traditional',
                    maxResults: 5
                });

                if (searchResult && searchResult.results) {
                    // 基于搜索结果推断最佳模式
                    const resultCount = searchResult.results.length;
                    if (resultCount > 15) {
                        searchMode = 'specific';
                    } else if (resultCount > 5) {
                        searchMode = 'auto';
                    } else {
                        searchMode = 'broad';
                    }

                    console.log('AI搜索预处理完成:', {
                        original: query,
                        processed: processedQuery,
                        mode: searchMode,
                        resultCount: resultCount
                    });
                }
            } catch (error) {
                console.warn('AI搜索引擎预处理失败，使用原始查询:', error);
            }
        }

        // 跳转到图片展示页面，传递搜索参数和模式
        const params = new URLSearchParams({
            search: processedQuery,
            mode: searchMode,
            original: query !== processedQuery ? query : undefined
        });

        // 保存搜索历史
        if (window.semanticSearchEngine && typeof window.semanticSearchEngine.addToSearchHistory === 'function') {
            window.semanticSearchEngine.addToSearchHistory(query);
        }

        window.location.href = `gallery.html?${params.toString()}`;
    }

    // 初始状态检查
    updateButtonState();

    // 监听输入事件
    searchInput.addEventListener('input', async (e) => {
        updateButtonState();
        const value = e.target.value.trim();

        // 防抖处理
        clearTimeout(searchInput.suggestionTimeout);
        searchInput.suggestionTimeout = setTimeout(async () => {
            if (value.length > 0) {
                await showSuggestions(value);
            } else {
                await showSuggestions(''); // 显示热门搜索
            }
        }, 300);
    });

    // 监听焦点事件
    searchInput.addEventListener('focus', async () => {
        const value = searchInput.value.trim();
        await showSuggestions(value);
    });

    // 点击外部隐藏建议
    document.addEventListener('click', (e) => {
        if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
            hideSuggestions();
        }
    });

    // 键盘导航
    searchInput.addEventListener('keydown', handleKeyNavigation);

    // 点击搜索按钮
    searchBtn.addEventListener('click', performSearch);

    // 回车键搜索
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && searchSuggestions.style.display === 'none') {
            performSearch();
        }
    });

    // 清除搜索历史
    if (clearHistoryBtn) {
        clearHistoryBtn.addEventListener('click', async (e) => {
            e.stopPropagation();
            if (confirm('确定要清除所有搜索历史吗？')) {
                if (searchInterface) {
                    searchInterface.clearHistory();
                } else if (window.semanticSearchEngine) {
                    window.semanticSearchEngine.clearSearchHistory();
                }
                hideSuggestions();
                // 重新显示建议（不包含历史记录）
                setTimeout(async () => {
                    const value = searchInput.value.trim();
                    await showSuggestions(value);
                }, 100);
            }
        });
    }
}

// 隐藏loading指示器
function hideLoadingIndicator() {
    const loading = document.getElementById('loadingIndicator');
    if (loading) {
        loading.style.opacity = '0';
        setTimeout(() => loading.remove(), 300);
    }
}

// 显示错误
function showError(message) {
    hideLoadingIndicator();
    ErrorHandler.showUserError(message);
}

// 初始化基础搜索
function initializeBasicSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');

    if (!searchInput || !searchBtn) return;

    // 基础搜索功能
    const performBasicSearch = () => {
        const query = searchInput.value.trim();
        if (query === '') {
            alert('请输入搜索内容');
            return;
        }

        // 直接跳转到图片展示页面
        const params = new URLSearchParams({ search: query, mode: 'basic' });
        window.location.href = `gallery.html?${params.toString()}`;
    };

    searchBtn.addEventListener('click', performBasicSearch);
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') performBasicSearch();
    });

    // 添加降级模式标记
    const searchBox = searchInput.closest('.search-box');
    if (searchBox) {
        searchBox.classList.add('fallback-mode');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('开始初始化应用...');

        // 等待核心依赖模块完全加载
        console.log('等待核心模块加载...');
        let retryCount = 0;
        const maxRetries = 50; // 最多等待5秒

        while (!window.imagesMetadata && retryCount < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 100));
            retryCount++;
        }

        if (!window.imagesMetadata) {
            console.warn('ImagesMetadata 模块加载超时，使用降级模式');
        }

        // 初始化核心组件，带错误处理
        const categoryManager = new ImageCategoryManager();

        if (categoryManager.useFallbackData) {
            console.warn('使用降级数据模式');
            // 显示降级模式横幅
            const container = document.querySelector('.container');
            if (container) {
                const banner = document.createElement('div');
                banner.className = 'degraded-mode-banner';
                banner.innerHTML = `
                    ⚠️ 系统运行在降级模式，部分功能可能受限
                    <button class="close-btn" onclick="this.parentElement.remove()">×</button>
                `;
                container.insertBefore(banner, container.firstChild);
            }
        }

        // 初始化分类管理器（现在会等待数据完全加载）
        console.log('初始化分类管理器...');
        await categoryManager.initialize();

        // 渲染UI
        console.log('渲染界面...');
        renderProjects(categoryManager.getCurrentTabData());
        initializeTabs(categoryManager);
        initializeImageSearch();
        initializeNavigation();

        // 隐藏loading指示器
        hideLoadingIndicator();

        console.log('应用初始化完成');

    } catch (error) {
        console.error('应用初始化失败:', error);
        showError('应用加载失败，请刷新页面重试');
    }
});

// 加载依赖脚本
async function loadDependencies() {
    const scripts = [
        'openrouter-api.js',
        'image-classifier.js',
        'images-metadata.js',
        'image-indexer.js',
        'semantic-search.js',
        'prompt-templates.js',
        'ai-search-engine.js',
        'search-interface.js'
    ];

    for (const script of scripts) {
        if (!document.querySelector(`script[src="${script}"]`)) {
            await loadScript(script);
        }
    }
}

// 动态加载脚本
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// 初始化索引按钮
function initializeIndexButton() {
    // 在搜索区域添加索引按钮
    const searchSection = document.querySelector('.search-section');
    const indexButton = document.createElement('button');
    indexButton.className = 'index-btn';
    indexButton.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            <circle cx="12" cy="12" r="3"></circle>
        </svg>
    `;
    indexButton.addEventListener('click', openIndexModal);
    searchSection.appendChild(indexButton);
}

// 初始化导航按钮事件
function initializeNavigation() {
    // 聊天按钮事件
    const chatBtn = document.getElementById('chatBtn');
    if (chatBtn) {
        chatBtn.addEventListener('click', navigateToChat);
    }

    // 设置按钮事件
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', () => openSettingsModal());
    }
}

// 图片索引模态弹窗管理
class IndexModal {
    constructor() {
        this.modal = document.getElementById('indexModal');
        this.closeBtn = document.getElementById('closeIndexModal');
        this.cancelBtn = document.getElementById('cancelIndexBtn');
        this.startBtn = document.getElementById('startIndexBtn');
        this.progressSection = document.getElementById('indexProgress');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
        this.progressCount = document.getElementById('progressCount');
        this.progressDetails = document.getElementById('progressDetails');
        this.forceToggle = document.getElementById('forceRebuildToggle');
        
        this.isIndexing = false;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // 关闭按钮
        this.closeBtn.addEventListener('click', () => this.close());
        this.cancelBtn.addEventListener('click', () => this.close());

        // 点击背景关闭
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal && !this.isIndexing) {
                this.close();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show') && !this.isIndexing) {
                this.close();
            }
        });

        // 开始索引按钮
        this.startBtn.addEventListener('click', () => this.startIndexing());
    }

    open() {
        this.modal.classList.add('show');
        document.body.classList.add('modal-open');
        this.resetState();
    }

    close() {
        if (this.isIndexing) {
            if (confirm('索引正在进行中，确定要取消吗？')) {
                this.isIndexing = false;
            } else {
                return;
            }
        }
        
        this.modal.classList.remove('show');
        document.body.classList.remove('modal-open');
    }

    resetState() {
        this.isIndexing = false;
        this.progressSection.style.display = 'none';
        this.startBtn.disabled = false;
        this.startBtn.textContent = '开始索引';
        this.cancelBtn.textContent = '取消';
        
        // 重置选项
        const radioAll = document.querySelector('input[name="indexType"][value="all"]');
        if (radioAll) radioAll.checked = true;
    }

    async startIndexing() {
        if (!window.imageIndexer) {
            alert('图片索引器未加载');
            return;
        }

        // 获取选择的索引类型
        const indexType = document.querySelector('input[name="indexType"]:checked')?.value || 'all';
        const forceRebuildEnabled = !!(this.forceToggle && this.forceToggle.checked);
        
        this.isIndexing = true;
        this.startBtn.disabled = true;
        this.startBtn.textContent = '索引中...';
        this.cancelBtn.textContent = '强制停止';
        this.progressSection.style.display = 'block';

        try {
            // 设置进度回调
            window.imageIndexer.setCallbacks(
                (progress) => this.updateProgress(progress),
                (results) => this.onIndexComplete(results)
            );

            // 根据选择的类型开始索引
            if (forceRebuildEnabled || indexType === 'rebuild') {
                // 强制重建或选择了重建
                await window.imageIndexer.rebuildIndexing();
            } else if (indexType === 'untagged') {
                // 只索引未打标的图片
                await this.startUntaggedIndexing();
            } else {
                // 索引全部图片
                await window.imageIndexer.startIndexing();
            }

        } catch (error) {
            console.error('索引失败:', error);
            this.showError(error.message);
        }
    }

    async startUntaggedIndexing() {
        // 获取所有图片
        const allImages = await window.imageIndexer.scanDirectory();
        
        // 过滤出未打标的图片
        const untaggedImages = allImages.filter(imagePath => {
            const metadata = window.imagesMetadata?.getImage(imagePath);
            return !metadata || !metadata.tags || metadata.tags.length === 0;
        });

        console.log(`发现 ${untaggedImages.length} 张未打标图片`);

        if (untaggedImages.length === 0) {
            this.onIndexComplete({
                total: allImages.length,
                success: 0,
                skipped: allImages.length,
                errors: 0,
                message: '所有图片都已打标'
            });
            return;
        }

        // 批量索引未打标图片
        await window.imageIndexer.batchIndexImages(untaggedImages);
    }

    updateProgress(progress) {
        const percentage = progress.total > 0 ? (progress.processed / progress.total) * 100 : 0;

        this.progressFill.style.width = `${percentage}%`;
        this.progressText.textContent = `正在处理图片...`;
        this.progressCount.textContent = `${progress.processed} / ${progress.total}`;

        if (progress.errors > 0) {
            this.progressDetails.textContent = `已完成 ${progress.processed - progress.errors} 张，失败 ${progress.errors} 张`;
        } else {
            this.progressDetails.textContent = `已处理 ${progress.processed} 张图片（包含缓存加速）`;
        }
    }

    async onIndexComplete(results) {
        this.isIndexing = false;
        this.progressFill.style.width = '100%';
        this.progressText.textContent = '索引完成！';
        this.progressCount.textContent = `${results.total} / ${results.total}`;

        // 构建更详细的结果说明，重点突出缓存使用情况
        let detailsText = '';
        if (results.cached > 0) {
            detailsText = `🔄 使用缓存: ${results.cached} 张`;
        }
        if (results.success > 0) {
            if (detailsText) detailsText += ', ';
            detailsText += `✨ 新分析: ${results.success} 张`;
        }
        if (results.skipped > 0) {
            if (detailsText) detailsText += ', ';
            detailsText += `⏭️ 跳过: ${results.skipped} 张`;
        }
        if (results.errors > 0) {
            if (detailsText) detailsText += ', ';
            detailsText += `❌ 失败: ${results.errors} 张`;
        }

        // 如果大部分都是缓存，特别提醒用户
        if (results.cached > 0 && results.cached >= results.total * 0.8) {
            detailsText += ` | 大部分图片已有缓存，无需重复分析`;
        }

        this.progressDetails.textContent = detailsText || '处理完成';

        this.startBtn.disabled = false;
        this.startBtn.textContent = '索引完成';
        this.cancelBtn.textContent = '关闭';

        // 确保数据持久化并刷新界面
        if (results.success > 0 || results.cached > 0) {
            console.log('索引完成，开始保存数据并刷新界面...');

            // 确保数据已保存到 localStorage
            if (window.imagesMetadata && window.imagesMetadata.saveToStorage) {
                await window.imagesMetadata.saveToStorage();
                console.log('索引数据已保存到 localStorage');
            }

            // 刷新分类数据和界面
            await imageCategoryManager.refresh();
            renderProjects();

            console.log('界面刷新完成，索引数据已持久化');
        }

        // 3秒后自动关闭
        setTimeout(() => {
            if (!this.isIndexing) {
                this.close();
            }
        }, 3000);
    }

    showError(message) {
        this.isIndexing = false;
        this.progressText.textContent = '索引失败';
        this.progressDetails.textContent = `错误: ${message}`;
        this.startBtn.disabled = false;
        this.startBtn.textContent = '重试';
        this.cancelBtn.textContent = '关闭';
    }
}

// 全局索引弹窗实例
let indexModal;

// 打开索引弹窗
function openIndexModal() {
    if (!indexModal) {
        indexModal = new IndexModal();
    }
    indexModal.open();
}

// 全局设置弹窗实例（首页）
let settingsModalInstance;

function openSettingsModal() {
    try {
        if (!settingsModalInstance) {
            settingsModalInstance = new SettingsModal();
            // 兼容其他地方可能使用的全局引用
            window.settingsModal = settingsModalInstance;
        }
        settingsModalInstance.open();
    } catch (e) {
        console.error('打开设置弹窗失败:', e);
    }
}

// 聊天页面导航
function navigateToChat() {
    window.location.href = 'chat.html';
}

// 设置弹窗管理
class SettingsModal {
    constructor() {
        this.modal = document.getElementById('settingsModal');
        this.closeBtn = document.getElementById('closeSettingsModal');
        this.tabs = this.modal.querySelectorAll('.settings-tab');
        this.contents = this.modal.querySelectorAll('.settings-content');

        // 索引tab内的元素（作用域限定在设置弹窗内，避免与独立索引弹窗冲突）
        this.indexScope = this.modal; // 作用域容器
        this.indexOptions = this.indexScope.querySelector('#indexSettings');
        this.indexProgress = this.indexScope.querySelector('#indexProgress');
        this.progressFill = this.indexScope.querySelector('#progressFill');
        this.progressText = this.indexScope.querySelector('#progressText');
        this.progressCount = this.indexScope.querySelector('#progressCount');
        this.progressDetails = this.indexScope.querySelector('#progressDetails');
        this.forceToggle = this.indexScope.querySelector('#forceRebuildToggle');

        this.initializeEventListeners();
        this.loadSettings();
    }

    initializeEventListeners() {
        // 关闭按钮
        this.closeBtn.addEventListener('click', () => this.close());

        // 点击背景关闭
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.close();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.close();
            }
        });

        // Tab切换
        this.tabs.forEach(tab => {
            tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
        });

        // 保存设置
        this.modal.querySelector('#saveSettings').addEventListener('click', () => this.saveSettings());

        // 测试连接
        this.modal.querySelector('#testConnection').addEventListener('click', () => this.testConnection());

        // 索引功能按钮（在设置弹窗的索引tab中）
        const settingsStartIndexBtn = this.modal.querySelector('#settingsStartIndexBtn');
        const settingsCancelIndexBtn = this.modal.querySelector('#settingsCancelIndexBtn');

        if (settingsStartIndexBtn) {
            settingsStartIndexBtn.addEventListener('click', () => this.startIndexing());
        }
        if (settingsCancelIndexBtn) {
            settingsCancelIndexBtn.addEventListener('click', () => this.cancelIndexing());
        }

        // 数据管理按钮
        const exportJsonBtn = this.modal.querySelector('#exportJsonBtn');
        const exportCsvBtn = this.modal.querySelector('#exportCsvBtn');
        const importJsonBtn = this.modal.querySelector('#importJsonBtn');
        const importCsvBtn = this.modal.querySelector('#importCsvBtn');
        const openDataManagerBtn = this.modal.querySelector('#openDataManagerBtn');
        const migrateDataBtn = this.modal.querySelector('#migrateDataBtn');

        if (exportJsonBtn) {
            exportJsonBtn.addEventListener('click', () => this.exportData('json'));
        }
        if (exportCsvBtn) {
            exportCsvBtn.addEventListener('click', () => this.exportData('csv'));
        }
        if (importJsonBtn) {
            importJsonBtn.addEventListener('click', () => this.importData('json'));
        }
        if (importCsvBtn) {
            importCsvBtn.addEventListener('click', () => this.importData('csv'));
        }
        if (openDataManagerBtn) {
            openDataManagerBtn.addEventListener('click', () => window.open('data-manager.html', '_blank'));
        }
        if (migrateDataBtn) {
            migrateDataBtn.addEventListener('click', () => this.migrateData());
        }
    }

    open() {
        this.modal.classList.add('show');
        document.body.classList.add('modal-open');
        // 默认展示文字API设置页
        this.switchTab('text');
        this.updateApiStatus();
        this.updateDataStats();
    }

    close() {
        this.modal.classList.remove('show');
        document.body.classList.remove('modal-open');
    }

    switchTab(tabName) {
        // 切换tab激活状态
        this.tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });

        // 切换内容显示
        this.contents.forEach(content => {
            content.style.display = content.id === `${tabName}Settings` ? 'block' : 'none';
        });

        // 切换底部按钮显示
        const footerApi = document.getElementById('footerApi');
        const footerIndex = document.getElementById('footerIndex');
        if (tabName === 'index') {
            footerApi.style.display = 'none';
            footerIndex.style.display = 'flex';
        } else {
            footerApi.style.display = 'flex';
            footerIndex.style.display = 'none';
        }
    }

    // 设置弹窗里的索引逻辑（作用域限定在本弹窗内）
    async startIndexing() {
        if (!window.imageIndexer) {
            this.showToast('图片索引器未加载', 'error');
            return;
        }

        // 读取选项与强制重建
        const indexType = this.indexScope.querySelector('input[name="indexType"]:checked')?.value || 'all';
        const forceRebuildEnabled = !!(this.forceToggle && this.forceToggle.checked);

        console.log('开始索引:', { indexType, forceRebuildEnabled });

        // 显示进度
        this.isIndexing = true;
        this.indexProgress.style.display = 'block';
        
        const startBtn = this.modal.querySelector('#settingsStartIndexBtn');
        const cancelBtn = this.modal.querySelector('#settingsCancelIndexBtn');
        
        if (startBtn) {
            startBtn.disabled = true;
            startBtn.textContent = '索引中...';
        }
        if (cancelBtn) {
            cancelBtn.textContent = '强制停止';
        }

        try {
            // 设置进度回调
            window.imageIndexer.setCallbacks(
                (progress) => this.updateIndexProgress(progress),
                (results) => this.onIndexComplete(results)
            );

            // 根据选择的类型开始索引
            if (forceRebuildEnabled || indexType === 'rebuild') {
                console.log('开始重建索引...');
                await window.imageIndexer.rebuildIndexing();
            } else if (indexType === 'untagged') {
                console.log('开始索引未打标图片...');
                await this.startUntaggedIndexing();
            } else {
                console.log('开始索引全部图片...');
                await window.imageIndexer.startIndexing();
            }
        } catch (error) {
            console.error('索引失败:', error);
            this.showIndexError(error.message);
        }
    }

    async startUntaggedIndexing() {
        const allImages = await window.imageIndexer.scanDirectory();
        const untagged = allImages.filter(p => {
            const meta = window.imagesMetadata?.getImage(p);
            return !meta || !meta.tags || meta.tags.length === 0;
        });
        if (untagged.length === 0) {
            this.onIndexComplete({ total: allImages.length, success: 0, cached: allImages.length, skipped: 0, errors: 0, message: '所有图片都已打标' });
            return;
        }
        await window.imageIndexer.batchIndexImages(untagged);
    }

    updateIndexProgress(progress) {
        const percentage = progress.total > 0 ? (progress.processed / progress.total) * 100 : 0;
        this.progressFill.style.width = `${Math.round(percentage)}%`;
        
        // 更新主进度文本
        if (progress.currentFile) {
            this.progressText.textContent = `正在处理: ${progress.currentFile}`;
        } else if (progress.isIndexing) {
            this.progressText.textContent = '正在索引图片...';
        } else {
            this.progressText.textContent = '准备开始...';
        }
        
        // 更新计数器
        this.progressCount.textContent = `${progress.processed} / ${progress.total}`;
        
        // 更新详细信息，区分缺失文件和真正的错误
        let details = [];
        
        if (progress.errors > 0) {
            details.push(`❌ 错误: ${progress.errors} 张`);
        }
        
        if (progress.missing > 0) {
            details.push(`⚠️ 缺失: ${progress.missing} 张`);
        }
        
        const successCount = progress.processed - (progress.errors || 0) - (progress.missing || 0);
        if (successCount > 0) {
            details.push(`✅ 成功: ${successCount} 张`);
        }
        
        if (details.length > 0) {
            this.progressDetails.textContent = details.join(', ');
        } else {
            this.progressDetails.textContent = '正在准备...';
        }
        
        // 添加进度百分比显示
        const progressBar = this.progressFill.parentElement;
        if (progressBar && !progressBar.querySelector('.progress-percentage')) {
            const percentageSpan = document.createElement('span');
            percentageSpan.className = 'progress-percentage';
            percentageSpan.style.cssText = `
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-size: 12px;
                font-weight: bold;
                text-shadow: 0 1px 2px rgba(0,0,0,0.5);
                z-index: 1;
            `;
            progressBar.appendChild(percentageSpan);
            progressBar.style.position = 'relative';
        }
        
        const percentageSpan = progressBar?.querySelector('.progress-percentage');
        if (percentageSpan) {
            percentageSpan.textContent = `${Math.round(percentage)}%`;
        }
    }

    async onIndexComplete(results) {
        this.isIndexing = false;
        this.progressFill.style.width = '100%';
        
        // 根据结果类型设置不同的文本
        if (results.errors === 0 && (results.missing || 0) === 0) {
            this.progressText.textContent = '索引完成！🎉';
        } else {
            this.progressText.textContent = '索引完成（部分错误）';
        }
        
        this.progressCount.textContent = `${results.total} / ${results.total}`;

        // 构建详细信息
        let details = [];
        
        if (results.cached > 0) {
            details.push(`🔄 使用缓存: ${results.cached} 张`);
        }
        
        if (results.success > 0) {
            details.push(`✨ 新分析: ${results.success} 张`);
        }
        
        if (results.skipped > 0) {
            details.push(`⏭️ 跳过: ${results.skipped} 张`);
        }
        
        if (results.missing > 0) {
            details.push(`⚠️ 文件缺失: ${results.missing} 张`);
        }
        
        if (results.errors > 0) {
            details.push(`❌ 处理错误: ${results.errors} 张`);
        }
        
        // 显示耗时信息
        if (results.duration) {
            const durationText = results.duration > 1000 
                ? `耗时: ${Math.round(results.duration / 1000)}s`
                : `耗时: ${results.duration}ms`;
            details.push(durationText);
        }
        
        this.progressDetails.textContent = details.length > 0 
            ? details.join(', ')
            : '处理完成';
        
        // 更新百分比显示
        const percentageSpan = this.progressFill.parentElement?.querySelector('.progress-percentage');
        if (percentageSpan) {
            percentageSpan.textContent = '100%';
            percentageSpan.style.color = results.errors > 0 ? '#ff9800' : '#4caf50'; // 有错误时显示橙色
        }

        const startBtn = this.modal.querySelector('#settingsStartIndexBtn') || this.modal.querySelector('#startIndexBtn');
        const cancelBtn = this.modal.querySelector('#settingsCancelIndexBtn') || this.modal.querySelector('#cancelIndexBtn');
        if (startBtn) {
            startBtn.disabled = false;
            startBtn.textContent = '索引完成';
        }
        if (cancelBtn) {
            cancelBtn.textContent = '关闭';
        }
        
        // 如果有缺失文件，提供建议
        if (results.missing > 0) {
            console.log(`📝 发现 ${results.missing} 个缺失文件，建议重新生成 manifest.json`);
            setTimeout(() => {
                if (confirm(`发现 ${results.missing} 个文件缺失。\n是否重新生成图片清单来同步文件？`)) {
                    // 这里可以调用重新生成manifest的功能
                    console.log('用户选择重新生成manifest');
                }
            }, 2000);
        }

        // 确保数据持久化并刷新界面
        if (results.success > 0 || results.cached > 0) {
            console.log('设置弹窗索引完成，开始保存数据并刷新界面...');

            // 确保数据已保存到 localStorage
            if (window.imagesMetadata && window.imagesMetadata.saveToStorage) {
                await window.imagesMetadata.saveToStorage();
                console.log('索引数据已保存到 localStorage');
            }

            // 刷新首页分类卡片
            if (window.imageCategoryManager) {
                await window.imageCategoryManager.refresh();
                if (typeof renderProjects === 'function') {
                    renderProjects();
                }
            }

            console.log('界面刷新完成，索引数据已持久化');
        }
    }

    showIndexError(message) {
        this.isIndexing = false;
        this.progressText.textContent = '索引失败';
        this.progressDetails.textContent = `错误: ${message}`;
        const startBtn = this.modal.querySelector('#settingsStartIndexBtn') || this.modal.querySelector('#startIndexBtn');
        const cancelBtn = this.modal.querySelector('#settingsCancelIndexBtn') || this.modal.querySelector('#cancelIndexBtn');
        if (startBtn) {
            startBtn.disabled = false;
            startBtn.textContent = '重试';
        }
        if (cancelBtn) {
            cancelBtn.textContent = '关闭';
        }
    }

    cancelIndexing() {
        if (this.isIndexing) {
            if (!confirm('索引正在进行中，确定要取消吗？')) return;
            // 尝试停止索引器
            if (window.imageIndexer && window.imageIndexer.isIndexing) {
                window.imageIndexer.stopIndexing();
            }
        }
        this.resetIndexUI();
    }

    resetIndexUI() {
        this.isIndexing = false;
        if (this.indexProgress) {
            this.indexProgress.style.display = 'none';
        }
        
        const startBtn = this.modal.querySelector('#settingsStartIndexBtn');
        const cancelBtn = this.modal.querySelector('#settingsCancelIndexBtn');
        
        if (startBtn) {
            startBtn.disabled = false;
            startBtn.textContent = '开始索引';
        }
        if (cancelBtn) {
            cancelBtn.textContent = '取消';
        }
    }

    loadSettings() {
        // 加载文字API设置
        const chatApiKey = localStorage.getItem('chat_openrouter_api_key') || localStorage.getItem('openrouter_api_key');
        const chatModel = localStorage.getItem('chat_openrouter_model') || localStorage.getItem('openrouter_model') || 'anthropic/claude-3.5-sonnet';

        if (chatApiKey) {
            document.getElementById('apiKey').value = chatApiKey;
        }
        document.getElementById('modelSelect').value = chatModel;

        // 加载图像API设置
        const imageApiKey = localStorage.getItem('openrouter_api_key');
        const imageModel = localStorage.getItem('openrouter_model') || 'anthropic/claude-3.5-sonnet';

        if (imageApiKey) {
            document.getElementById('imageApiKey').value = imageApiKey;
        }
        document.getElementById('imageModelSelect').value = imageModel;

        // 更新状态显示
        this.updateApiStatus();
    }

    saveSettings() {
        // 保存文字API设置
        const chatApiKey = document.getElementById('apiKey').value.trim();
        const chatModel = document.getElementById('modelSelect').value;

        if (chatApiKey) {
            localStorage.setItem('openrouter_api_key', chatApiKey);
            localStorage.setItem('chat_openrouter_api_key', chatApiKey); // 兼容聊天页面
        } else {
            localStorage.removeItem('openrouter_api_key');
            localStorage.removeItem('chat_openrouter_api_key');
        }
        localStorage.setItem('openrouter_model', chatModel);
        localStorage.setItem('chat_openrouter_model', chatModel); // 兼容聊天页面

        // 保存图像API设置
        const imageApiKey = document.getElementById('imageApiKey').value.trim();
        const imageModel = document.getElementById('imageModelSelect').value;

        if (imageApiKey) {
            localStorage.setItem('openrouter_api_key', imageApiKey);
        } else {
            localStorage.removeItem('openrouter_api_key');
        }
        localStorage.setItem('openrouter_model', imageModel);

        // 更新全局API实例
        if (window.openrouterAPI) {
            window.openrouterAPI.apiKey = imageApiKey;
            window.openrouterAPI.model = imageModel;
        }

        this.updateApiStatus();
        this.close();

        // 显示保存成功提示
        this.showToast('设置已保存');
    }

    // 数据管理功能
    exportData(format) {
        if (!window.imagesMetadata) {
            this.showToast('图片元数据不可用', 'error');
            return;
        }

        try {
            const data = window.imagesMetadata.getAllImages();
            const fileName = `images_metadata_${new Date().toISOString().split('T')[0]}.${format}`;
            
            if (format === 'json') {
                this.downloadFile(JSON.stringify(data, null, 2), fileName, 'application/json');
            } else if (format === 'csv') {
                const csv = this.convertToCSV(data);
                this.downloadFile(csv, fileName, 'text/csv');
            }
            
            this.showToast(`数据已导出为 ${format.toUpperCase()} 格式`);
        } catch (error) {
            console.error('导出失败:', error);
            this.showToast('导出失败: ' + error.message, 'error');
        }
    }

    importData(format) {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = format === 'json' ? '.json' : '.csv';
        
        input.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            try {
                const text = await file.text();
                let data;
                
                if (format === 'json') {
                    data = JSON.parse(text);
                } else if (format === 'csv') {
                    data = this.parseCSV(text);
                }
                
                if (window.imagesMetadata && data) {
                    await window.imagesMetadata.importData(data);
                    this.showToast('数据导入成功');
                    
                    // 刷新界面
                    if (window.imageCategoryManager) {
                        await window.imageCategoryManager.refresh();
                        if (typeof renderProjects === 'function') {
                            renderProjects();
                        }
                    }
                }
            } catch (error) {
                console.error('导入失败:', error);
                this.showToast('导入失败: ' + error.message, 'error');
            }
        };
        
        input.click();
    }

    migrateData() {
        if (confirm('确定要从localStorage迁移数据吗？这将覆盖现有数据。')) {
            try {
                if (window.imagesMetadata && window.imagesMetadata.migrateFromLocalStorage) {
                    window.imagesMetadata.migrateFromLocalStorage();
                    this.showToast('数据迁移完成');
                } else {
                    this.showToast('迁移功能不可用', 'error');
                }
            } catch (error) {
                console.error('迁移失败:', error);
                this.showToast('迁移失败: ' + error.message, 'error');
            }
        }
    }

    convertToCSV(data) {
        if (!Array.isArray(data) || data.length === 0) {
            return 'path,description,tags,classifications\n';
        }
        
        const headers = ['path', 'description', 'tags', 'classifications'];
        const csvContent = [headers.join(',')];
        
        data.forEach(item => {
            const row = [
                `"${item.path || ''}"`,
                `"${(item.description || '').replace(/"/g, '""')}"`,
                `"${Array.isArray(item.tags) ? item.tags.join(';') : ''}"`,
                `"${item.classifications ? JSON.stringify(item.classifications).replace(/"/g, '""') : ''}"`
            ];
            csvContent.push(row.join(','));
        });
        
        return csvContent.join('\n');
    }

    parseCSV(text) {
        const lines = text.split('\n').filter(line => line.trim());
        if (lines.length < 2) return [];
        
        const headers = lines[0].split(',').map(h => h.trim());
        const data = [];
        
        for (let i = 1; i < lines.length; i++) {
            const values = this.parseCSVLine(lines[i]);
            if (values.length >= headers.length) {
                const item = {};
                headers.forEach((header, index) => {
                    const value = values[index] || '';
                    if (header === 'tags') {
                        item[header] = value ? value.split(';') : [];
                    } else if (header === 'classifications') {
                        try {
                            item[header] = value ? JSON.parse(value) : null;
                        } catch {
                            item[header] = null;
                        }
                    } else {
                        item[header] = value;
                    }
                });
                data.push(item);
            }
        }
        
        return data;
    }

    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === '"') {
                if (inQuotes && line[i + 1] === '"') {
                    current += '"';
                    i++;
                } else {
                    inQuotes = !inQuotes;
                }
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current.trim());
        return result;
    }

    downloadFile(content, fileName, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    async testConnection() {
        const activeTab = document.querySelector('.settings-tab.active').dataset.tab;
        const testBtn = document.getElementById('testConnection');

        testBtn.disabled = true;
        testBtn.textContent = '测试中...';

        try {
            if (activeTab === 'text') {
                // 测试文字API
                const apiKey = document.getElementById('apiKey').value.trim();
                const model = document.getElementById('modelSelect').value;

                if (!apiKey) {
                    throw new Error('请先输入API Key');
                }

                const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: [{ role: 'user', content: 'Hello' }],
                        max_tokens: 10
                    })
                });

                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status}`);
                }

                this.showToast('文字API连接成功');
                this.updateApiStatus('chat', 'connected');
            } else {
                // 测试图像API
                const apiKey = document.getElementById('imageApiKey').value.trim();
                const model = document.getElementById('imageModelSelect').value;

                if (!apiKey) {
                    throw new Error('请先输入API Key');
                }

                const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: [{ role: 'user', content: 'Hello' }],
                        max_tokens: 10
                    })
                });

                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status}`);
                }

                this.showToast('图像API连接成功');
                this.updateApiStatus('image', 'connected');
            }
        } catch (error) {
            this.showToast(error.message, 'error');
            this.updateApiStatus(activeTab === 'text' ? 'chat' : 'image', 'error');
        } finally {
            testBtn.disabled = false;
            testBtn.textContent = '测试连接';
        }
    }

    updateApiStatus(type = null, status = null) {
        const updateStatus = (statusId, hasKey) => {
            const statusEl = document.getElementById(statusId);
            if (status) {
                statusEl.className = `api-status ${status}`;
                statusEl.querySelector('.status-text').textContent =
                    status === 'connected' ? '连接成功' :
                    status === 'error' ? '连接失败' : '未连接';
            } else {
                statusEl.className = hasKey ? 'api-status' : 'api-status';
                statusEl.querySelector('.status-text').textContent = hasKey ? '已配置' : '未连接';
            }
        };

        if (!type || type === 'chat') {
            const chatApiKey = document.getElementById('apiKey').value.trim();
            updateStatus('apiStatus', !!chatApiKey);
        }

        if (!type || type === 'image') {
            const imageApiKey = document.getElementById('imageApiKey').value.trim();
            updateStatus('imageApiStatus', !!imageApiKey);
        }
    }

    showToast(message, type = 'success') {
        // 简单的toast提示
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff3b30' : '#34c759'};
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            z-index: 10000;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // 更新数据统计
    updateDataStats() {
        const statTotalImages = document.getElementById('statTotalImages');
        const statIndexedImages = document.getElementById('statIndexedImages');
        
        if (window.imagesMetadata) {
            const stats = window.imagesMetadata.getStats();
            if (statTotalImages) statTotalImages.textContent = stats.totalImages || 0;
            if (statIndexedImages) statIndexedImages.textContent = stats.indexedImages || 0;
        } else {
            if (statTotalImages) statTotalImages.textContent = '-';
            if (statIndexedImages) statIndexedImages.textContent = '-';
        }
    }

    // 索引功能方法
    async startIndexing() {
        if (!window.imageIndexer) {
            this.showToast('图片索引器未加载', 'error');
            return;
        }

        try {
            // 首先让用户选择文件夹
            const selectedFiles = await this.selectDirectory();
            if (!selectedFiles || selectedFiles.length === 0) {
                this.showToast('未选择任何文件夹或文件', 'warning');
                return;
            }

            // 获取选择的索引类型
            const indexType = document.querySelector('input[name="indexType"]:checked')?.value || 'all';
            const forceRebuildEnabled = document.getElementById('forceRebuildToggle')?.checked || false;

            const startBtn = document.getElementById('startIndexBtn');
            const cancelBtn = document.getElementById('cancelIndexBtn');
            const progressSection = document.getElementById('indexProgress');

            if (!startBtn || !cancelBtn || !progressSection) {
                this.showToast('索引界面元素未找到', 'error');
                return;
            }

            startBtn.disabled = true;
            startBtn.textContent = '索引中...';
            cancelBtn.textContent = '强制停止';
            progressSection.style.display = 'block';

            // 设置进度回调
            window.imageIndexer.setCallbacks(
                (progress) => this.updateIndexProgress(progress),
                (results) => this.onIndexComplete(results)
            );

            // 根据选择的类型开始索引
            if (forceRebuildEnabled || indexType === 'rebuild') {
                await window.imageIndexer.rebuildIndexingFromFiles(selectedFiles);
            } else if (indexType === 'untagged') {
                await this.startUntaggedIndexingFromFiles(selectedFiles);
            } else {
                await window.imageIndexer.startIndexingFromFiles(selectedFiles);
            }

        } catch (error) {
            console.error('索引失败:', error);
            this.showToast(error.message, 'error');
            this.resetIndexUI();
        }
    }

    /**
     * 选择文件夹（为确保用户激活不丢失，优先使用 <input type="file" webkitdirectory>，避免在 await 之后再触发 click）
     * @returns {Promise<File[]>} 选择的文件列表
     */
    selectDirectory() {
        return new Promise((resolve) => {
            // 直接使用 input[webkitdirectory]，保证在用户点击同一事件栈内触发系统文件选择框
            const input = document.createElement('input');
            input.type = 'file';
            input.webkitdirectory = true;
            input.multiple = true;
            input.accept = 'image/*';
            input.style.display = 'none';

            const cleanup = () => { if (input.parentNode) document.body.removeChild(input); };
            input.addEventListener('change', (e) => {
                const list = e.target.files;
                const files = list ? Array.from(list) : [];
                if (files.length > 0) {
                    console.log(`用户选择了包含 ${files.length} 个文件的文件夹`);
                }
                resolve(files);
                cleanup();
            }, { once: true });

            document.body.appendChild(input);
            input.click();
        });
    }

    async startUntaggedIndexing() {
        // 这个方法可以从IndexModal类复制或者调用imageIndexer的相应方法
        await window.imageIndexer.startIndexing();
    }

    async startUntaggedIndexingFromFiles(selectedFiles) {
        // 从选择的文件中索引未标记的图片
        await window.imageIndexer.startIndexingFromFiles(selectedFiles);
    }

    cancelIndexing() {
        if (window.imageIndexer && window.imageIndexer.isIndexing) {
            window.imageIndexer.stopIndexing();
        }
        this.resetIndexUI();
    }

    updateIndexProgress(progress) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const progressCount = document.getElementById('progressCount');

        if (progressFill) {
            progressFill.style.width = `${progress.percentage}%`;
        }
        if (progressText) {
            progressText.textContent = progress.status;
        }
        if (progressCount) {
            progressCount.textContent = `${progress.completed} / ${progress.total}`;
        }
    }

    async onIndexComplete(results) {
        const startBtn = document.getElementById('startIndexBtn');
        const cancelBtn = document.getElementById('cancelIndexBtn');
        const progressDetails = document.getElementById('progressDetails');

        if (startBtn) {
            startBtn.disabled = false;
            startBtn.textContent = '索引完成';
        }
        if (cancelBtn) {
            cancelBtn.textContent = '关闭';
        }

        let detailsText = '';
        if (results.cached > 0) {
            detailsText += `💾 缓存: ${results.cached} 张`;
        }
        if (results.success > 0) {
            if (detailsText) detailsText += ', ';
            detailsText += `✨ 新分析: ${results.success} 张`;
        }
        if (results.skipped > 0) {
            if (detailsText) detailsText += ', ';
            detailsText += `⏭️ 跳过: ${results.skipped} 张`;
        }
        if (results.errors > 0) {
            if (detailsText) detailsText += ', ';
            detailsText += `❌ 失败: ${results.errors} 张`;
        }

        if (progressDetails) {
            progressDetails.textContent = detailsText || '处理完成';
        }

        // 确保数据持久化并刷新界面
        if (results.success > 0 || results.cached > 0) {
            console.log('文件上传索引完成，开始保存数据并刷新界面...');

            // 确保数据已保存到 localStorage
            if (window.imagesMetadata && window.imagesMetadata.saveToStorage) {
                await window.imagesMetadata.saveToStorage();
                console.log('索引数据已保存到 localStorage');
            }

            // 刷新分类数据
            if (window.imageCategoryManager) {
                await window.imageCategoryManager.refresh();
                renderProjects();
            }

            console.log('界面刷新完成，索引数据已持久化');
        }

        this.showToast('索引完成！');

        // 3秒后重置UI
        setTimeout(() => {
            this.resetIndexUI();
        }, 3000);
    }

    resetIndexUI() {
        const startBtn = document.getElementById('startIndexBtn');
        const cancelBtn = document.getElementById('cancelIndexBtn');
        const progressSection = document.getElementById('indexProgress');

        if (startBtn) {
            startBtn.disabled = false;
            startBtn.textContent = '开始索引';
        }
        if (cancelBtn) {
            cancelBtn.textContent = '取消';
        }
        if (progressSection) {
            progressSection.style.display = 'none';
        }
    }
}

// 添加卡片点击事件
document.addEventListener('click', (e) => {
    const projectCard = e.target.closest('.project-card');
    if (projectCard) {
        const category = projectCard.dataset.category;
        const type = projectCard.dataset.type;

        // 跳转到瀑布流页面，传递分类类型和具体分类
        const params = new URLSearchParams({
            type: type,
            category: category
        });
        window.location.href = `gallery.html?${params.toString()}`;
    }
});
