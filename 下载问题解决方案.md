# Excel文件下载问题解决方案

## 问题描述
点击"创建Excel文件"后，浏览器显示下载成功，但本地找不到文件，或者文件名变成随机UUID。

## 原因分析
这通常是由于以下原因造成的：
1. 浏览器安全策略限制
2. 下载目录设置问题
3. 文件名包含特殊字符
4. MIME类型不正确

## 解决方案

### 方案1：检查Chrome下载设置
1. 打开Chrome浏览器
2. 点击右上角三个点菜单 → 设置
3. 在左侧点击"高级" → "下载内容"
4. 检查"下载前询问每个文件的保存位置"是否开启
5. 检查默认下载位置是否正确

### 方案2：手动指定下载位置
1. 在Chrome设置中开启"下载前询问每个文件的保存位置"
2. 重新点击"创建Excel文件"
3. 浏览器会弹出保存对话框，手动选择保存位置和文件名

### 方案3：使用传统CSV导出
如果Excel下载仍有问题，可以暂时使用：
1. 点击"导出CSV表格"按钮
2. 将CSV文件另存为.xlsx格式
3. 在Excel中打开并编辑

### 方案4：检查文件是否在垃圾桶
有时候文件可能被意外删除：
1. 打开访达(Finder)
2. 查看垃圾桶
3. 搜索"images_index"或"xlsx"

### 方案5：使用终端查找文件
```bash
# 在下载目录搜索Excel文件
find ~/Downloads -name "*.xlsx" -type f 2>/dev/null

# 在整个用户目录搜索（可能较慢）
find ~ -name "*images_index*" -type f 2>/dev/null
```

## 临时解决方案

如果上述方法都不行，我可以为你创建一个替代的下载方法：

### 使用FileSaver.js库
这是一个更可靠的文件下载库，专门解决浏览器下载问题。

### 使用Data URI方案
将Excel文件编码为Data URI，强制浏览器下载。

## 测试步骤

1. 先清空浏览器下载历史
2. 重新访问页面
3. 打开浏览器开发者工具(F12)
4. 点击"创建Excel文件"
5. 查看控制台输出和网络请求
6. 检查下载目录

## 联系我
如果问题仍然存在，请提供：
- 浏览器版本信息
- 操作系统版本
- 控制台错误信息
- 下载设置截图