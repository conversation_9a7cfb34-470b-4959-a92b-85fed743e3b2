/**
 * 图片索引器单元测试
 * 测试核心功能：文件名解析、URL构建、清单处理等
 */

// 模拟环境
global.fetch = jest.fn();
global.Image = class {
    constructor() {
        this.onload = null;
        this.onerror = null;
        this.src = '';
    }
    
    set src(value) {
        this._src = value;
        // 模拟图片加载成功
        setTimeout(() => {
            if (this.onload) this.onload();
        }, 10);
    }
    
    get src() {
        return this._src;
    }
};

// 模拟Window对象
global.window = {
    imagesMetadata: null,
    imageIndexer: null
};

// 导入被测试的类
const ImageIndexer = require('../image-indexer.js');

describe('ImageIndexer', () => {
    let indexer;
    
    beforeEach(() => {
        indexer = new ImageIndexer();
        jest.clearAllMocks();
    });

    describe('buildImageUrl', () => {
        test('应该正确编码中文目录和文件名', () => {
            const result = indexer.buildImageUrl('示例图片', 'Saved Screens 16.png');
            expect(result).toBe('%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%87/Saved%20Screens%2016.png');
        });

        test('应该确保目录以斜杠结尾', () => {
            const result1 = indexer.buildImageUrl('示例图片/', 'test.png');
            const result2 = indexer.buildImageUrl('示例图片', 'test.png');
            expect(result1).toBe(result2);
        });

        test('应该正确处理特殊字符', () => {
            const result = indexer.buildImageUrl('test', 'file with spaces & symbols.png');
            expect(result).toBe('test/file%20with%20spaces%20%26%20symbols.png');
        });
    });

    describe('文件名解析', () => {
        test('应该正确解析Saved Screens文件名', () => {
            const pattern = /^Saved Screens (\d+)\.png$/i;
            
            expect('Saved Screens 16.png'.match(pattern)?.[1]).toBe('16');
            expect('Saved Screens 89.png'.match(pattern)?.[1]).toBe('89');
            expect('Other File.png'.match(pattern)).toBeNull();
        });

        test('应该正确解析App截图文件名', () => {
            const patterns = [
                /^(\w+) iOS (\d+)\.png$/i,
                /^(\w+) Android (\d+)\.png$/i
            ];
            
            const testFiles = [
                'MLS iOS 13.png',
                'Fuse iOS 115.png', 
                'Atoms iOS 74.png'
            ];
            
            testFiles.forEach(filename => {
                const match = filename.match(patterns[0]);
                expect(match).not.toBeNull();
                expect(match[1]).toBeTruthy(); // App name
                expect(match[2]).toBeTruthy(); // Index
            });
        });
    });

    describe('fetchManifestList', () => {
        test('应该正确处理新格式manifest', async () => {
            const mockManifest = {
                files: ['Saved Screens 16.png', 'Saved Screens 17.png'],
                stats: { totalFiles: 2 }
            };
            
            global.fetch.mockResolvedValueOnce({
                ok: true,
                json: async () => mockManifest
            });
            
            const result = await indexer.fetchManifestList('示例图片/');
            
            expect(result).toHaveLength(2);
            expect(result[0]).toContain('Saved%20Screens%2016.png');
            expect(fetch).toHaveBeenCalledWith(
                expect.stringContaining('manifest.json'),
                expect.objectContaining({ cache: 'no-store' })
            );
        });

        test('应该处理旧格式manifest（直接数组）', async () => {
            const mockManifest = ['file1.png', 'file2.png'];
            
            global.fetch.mockResolvedValueOnce({
                ok: true,
                json: async () => mockManifest
            });
            
            const result = await indexer.fetchManifestList('test/');
            
            expect(result).toHaveLength(2);
            expect(result[0]).toContain('file1.png');
        });

        test('应该在manifest不存在时返回null', async () => {
            global.fetch.mockResolvedValueOnce({
                ok: false,
                status: 404
            });
            
            const result = await indexer.fetchManifestList('test/');
            expect(result).toBeNull();
        });
    });

    describe('fetchWithTimeout', () => {
        test('应该在超时时抛出错误', async () => {
            // 模拟超时
            indexer.REQUEST_TIMEOUT = 100;
            
            global.fetch.mockImplementationOnce(() => 
                new Promise(resolve => setTimeout(resolve, 200))
            );
            
            await expect(
                indexer.fetchWithTimeout('http://test.com')
            ).rejects.toThrow();
        });

        test('应该对404状态码直接返回，不重试', async () => {
            global.fetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: 'Not Found'
            });
            
            const result = await indexer.fetchWithTimeout('http://test.com');
            
            expect(result.status).toBe(404);
            expect(fetch).toHaveBeenCalledTimes(1); // 不应该重试
        });

        test('应该对网络错误进行重试', async () => {
            global.fetch
                .mockRejectedValueOnce(new Error('Network Error'))
                .mockRejectedValueOnce(new Error('Network Error'))
                .mockResolvedValueOnce({
                    ok: true,
                    status: 200
                });
            
            const result = await indexer.fetchWithTimeout('http://test.com');
            
            expect(result.status).toBe(200);
            expect(fetch).toHaveBeenCalledTimes(3); // 初次 + 2次重试
        });
    });

    describe('checkImageExists', () => {
        test('应该使用HEAD请求检查图片存在性', async () => {
            global.fetch.mockResolvedValueOnce({
                ok: true,
                status: 200
            });
            
            const result = await indexer.checkImageExists('test.png');
            
            expect(result.exists).toBe(true);
            expect(fetch).toHaveBeenCalledWith('test.png', 
                expect.objectContaining({ method: 'HEAD' })
            );
        });

        test('应该正确报告404错误', async () => {
            global.fetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: 'Not Found'
            });
            
            const result = await indexer.checkImageExists('missing.png');
            
            expect(result.exists).toBe(false);
            expect(result.error).toContain('404');
        });
    });

    describe('processConcurrent', () => {
        test('应该限制并发数量', async () => {
            const tasks = [1, 2, 3, 4, 5, 6, 7, 8];
            let currentConcurrent = 0;
            let maxConcurrent = 0;
            
            const taskHandler = async (task) => {
                currentConcurrent++;
                maxConcurrent = Math.max(maxConcurrent, currentConcurrent);
                
                await new Promise(resolve => setTimeout(resolve, 10));
                
                currentConcurrent--;
                return task * 2;
            };
            
            const results = await indexer.processConcurrent(
                tasks, taskHandler, 3 // 限制并发为3
            );
            
            expect(results).toEqual([2, 4, 6, 8, 10, 12, 14, 16]);
            expect(maxConcurrent).toBeLessThanOrEqual(3);
        });
    });

    describe('配置常量验证', () => {
        test('应该有合理的默认配置', () => {
            expect(indexer.IMAGE_DIR).toBe('示例图片/');
            expect(indexer.SUPPORTED_EXTENSIONS).toContain('.png');
            expect(indexer.CONCURRENCY_LIMIT).toBeGreaterThan(0);
            expect(indexer.CONCURRENCY_LIMIT).toBeLessThanOrEqual(10);
            expect(indexer.REQUEST_TIMEOUT).toBeGreaterThan(1000);
            expect(indexer.RETRY_ATTEMPTS).toBeGreaterThanOrEqual(1);
        });
    });

    describe('扫描策略集成测试', () => {
        test('应该优先使用manifest策略', async () => {
            const mockManifest = {
                files: ['test1.png', 'test2.png']
            };
            
            global.fetch.mockResolvedValueOnce({
                ok: true,
                json: async () => mockManifest
            });
            
            const result = await indexer.scanDirectory('test/');
            
            expect(result).toHaveLength(2);
            expect(result[0]).toContain('test1.png');
            
            // 应该只调用了manifest获取，没有fallback
            expect(fetch).toHaveBeenCalledTimes(1);
        });
    });
});

describe('性能测试', () => {
    let indexer;
    
    beforeEach(() => {
        indexer = new ImageIndexer();
    });

    test('URL构建性能', () => {
        const start = performance.now();
        
        for (let i = 0; i < 1000; i++) {
            indexer.buildImageUrl('示例图片/', `Saved Screens ${i}.png`);
        }
        
        const duration = performance.now() - start;
        expect(duration).toBeLessThan(100); // 应该在100ms内完成1000次
    });

    test('并发处理性能', async () => {
        const tasks = Array.from({ length: 50 }, (_, i) => i);
        const taskHandler = async (task) => {
            await new Promise(resolve => setTimeout(resolve, 1));
            return task;
        };
        
        const start = performance.now();
        await indexer.processConcurrent(tasks, taskHandler, 6);
        const duration = performance.now() - start;
        
        // 并发处理应该明显快于串行处理
        expect(duration).toBeLessThan(300); // 串行需要50ms，并发应该在300ms内
    });
});

// 导出用于其他测试文件
module.exports = { ImageIndexer };