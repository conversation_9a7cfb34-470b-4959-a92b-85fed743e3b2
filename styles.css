/* ====== Notion 风格变量 ====== */
:root {
    /* 基础色彩 */
    --fg-default: #37352F;
    --fg-muted: rgba(55,53,47,0.6);
    --bg-default: #FFFFFF;
    --bg-secondary: #F7F6F3;
    --border: rgba(55,53,47,0.16);
    --rule: rgba(55,53,47,0.09);

    /* 字体 */
    --font-body: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    --fs-body: 16px;
    --lh-body: 1.6;

    /* 间距 */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;

    /* 圆角 */
    --radius-1: 3px;
    --radius-2: 6px;
    --radius-3: 12px;

    /* 内容宽度 */
    --content-max: 1200px;
}

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-body);
    background-color: var(--bg-secondary);
    color: var(--fg-default);
    line-height: var(--lh-body);
    font-size: var(--fs-body);
}

body.modal-open {
    overflow: hidden;
}

.container {
    max-width: var(--content-max);
    margin: 0 auto;
    padding: var(--space-6);
}

/* 搜索区域 */
.search-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: var(--space-6);
    position: relative;
}

.search-container {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    width: 100%;
    max-width: 680px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    flex: 1;
    background-color: var(--bg-default);
    border: 1px solid var(--border);
    border-radius: 50px;
    padding: var(--space-3) var(--space-4);
    transition: all 0.2s ease;
}

.search-box:focus-within {
    border-color: var(--fg-default);
    box-shadow: 0 0 0 1px var(--fg-default);
}

.chat-btn,
.settings-btn {
    background: var(--bg-default);
    border: 1px solid var(--border);
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

/* 透明入口：放在设置按钮右侧，保持同样尺寸但完全透明 */
.ghost-entry {
    display: block;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: transparent;
    border: 1px solid transparent;
    cursor: pointer;
    flex-shrink: 0;
}
.ghost-entry:hover,
.ghost-entry:focus { outline: none; border-color: transparent; }

.chat-btn:hover,
.settings-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--fg-muted);
}

.chat-btn svg,
.settings-btn svg {
    width: 20px;
    height: 20px;
    color: var(--fg-muted);
}

.search-icon {
    color: var(--fg-muted);
    flex-shrink: 0;
    margin-right: var(--space-2);
}

#searchInput {
    flex: 1;
    min-width: 0;
    border: none;
    background: transparent;
    font-size: var(--fs-body);
    outline: none;
    color: var(--fg-default);
    font-family: var(--font-body);
}

#searchInput::placeholder {
    color: var(--fg-muted);
}

.start-btn {
    padding: var(--space-2) var(--space-4);
    border: none;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 500;
    background-color: var(--fg-default);
    color: var(--bg-default);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    flex-shrink: 0;
    font-family: var(--font-body);
}

.start-btn:hover {
    background-color: var(--fg-muted);
}

/* 搜索建议样式 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border);
    z-index: 1000;
    max-height: 450px;
    overflow-y: auto;
    margin-top: 8px;
    overflow: hidden;
}

.suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px 8px;
    border-bottom: 1px solid var(--rule);
}

.suggestions-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--fg-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.clear-history-btn {
    background: none;
    border: none;
    color: var(--fg-default);
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.clear-history-btn:hover {
    background-color: var(--bg-secondary);
}

.suggestions-list {
    padding: 8px 0;
}

.suggestion-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 14px;
    color: var(--fg-default);
}

.suggestion-item:hover {
    background-color: var(--bg-secondary);
}

.suggestion-item.active {
    background-color: var(--fg-default);
    color: var(--bg-default);
}

.suggestion-icon {
    width: 16px;
    height: 16px;
    margin-right: 12px;
    opacity: 0.6;
    flex-shrink: 0;
}

.suggestion-text {
    flex: 1;
    line-height: 1.4;
}

.suggestion-type {
    font-size: 12px;
    opacity: 0.6;
    margin-left: 8px;
}

/* 搜索建议类型图标 */
.suggestion-item[data-type="history"] .suggestion-icon {
    color: var(--fg-muted);
}

.suggestion-item[data-type="suggestion"] .suggestion-icon {
    color: var(--fg-default);
}

.suggestion-item[data-type="popular"] .suggestion-icon {
    color: #FF9500;
}

/* Tab 导航样式 */
.tab-navigation {
    display: flex;
    width: fit-content;
    gap: var(--space-1);
    background-color: var(--bg-default);
    border: 1px solid var(--border);
    border-radius: var(--radius-2);
    padding: var(--space-1);
    margin: 0 auto var(--space-6);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border: none;
    background: transparent;
    border-radius: var(--radius-1);
    font-size: 14px;
    font-weight: 500;
    color: var(--fg-muted);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-family: var(--font-body);
}

.tab-btn svg {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.tab-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--fg-default);
}

.tab-btn:hover svg {
    opacity: 1;
}

.tab-btn.active {
    background-color: var(--fg-default);
    color: var(--bg-default);
    font-weight: 500;
}

.tab-btn.active svg {
    opacity: 1;
}

/* 项目网格布局 */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-4);
    justify-items: center;
}

/* 项目卡片样式 */
.project-card {
    background: var(--bg-default);
    border: 1px solid var(--border);
    border-radius: var(--radius-2);
    overflow: hidden;
    transition: all 0.2s ease;
    cursor: pointer;
    width: 100%;
    max-width: 320px;
    position: relative;
}

.project-card:hover {
    border-color: var(--fg-muted);
    transform: translateY(-2px);
}

/* 项目图片区域 */
.project-images {
    position: relative;
    height: 300px;
    padding: 40px 20px;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    overflow: hidden;
}

.project-images img {
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
}

.project-images .main-image {
    height: 200px;
    z-index: 2;
}

.project-images .side-image {
    height: 180px;
    opacity: 0.9;
    z-index: 1;
}

.project-card:hover .project-images img {
    transform: scale(1.05);
}

/* 项目信息区域 */
.project-info {
    position: relative;
    padding: var(--space-5) var(--space-4);
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.project-category {
    display: inline-block;
    padding: var(--space-3) var(--space-6);
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    font-family: var(--font-body);
    transition: all 0.3s ease;
    background-color: var(--fg-default);
    color: white;
    align-self: center;
}

.category-stats {
    font-size: 14px;
    color: var(--fg-muted);
    font-weight: 500;
}

/* 统一标签样式 - 所有类别都使用黑色背景 */
.category-investment,
.category-detail,
.category-calendar,
.category-education,
.category-subscription,
.category-payment,
.category-promotion,
.category-operation,
.category-modal,
.category-list,
.category-button,
.category-navigation,
.category-register,
.category-payment-flow,
.category-onboarding {
    background-color: var(--fg-default) !important;
    color: white !important;
}

.project-category:hover {
    transform: scale(1.05);
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .projects-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 30px;
    }

    .container {
        padding: 20px 15px;
    }

    .search-section {
        margin-bottom: 40px;
    }

    .search-box {
        max-width: none; /* 移动端占满宽度 */
        border-radius: 24px;
    }

    #searchInput {
        font-size: 14px; /* 与下方控件一致 */
        padding: 12px 14px;
    }

    .start-btn {
        font-size: 14px;
        padding: 10px 14px;
    }

    /* 搜索建议响应式 */
    .search-suggestions {
        border-radius: 12px;
        margin-top: 6px;
    }

    /* 移动端下 Tab 导航占满宽度，避免被内容挤出；允许横向滚动 */
    .tab-navigation {
        display: flex;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .suggestion-item {
        padding: 10px 16px;
        font-size: 13px;
    }

    .suggestion-icon {
        width: 14px;
        height: 14px;
        margin-right: 10px;
    }
}

/* 图片覆盖层 */
.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.1));
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    padding: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .image-overlay {
    opacity: 1;
}

.image-count {
    background: rgba(255, 255, 255, 0.9);
    color: var(--fg-default);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

/* 索引按钮 */
.index-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    color: var(--fg-muted); /* 颜色更浅 */
    border: none; /* 移除外围线框 */
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    width: 40px;
    height: 40px;
}

.index-btn:hover {
    background: #1d1d1f;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(29, 29, 31, 0.3);
}

.index-btn:disabled {
    background: var(--bg-secondary);
    color: var(--fg-muted);
    border-color: var(--border);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.index-btn svg {
    width: 20px;
    height: 20px;
    stroke-width: 2;
}

/* 空状态 */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: var(--fg-muted);
}

.empty-state p {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.empty-state small {
    font-size: 14px;
    opacity: 0.8;
}

/* 分类卡片特殊样式 */
.category-card .project-images {
    position: relative;
    overflow: hidden;
}

.category-card .main-image,
.category-card .side-image {
    transition: transform 0.3s ease;
}

.category-card:hover .main-image {
    transform: scale(1.05);
}

.category-card:hover .side-image {
    transform: scale(1.1) translateX(5px);
}

/* 动态分类样式 */
.category-金融,
.category-健康与健身,
.category-教育,
.category-体育,
.category-旅行与交通,
.category-实用工具,
.category-支付方式,
.category-订单详情,
.category-我的账户与个人资料,
.category-导览与教程,
.category-促销与奖励,
.category-视图,
.category-卡片,
.category-按钮,
.category-进度指示器,
.category-标签,
.category-列表 {
    background: linear-gradient(135deg, #1d1d1f, #2c2c2e) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(29, 29, 31, 0.3);
}

/* 模态弹窗样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.modal-content {
    background: var(--bg-default);
    border: 1px solid var(--border);
    border-radius: var(--radius-2);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.95);
    transition: transform 0.2s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--fg-default);
    font-family: var(--font-body);
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-1);
    color: var(--fg-muted);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--fg-default);
}

.modal-body {
    padding: var(--space-6);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    gap: var(--space-3);
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--border);
    justify-content: flex-end;
}

/* 设置模态框样式 */
.settings-modal {
    max-width: 600px;
}

.settings-tabs {
    display: flex;
    border-bottom: 1px solid var(--border);
    background: var(--bg-secondary);
}

.settings-tab {
    background: none;
    border: none;
    padding: 16px 24px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--fg-muted);
    flex: 1;
    justify-content: center;
}

.settings-tab:hover {
    background: rgba(0, 122, 255, 0.1);
    color: #007aff;
}

.settings-tab.active {
    background: white;
    color: #007aff;
    border-bottom: 2px solid #007aff;
}

.settings-tab svg {
    width: 16px;
    height: 16px;
}

.settings-content {
    display: none;
}

.settings-content.active {
    display: block;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--fg-default);
    font-size: 14px;
}

.setting-group input,
.setting-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d1d6;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.setting-group input:focus,
.setting-group select:focus {
    outline: none;
    border-color: #007aff;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.help-text {
    display: block;
    margin-top: 6px;
    font-size: 12px;
    color: var(--fg-muted);
}

.help-text a {
    color: #007aff;
    text-decoration: none;
}

.help-text a:hover {
    text-decoration: underline;
}

.api-status {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3);
    background: var(--bg-secondary);
    border-radius: var(--radius-2);
    border: 1px solid var(--border);
}

.status-indicator {
    width: var(--space-2);
    height: var(--space-2);
    border-radius: 50%;
    background: var(--fg-muted);
}

.api-status.connected .status-indicator {
    background: #10b981;
}

.api-status.error .status-indicator {
    background: #ef4444;
}

.status-text {
    font-size: 14px;
    color: var(--fg-default);
}

/* 索引选项样式 */
.index-options h4 {
    margin: 0 0 var(--space-4) 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--fg-default);
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.option-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    padding: var(--space-4);
    border: 1px solid var(--border);
    border-radius: var(--radius-2);
    cursor: pointer;
    transition: all 0.2s ease;
}

.option-item:hover {
    border-color: var(--fg-muted);
    background: var(--bg-secondary);
}

.option-item input[type="radio"] {
    margin: 0;
    margin-top: 2px;
    accent-color: var(--fg-default);
}

.option-item input[type="radio"]:checked + .option-content {
    color: var(--fg-default);
}

.option-item:has(input:checked) {
    border-color: var(--fg-default);
    background: var(--bg-secondary);
}

.option-content {
    flex: 1;
}

.option-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: var(--space-1);
    color: var(--fg-default);
}

.option-desc {
    font-size: 13px;
    color: var(--fg-muted);
    line-height: 1.4;
}

/* 进度条样式 */
.index-progress {
    margin-top: var(--space-6);
}

.index-progress h4 {
    margin: 0 0 var(--space-4) 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--fg-default);
}

.progress-bar {
    width: 100%;
    height: var(--space-2);
    background: var(--border);
    border-radius: var(--space-1);
    overflow: hidden;
    margin-bottom: var(--space-3);
}

.progress-fill {
    height: 100%;
    background: var(--fg-default);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: var(--space-1);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2);
}

.progress-info span {
    font-size: 14px;
    color: var(--fg-default);
    font-weight: 500;
}

.progress-details {
    font-size: 13px;
    color: var(--fg-muted);
    line-height: 1.4;
}

/* 按钮样式 */
.btn {
    padding: var(--space-2) var(--space-5);
    border: 1px solid var(--border);
    border-radius: var(--radius-1);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
    font-family: var(--font-body);
}

.btn-primary {
    background: var(--fg-default);
    color: var(--bg-default);
    border-color: var(--fg-default);
}

.btn-primary:hover {
    background: var(--fg-muted);
    border-color: var(--fg-muted);
}

.btn-primary:disabled {
    background: #8E8E93;
    cursor: not-allowed;
}

.btn-secondary {
    background: var(--bg-default);
    color: var(--fg-default);
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    background: var(--bg-secondary);
    border-color: var(--fg-muted);
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* ====== 增强的AI搜索界面样式 ====== */

/* 增强的搜索建议框 */
.enhanced-suggestions {
    /* 继承原有的 .search-suggestions 样式 */
}

/* 搜索模式选择器 */
.search-mode-selector {
    background: var(--bg-default);
    border-bottom: 1px solid var(--rule);
    padding: 16px;
}

.mode-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 600;
    color: var(--fg-default);
}

.mode-close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--fg-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.mode-close-btn:hover {
    background: var(--bg-secondary);
    color: var(--fg-default);
}

.mode-options {
    display: grid;
    gap: 8px;
}

.mode-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius-2);
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--bg-default);
}

.mode-option:hover {
    background: var(--bg-secondary);
    border-color: var(--fg-muted);
}

.mode-option input[type="radio"] {
    margin: 0;
    cursor: pointer;
}

.mode-option input[type="radio"]:checked + .mode-content {
    color: var(--fg-default);
}

.mode-content {
    flex: 1;
    color: var(--fg-muted);
    transition: color 0.2s ease;
}

.mode-title {
    display: block;
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 2px;
}

.mode-desc {
    display: block;
    font-size: 12px;
    opacity: 0.8;
    line-height: 1.3;
}

/* 搜索模式指示器 */
.search-mode-indicator {
    padding: 8px 16px;
    border-top: 1px solid var(--rule);
    background: var(--bg-secondary);
}

.mode-toggle-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: var(--fg-muted);
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    width: 100%;
    justify-content: space-between;
}

.mode-toggle-btn:hover {
    background: var(--bg-default);
    color: var(--fg-default);
}

.mode-icon {
    font-size: 14px;
}

.mode-text {
    flex: 1;
    text-align: left;
    font-weight: 500;
}

.mode-arrow {
    font-size: 10px;
    transition: transform 0.2s ease;
}

.mode-toggle-btn.active .mode-arrow {
    transform: rotate(180deg);
}

/* 置信度指示器（用于画廊页面） */
.confidence-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    z-index: 2;
    border-radius: 2px 2px 0 0;
}

.masonry-item {
    position: relative;
}

.masonry-item[data-confidence] {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

/* 增强的空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--fg-muted);
}

.empty-state.error-state {
    color: #FF3B30;
}

.search-suggestions-empty {
    margin-top: 20px;
    text-align: left;
}

.search-suggestions-empty p {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--fg-default);
}

.search-suggestions-empty ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.search-suggestions-empty li {
    padding: 4px 0;
    font-size: 14px;
    line-height: 1.4;
}

.search-suggestions-empty li strong {
    color: var(--fg-default);
}

.search-suggestions-empty em {
    display: inline-block;
    margin-top: 12px;
    font-size: 12px;
    opacity: 0.7;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .search-mode-selector {
        padding: 12px;
    }

    .mode-options {
        gap: 6px;
    }

    .mode-option {
        padding: 10px;
    }

    .mode-title {
        font-size: 13px;
    }

    .mode-desc {
        font-size: 11px;
    }

    .search-suggestions {
        margin-top: 6px;
        max-height: 300px;
    }
}

/* AI搜索相关的动画 */
@keyframes searchPulse {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

.searching .search-icon {
    animation: searchPulse 1.5s ease-in-out infinite;
}

/* 搜索模式切换动画 */
.search-mode-selector {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 增强的建议项样式 */
.suggestion-item.ai-powered {
    border-left: 3px solid #007AFF;
}

.suggestion-item.ai-powered .suggestion-icon {
    color: #007AFF;
}

.suggestion-confidence {
    font-size: 10px;
    background: rgba(0, 122, 255, 0.1);
    color: #007AFF;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.loading-content {
    text-align: center;
    color: #666;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Toast */
.error-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc3545;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Fallback State Styles */
.fallback-mode .search-box input {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.fallback-mode .search-box input::placeholder {
    color: #6c757d;
}

/* Enhanced Error States */
.project-card.error {
    border: 2px dashed #dc3545;
    opacity: 0.6;
}

.project-card.error .card-title::after {
    content: " (加载失败)";
    color: #dc3545;
    font-size: 0.8em;
}

/* Degraded Mode Banner */
.degraded-mode-banner {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 8px 16px;
    text-align: center;
    font-size: 14px;
    margin-bottom: 16px;
    border-radius: 4px;
}

.degraded-mode-banner .close-btn {
    float: right;
    background: none;
    border: none;
    color: #856404;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
}

/* Mobile Error Handling */
@media (max-width: 768px) {
    .error-toast {
        top: 10px;
        right: 10px;
        left: 10px;
        right: 10px;
    }

    .loading-content {
        padding: 20px;
    }
}
