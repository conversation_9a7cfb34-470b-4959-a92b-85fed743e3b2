<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目展示页面</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Loading indicator -->
    <div id="loadingIndicator" class="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在加载图片库...</p>
        </div>
    </div>

    <div class="container">
        <!-- 搜索框 -->
        <div class="search-section">
            <div class="search-container">
                <div class="search-box">
                    <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21l-4.35-4.35"></path>
                    </svg>
                    <input type="text" id="searchInput" placeholder="搜索图片：输入关键词、描述或分类..." />
                    <button id="searchBtn" class="start-btn">开始</button>
                </div>
                <button class="chat-btn" id="chatBtn" title="聊天">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path>
                    </svg>
                </button>
                <button class="settings-btn" id="settingsBtn" title="设置">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="3"></circle>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                    </svg>
                </button>
                <!-- 透明入口：点击进入表格编辑器 -->
                <a href="table-editor.html" class="ghost-entry" aria-label="表格编辑器入口" title="表格编辑器"></a>
            </div>

            <!-- 增强的智能搜索建议下拉框 -->
            <div class="search-suggestions enhanced-suggestions" id="searchSuggestions" style="display: none;">
                <!-- 搜索模式选择器 -->
                <div class="search-mode-selector" id="searchModeSelector" style="display: none;">
                    <div class="mode-header">
                        <span>搜索模式</span>
                        <button class="mode-close-btn" id="closeModeSelector">×</button>
                    </div>
                    <div class="mode-options">
                        <label class="mode-option">
                            <input type="radio" name="searchMode" value="auto" checked>
                            <div class="mode-content">
                                <span class="mode-title">🤖 智能自动</span>
                                <span class="mode-desc">自动选择最佳搜索策略</span>
                            </div>
                        </label>
                        <label class="mode-option">
                            <input type="radio" name="searchMode" value="ai_semantic">
                            <div class="mode-content">
                                <span class="mode-title">🧠 AI语义</span>
                                <span class="mode-desc">理解查询意图，智能匹配</span>
                            </div>
                        </label>
                        <label class="mode-option">
                            <input type="radio" name="searchMode" value="hybrid">
                            <div class="mode-content">
                                <span class="mode-title">🔄 混合搜索</span>
                                <span class="mode-desc">结合多种算法的综合搜索</span>
                            </div>
                        </label>
                        <label class="mode-option">
                            <input type="radio" name="searchMode" value="traditional">
                            <div class="mode-content">
                                <span class="mode-title">🔍 传统匹配</span>
                                <span class="mode-desc">基于关键词的精确匹配</span>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="suggestions-header" id="suggestionsHeader" style="display: none;">
                    <span class="suggestions-title">搜索历史</span>
                    <button class="clear-history-btn" id="clearHistoryBtn">清除</button>
                </div>
                <div class="suggestions-list" id="suggestionsList">
                    <!-- 智能搜索建议将通过JavaScript动态生成 -->
                </div>

                <!-- 搜索模式指示器 -->
                <div class="search-mode-indicator" id="searchModeIndicator">
                    <button class="mode-toggle-btn" id="modeToggleBtn" title="切换搜索模式">
                        <span class="mode-icon">🤖</span>
                        <span class="mode-text">智能自动</span>
                        <span class="mode-arrow">▼</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Tab 导航 -->
        <div class="tab-navigation">
                <button class="tab-btn active" data-tab="category">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"></circle>
                        <circle cx="12" cy="3" r="1"></circle>
                        <circle cx="21" cy="12" r="1"></circle>
                        <circle cx="12" cy="21" r="1"></circle>
                        <circle cx="3" cy="12" r="1"></circle>
                    </svg>
                    类别
                </button>
                <button class="tab-btn" data-tab="component">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <rect x="9" y="9" width="6" height="6"></rect>
                    </svg>
                    控件
                </button>
                <button class="tab-btn" data-tab="flow">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"></polyline>
                    </svg>
                    流程
                </button>
            </div>

        <!-- 项目网格 -->
        <div class="projects-grid" id="projectsGrid">
            <!-- 项目卡片将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 图片索引弹窗 -->
    <div id="indexModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>图片索引设置</h3>
                <button id="closeIndexModal" class="modal-close">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="index-options">
                    <h4>选择索引范围</h4>
                    <div class="option-group">
                        <label class="option-item">
                            <input type="radio" name="indexType" value="all" checked>
                            <div class="option-content">
                                <div class="option-title">索引全部图片</div>
                                <div class="option-desc">扫描并索引目录中的所有图片文件</div>
                            </div>
                        </label>
                        <label class="option-item">
                            <input type="radio" name="indexType" value="untagged">
                            <div class="option-content">
                                <div class="option-title">仅索引未打标图片</div>
                                <div class="option-desc">只处理还没有标签和描述的图片</div>
                            </div>
                        </label>
                        <label class="option-item">
                            <input type="radio" name="indexType" value="rebuild">
                            <div class="option-content">
                                <div class="option-title">重建索引（已打标也重跑）</div>
                                <div class="option-desc">重新分析已打标图片并更新标签与描述</div>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="index-progress" id="indexProgress" style="display: none;">
                    <h4>索引进度</h4>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-info">
                        <span id="progressText">准备开始...</span>
                        <span id="progressCount">0 / 0</span>
                    </div>
                    <div class="progress-details" id="progressDetails"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancelIndexBtn" class="btn btn-secondary">取消</button>
                <button id="startIndexBtn" class="btn btn-primary">开始索引</button>
            </div>
        </div>
    </div>

    <!-- 设置弹窗 -->
    <div id="settingsModal" class="modal">
        <div class="modal-content settings-modal">
            <div class="modal-header">
                <h3>设置</h3>
                <button id="closeSettingsModal" class="modal-close">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>

            <!-- Tab 导航 -->
            <div class="settings-tabs">
                <button class="settings-tab active" data-tab="text">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path>
                    </svg>
                    文字API
                </button>
                <button class="settings-tab" data-tab="image">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="9" cy="9" r="2"></circle>
                        <path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                    </svg>
                    图像API
                </button>
                <button class="settings-tab" data-tab="index">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"></circle>
                        <circle cx="12" cy="3" r="1"></circle>
                        <circle cx="21" cy="12" r="1"></circle>
                        <circle cx="12" cy="21" r="1"></circle>
                        <circle cx="3" cy="12" r="1"></circle>
                    </svg>
                    索引
                </button>
                <button class="settings-tab" data-tab="data">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14,2 14,8 20,8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10,9 9,9 8,9"></polyline>
                    </svg>
                    数据管理
                </button>
            </div>

            <div class="modal-body">
                <!-- 文字（聊天）API设置 -->
                <div class="settings-content" id="textSettings">
                    <div class="setting-group">
                        <label for="apiKey">OpenRouter API Key</label>
                        <input type="password" id="apiKey" placeholder="输入你的OpenRouter API Key" />
                        <small class="help-text">用于聊天对话和文本生成，从 <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a> 获取你的API密钥</small>
                    </div>
                    <div class="setting-group">
                        <label for="modelSelect">选择模型</label>
                        <select id="modelSelect">
                            <option value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</option>
                            <option value="google/gemini-2.5-flash-preview-09-2025">Gemini 2.5 Flash Preview</option>
                            <option value="openai/gpt-4-turbo">GPT-4 Turbo</option>
                            <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="google/gemini-pro">Gemini Pro</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <div class="api-status" id="apiStatus">
                            <span class="status-indicator"></span>
                            <span class="status-text">未连接</span>
                        </div>
                    </div>
                </div>

                <!-- 图像分析API设置 -->
                <div class="settings-content" id="imageSettings" style="display: none;">
                    <div class="setting-group">
                        <label for="imageApiKey">OpenRouter API Key</label>
                        <input type="password" id="imageApiKey" placeholder="输入你的OpenRouter API Key" />
                        <small class="help-text">用于图片分析的API密钥，可与文字模型使用相同密钥</small>
                    </div>
                    <div class="setting-group">
                        <label for="imageModelSelect">选择视觉模型</label>
                        <select id="imageModelSelect">
                            <option value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</option>
                            <option value="openai/gpt-4o">GPT-4o</option>
                            <option value="google/gemini-2.5-flash-image-preview">Gemini 2.5 Flash Image Preview</option>
                            <option value="google/gemini-2.5-flash-preview-09-2025">Gemini 2.5 Flash Preview</option>
                            <option value="qwen/qwen-vl-plus">Qwen VL Plus</option>
                            <option value="openai/gpt-4-turbo">GPT-4 Turbo</option>
                        </select>
                        <small class="help-text">支持图像分析的模型</small>
                    </div>
                    <div class="setting-group">
                        <div class="api-status" id="imageApiStatus">
                            <span class="status-indicator"></span>
                            <span class="status-text">未连接</span>
                        </div>
                    </div>
                </div>

                <!-- 图片索引设置 -->
                <div class="settings-content" id="indexSettings" style="display: none;">
                    <div class="index-options">
                        <h4>选择索引范围</h4>
                        <div class="option-group">
                            <label class="option-item">
                                <input type="radio" name="indexType" value="all" checked>
                                <div class="option-content">
                                    <div class="option-title">索引全部图片</div>
                                    <div class="option-desc">扫描并索引目录中的所有图片文件</div>
                                </div>
                            </label>
                            <label class="option-item">
                                <input type="radio" name="indexType" value="untagged">
                                <div class="option-content">
                                    <div class="option-title">仅索引未打标图片</div>
                                    <div class="option-desc">只处理还没有标签和描述的图片</div>
                                </div>
                            </label>
                            <label class="option-item">
                                <input type="radio" name="indexType" value="rebuild">
                                <div class="option-content">
                                    <div class="option-title">重建索引（已打标也重跑）</div>
                                    <div class="option-desc">重新分析已打标图片并更新标签与描述</div>
                                </div>
                            </label>
                        </div>

                        <!-- 强制重建开关 -->
                        <div class="option-group" style="margin-top: 12px;">
                            <label class="option-item" style="align-items: center;">
                                <input type="checkbox" id="forceRebuildToggle">
                                <div class="option-content">
                                    <div class="option-title">强制重建（忽略缓存）</div>
                                    <div class="option-desc">对所有图片重新分析，覆盖已有描述与标签</div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="index-progress" id="indexProgress" style="display: none;">
                        <h4>索引进度</h4>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-info">
                            <span id="progressText">准备开始...</span>
                            <span id="progressCount">0 / 0</span>
                        </div>
                        <div class="progress-details" id="progressDetails"></div>
                    </div>
                </div>

                <!-- 数据管理设置 -->
                <div class="settings-content" id="dataSettings" style="display: none;">
                    <div class="setting-group">
                        <h4>📊 数据统计</h4>
                        <div class="data-stats">
                            <div class="stat-item">
                                <span class="stat-label">总图片数：</span>
                                <span class="stat-value" id="statTotalImages">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">已索引：</span>
                                <span class="stat-value" id="statIndexedImages">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-group">
                        <h4>💾 数据存储</h4>
                        <div class="storage-option">
                            <label>
                                <input type="checkbox" id="useFileStorageToggle" checked>
                                使用文件存储（推荐）
                            </label>
                            <small class="help-text">数据将保存为可编辑的CSV文件</small>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h4>📤 导出数据</h4>
                        <button class="btn btn-primary" id="exportJsonBtn" style="width: 100%; margin: 5px 0;">导出JSON文件</button>
                        <button class="btn btn-primary" id="exportCsvBtn" style="width: 100%; margin: 5px 0;">导出CSV表格</button>
                    </div>

                    <div class="setting-group">
                        <h4>📥 导入数据</h4>
                        <button class="btn btn-secondary" id="importJsonBtn" style="width: 100%; margin: 5px 0;">从JSON导入</button>
                        <button class="btn btn-secondary" id="importCsvBtn" style="width: 100%; margin: 5px 0;">从CSV导入</button>
                    </div>

                    <div class="setting-group">
                        <h4>⚙️ 高级操作</h4>
                        <button class="btn btn-secondary" id="openDataManagerBtn" style="width: 100%; margin: 5px 0;">打开数据管理器</button>
                        <button class="btn btn-secondary" id="migrateDataBtn" style="width: 100%; margin: 5px 0;">从long Storage迁移</button>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <div class="footer-api" id="footerApi">
                    <button class="btn btn-secondary" id="testConnection">测试连接</button>
                    <button class="btn btn-primary" id="saveSettings">保存设置</button>
                </div>
                <div class="footer-index" id="footerIndex" style="display: none;">
                    <button class="btn btn-secondary" id="settingsCancelIndexBtn">取消</button>
                    <button class="btn btn-primary" id="settingsStartIndexBtn">开始索引</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Core dependencies first -->
    <script src="startup-diagnostics.js"></script>
    <script src="image-error-handler.js"></script>
    <script src="openrouter-api.js"></script>
    <script src="image-classifier.js"></script>
    <script src="images-metadata.js"></script>
    <script src="file-storage-manager.js"></script>
    <script src="image-indexer.js"></script>
    <script src="semantic-search.js"></script>
    <script src="ai-search-engine.js"></script>
    <script src="search-interface.js"></script>

    <!-- AI search modules - load conditionally -->
    <script>
    // Load additional AI modules only if core modules are ready
    window.addEventListener('DOMContentLoaded', () => {
        // Check if core modules loaded successfully
        if (window.imagesMetadata && window.imageClassifier) {
            // Load additional AI search modules (excluding already loaded ones)
            const additionalModules = [
                'prompt-templates.js'
                // ai-search-engine.js and search-interface.js are already loaded above
            ];

            additionalModules.forEach(module => {
                const script = document.createElement('script');
                script.src = module;
                script.onerror = () => console.warn(`AI模块 ${module} 加载失败，使用基础搜索`);
                document.head.appendChild(script);
            });
        } else {
            console.warn('核心模块加载失败，禁用AI搜索功能');
        }
    });
    </script>

    <!-- Main script (must be last) -->
    <script src="script.js"></script>
</body>
</html>
