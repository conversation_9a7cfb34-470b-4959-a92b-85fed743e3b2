// 轻量级 Markdown 解析器
class MarkdownParser {
    constructor() {
        this.rules = [
            // 标题
            { pattern: /^(#{1,6})\s+(.+)$/gm, replace: (match, hashes, content) => `<h${hashes.length}>${content.trim()}</h${hashes.length}>` },

            // 粗体
            { pattern: /\*\*(.*?)\*\*/g, replace: '<strong>$1</strong>' },
            { pattern: /__(.*?)__/g, replace: '<strong>$1</strong>' },

            // 斜体
            { pattern: /\*(.*?)\*/g, replace: '<em>$1</em>' },
            { pattern: /_(.*?)_/g, replace: '<em>$1</em>' },

            // 行内代码
            { pattern: /`([^`]+)`/g, replace: '<code>$1</code>' },

            // 链接
            { pattern: /\[([^\]]+)\]\(([^)]+)\)/g, replace: '<a href="$2" target="_blank">$1</a>' },

            // 无序列表
            { pattern: /^\s*[-*+]\s+(.+)$/gm, replace: '<li>$1</li>' },

            // 有序列表
            { pattern: /^\s*\d+\.\s+(.+)$/gm, replace: '<li>$1</li>' },

            // 引用块
            { pattern: /^>\s+(.+)$/gm, replace: '<blockquote>$1</blockquote>' },

            // 水平线
            { pattern: /^(?:\*{3,}|-{3,}|_{3,})$/gm, replace: '<hr>' },

            // 代码块
            { pattern: /```(\w*)\n([\s\S]*?)```/g, replace: '<pre><code class="language-$1">$2</code></pre>' },

            // 表格（简化版）
            { pattern: /^\|(.+)\|$/gm, replace: (match, content) => {
                const cells = content.split('|').map(cell => cell.trim()).filter(cell => cell);
                return '<tr>' + cells.map(cell => `<td>${cell}</td>`).join('') + '</tr>';
            }}
        ];
    }

    parse(markdown) {
        if (!markdown) return '';

        let html = markdown;

        // 应用所有规则
        this.rules.forEach(rule => {
            if (typeof rule.replace === 'function') {
                html = html.replace(rule.pattern, rule.replace);
            } else {
                html = html.replace(rule.pattern, rule.replace);
            }
        });

        // 处理段落
        html = this.processParagraphs(html);

        // 处理列表
        html = this.processLists(html);

        // 处理表格
        html = this.processTables(html);

        // 清理多余空行
        html = html.replace(/\n\s*\n\s*\n/g, '\n\n');

        return html;
    }

    processParagraphs(html) {
        // 分割成块
        const blocks = html.split(/\n\s*\n/);

        return blocks.map(block => {
            block = block.trim();
            if (!block) return '';

            // 跳过已经是HTML标签的块
            if (block.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|table|tr|hr)/)) {
                return block;
            }

            // 包装成段落
            if (!block.startsWith('<') && block.length > 0) {
                return `<p>${block}</p>`;
            }

            return block;
        }).filter(block => block).join('\n\n');
    }

    processLists(html) {
        // 处理无序列表
        html = html.replace(/(<li>.*?<\/li>)(\s*<li>.*?<\/li>)*/gs, (match) => {
            return `<ul>${match}</ul>`;
        });

        return html;
    }

    processTables(html) {
        // 将连续的<tr>包装成表格
        html = html.replace(/(<tr>.*?<\/tr>)(\s*<tr>.*?<\/tr>)*/gs, (match) => {
            // 检查第一行是否是表头
            const rows = match.match(/<tr>.*?<\/tr>/g);
            if (rows && rows.length > 0) {
                const firstRow = rows[0].replace(/<td>/g, '<th>').replace(/<\/td>/g, '</th>');
                const otherRows = rows.slice(1).join('');
                return `<table><thead>${firstRow}</thead><tbody>${otherRows}</tbody></table>`;
            }
            return `<table>${match}</table>`;
        });

        return html;
    }

    // 流式渲染支持 - 增量解析
    parseIncremental(currentHtml, newChunk) {
        const combined = currentHtml + newChunk;
        return this.parse(combined);
    }

    // 保留引用标记的解析
    parseWithCitations(markdown) {
        let html = this.parse(markdown);

        // 查找并保留引用标记 [1], [2] 等
        html = html.replace(/\[(\d+)\]/g, '<span class="citation" data-ref="$1">[$1]</span>');

        return html;
    }

    // 安全的HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 移除HTML标签，获取纯文本
    stripHtml(html) {
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    }
}

// 全局实例
window.markdownParser = new MarkdownParser();