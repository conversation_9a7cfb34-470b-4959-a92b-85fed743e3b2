// PDF 查看器组件 - NotebookLM 风格
class PDFViewer {
    constructor(container) {
        this.container = container;
        this.pdf = null;
        this.scale = 1.0;
        this.currentPage = 1;
        this.totalPages = 0;
        this.renderedPages = new Set();
        this.pageElements = new Map();
        this.highlightElements = new Set();

        // 配置参数
        this.config = {
            defaultScale: 1.0,
            maxScale: 3.0,
            minScale: 0.5,
            pageGap: 20, // 页面间距
            enableLazyLoading: true,
            renderDistance: 2, // 预渲染距离（页面数）
        };

        this.init();
    }

    init() {
        this.createViewerStructure();
        this.bindEvents();
    }

    createViewerStructure() {
        this.container.innerHTML = `
            <div class="pdf-viewer">
                <div class="pdf-toolbar">
                    <div class="pdf-controls">
                        <button class="pdf-btn" id="pdfPrevBtn" title="上一页">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,18 9,12 15,6"></polyline>
                            </svg>
                        </button>

                        <div class="pdf-page-info">
                            <span id="pdfCurrentPage">1</span> / <span id="pdfTotalPages">1</span>
                        </div>

                        <button class="pdf-btn" id="pdfNextBtn" title="下一页">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"></polyline>
                            </svg>
                        </button>
                    </div>

                    <div class="pdf-zoom-controls">
                        <button class="pdf-btn" id="pdfZoomOutBtn" title="缩小">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="7" y1="11" x2="15" y2="11"></line>
                            </svg>
                        </button>

                        <span class="pdf-zoom-level" id="pdfZoomLevel">100%</span>

                        <button class="pdf-btn" id="pdfZoomInBtn" title="放大">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <line x1="7" y1="11" x2="15" y2="11"></line>
                                <line x1="11" y1="7" x2="11" y2="15"></line>
                            </svg>
                        </button>

                        <button class="pdf-btn" id="pdfFitWidthBtn" title="适合宽度">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="7" y1="7" x2="17" y2="7"></line>
                                <line x1="7" y1="11" x2="17" y2="11"></line>
                                <line x1="7" y1="15" x2="17" y2="15"></line>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="pdf-container" id="pdfContainer">
                    <div class="pdf-loading">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">正在加载PDF...</div>
                    </div>
                </div>
            </div>
        `;

        this.pdfContainer = this.container.querySelector('#pdfContainer');
        this.updateUI();
    }

    bindEvents() {
        // 工具栏按钮事件
        this.container.querySelector('#pdfPrevBtn').addEventListener('click', () => this.previousPage());
        this.container.querySelector('#pdfNextBtn').addEventListener('click', () => this.nextPage());
        this.container.querySelector('#pdfZoomInBtn').addEventListener('click', () => this.zoomIn());
        this.container.querySelector('#pdfZoomOutBtn').addEventListener('click', () => this.zoomOut());
        this.container.querySelector('#pdfFitWidthBtn').addEventListener('click', () => this.fitWidth());

        // 滚动事件
        this.pdfContainer.addEventListener('scroll', () => {
            this.handleScroll();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (this.container.contains(e.target) || e.target.closest('.pdf-viewer')) {
                this.handleKeydown(e);
            }
        });
    }

    async loadPDF(arrayBuffer) {
        try {
            this.showLoading();

            if (typeof pdfjsLib === 'undefined') {
                throw new Error('PDF.js 未加载');
            }

            const loadingTask = pdfjsLib.getDocument(arrayBuffer);
            this.pdf = await loadingTask.promise;
            this.totalPages = this.pdf.numPages;

            this.setupPages();
            this.updateUI();
            this.hideLoading();

            // 渲染初始页面
            if (this.config.enableLazyLoading) {
                this.renderVisiblePages();
            } else {
                this.renderAllPages();
            }

        } catch (error) {
            console.error('PDF 加载失败:', error);
            this.showError('PDF 加载失败: ' + error.message);
        }
    }

    setupPages() {
        this.pdfContainer.innerHTML = '';
        this.renderedPages.clear();
        this.pageElements.clear();

        for (let pageNum = 1; pageNum <= this.totalPages; pageNum++) {
            const pageContainer = document.createElement('div');
            pageContainer.className = 'pdf-page-container';
            pageContainer.dataset.pageNumber = pageNum;

            pageContainer.innerHTML = `
                <div class="pdf-page-wrapper">
                    <canvas class="pdf-page-canvas" id="pdfPage${pageNum}"></canvas>
                    <div class="pdf-page-overlay" id="pdfPageOverlay${pageNum}"></div>
                    <div class="pdf-page-number">${pageNum}</div>
                </div>
            `;

            this.pdfContainer.appendChild(pageContainer);
            this.pageElements.set(pageNum, pageContainer);
        }
    }

    async renderPage(pageNumber) {
        if (this.renderedPages.has(pageNumber)) return;

        try {
            const page = await this.pdf.getPage(pageNumber);
            const canvas = document.getElementById(`pdfPage${pageNumber}`);
            const context = canvas.getContext('2d');

            // 计算缩放比例和尺寸
            const viewport = page.getViewport({ scale: this.scale });
            const outputScale = window.devicePixelRatio || 1;

            canvas.width = Math.floor(viewport.width * outputScale);
            canvas.height = Math.floor(viewport.height * outputScale);
            canvas.style.width = Math.floor(viewport.width) + 'px';
            canvas.style.height = Math.floor(viewport.height) + 'px';

            const transform = outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : null;

            // 渲染页面
            const renderContext = {
                canvasContext: context,
                viewport: viewport,
                transform: transform
            };

            await page.render(renderContext).promise;
            this.renderedPages.add(pageNumber);

            // 渲染文本层（用于文本选择和搜索）
            this.renderTextLayer(page, pageNumber, viewport);

        } catch (error) {
            console.error(`页面 ${pageNumber} 渲染失败:`, error);
        }
    }

    async renderTextLayer(page, pageNumber, viewport) {
        try {
            const textContent = await page.getTextContent();
            const overlay = document.getElementById(`pdfPageOverlay${pageNumber}`);

            // 清空现有文本层
            overlay.innerHTML = '';

            // 创建文本层
            textContent.items.forEach((item, index) => {
                const textElement = document.createElement('span');
                textElement.className = 'pdf-text-item';
                textElement.textContent = item.str;
                textElement.dataset.itemIndex = index;

                // 计算位置
                const transform = item.transform;
                const x = transform[4];
                const y = transform[5];
                const width = item.width || 0;
                const height = item.height || 0;

                textElement.style.position = 'absolute';
                textElement.style.left = x + 'px';
                textElement.style.bottom = (viewport.height - y) + 'px';
                textElement.style.width = width + 'px';
                textElement.style.height = height + 'px';
                textElement.style.fontSize = (height * 0.8) + 'px';
                textElement.style.opacity = '0'; // 隐藏，只用于位置定位

                overlay.appendChild(textElement);
            });

        } catch (error) {
            console.warn(`页面 ${pageNumber} 文本层渲染失败:`, error);
        }
    }

    renderVisiblePages() {
        const containerRect = this.pdfContainer.getBoundingClientRect();
        const containerTop = this.pdfContainer.scrollTop;
        const containerBottom = containerTop + containerRect.height;

        this.pageElements.forEach((pageElement, pageNumber) => {
            const pageTop = pageElement.offsetTop;
            const pageBottom = pageTop + pageElement.offsetHeight;

            // 检查页面是否在可见区域内或接近可见区域
            const isVisible = pageBottom >= containerTop - 1000 && pageTop <= containerBottom + 1000;

            if (isVisible && !this.renderedPages.has(pageNumber)) {
                this.renderPage(pageNumber);
            }
        });
    }

    renderAllPages() {
        for (let i = 1; i <= this.totalPages; i++) {
            this.renderPage(i);
        }
    }

    // 高亮功能
    highlightText(pageNumber, textQuery, options = {}) {
        this.clearHighlights();

        const overlay = document.getElementById(`pdfPageOverlay${pageNumber}`);
        if (!overlay) return;

        const textItems = overlay.querySelectorAll('.pdf-text-item');
        const highlightClass = options.highlightClass || 'pdf-text-highlight';
        const color = options.color || '#fff3cd';

        textItems.forEach(textItem => {
            const text = textItem.textContent.toLowerCase();
            const query = textQuery.toLowerCase();

            if (text.includes(query)) {
                const highlight = document.createElement('div');
                highlight.className = highlightClass;
                highlight.style.position = 'absolute';
                highlight.style.left = textItem.style.left;
                highlight.style.bottom = textItem.style.bottom;
                highlight.style.width = textItem.style.width;
                highlight.style.height = textItem.style.height;
                highlight.style.backgroundColor = color;
                highlight.style.opacity = '0.6';
                highlight.style.pointerEvents = 'none';

                overlay.appendChild(highlight);
                this.highlightElements.add(highlight);

                // 添加动画效果
                if (options.animate !== false) {
                    highlight.style.animation = 'highlight-flash 2s ease-out';
                }
            }
        });
    }

    clearHighlights() {
        this.highlightElements.forEach(element => {
            element.remove();
        });
        this.highlightElements.clear();
    }

    // 导航功能
    scrollToPage(pageNumber) {
        const pageElement = this.pageElements.get(pageNumber);
        if (pageElement) {
            pageElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            this.currentPage = pageNumber;
            this.updateUI();
        }
    }

    previousPage() {
        if (this.currentPage > 1) {
            this.scrollToPage(this.currentPage - 1);
        }
    }

    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.scrollToPage(this.currentPage + 1);
        }
    }

    // 缩放功能
    zoomIn() {
        this.setScale(Math.min(this.scale * 1.2, this.config.maxScale));
    }

    zoomOut() {
        this.setScale(Math.max(this.scale / 1.2, this.config.minScale));
    }

    fitWidth() {
        if (this.pageElements.size === 0) return;

        const containerWidth = this.pdfContainer.clientWidth - 40; // 减去边距
        const firstPage = this.pageElements.get(1);
        if (firstPage) {
            const canvas = firstPage.querySelector('canvas');
            if (canvas) {
                const naturalWidth = canvas.width / (window.devicePixelRatio || 1);
                const scale = containerWidth / naturalWidth;
                this.setScale(scale);
            }
        }
    }

    setScale(scale) {
        const oldScale = this.scale;
        this.scale = Math.max(this.config.minScale, Math.min(scale, this.config.maxScale));

        if (oldScale !== this.scale) {
            this.renderedPages.clear(); // 重新渲染所有页面
            this.renderVisiblePages();
            this.updateUI();
        }
    }

    // 事件处理
    handleScroll() {
        if (this.config.enableLazyLoading) {
            this.renderVisiblePages();
        }

        // 更新当前页码
        this.updateCurrentPage();
    }

    updateCurrentPage() {
        const containerTop = this.pdfContainer.scrollTop;
        const containerCenter = containerTop + this.pdfContainer.clientHeight / 2;

        this.pageElements.forEach((pageElement, pageNumber) => {
            const pageTop = pageElement.offsetTop;
            const pageBottom = pageTop + pageElement.offsetHeight;

            if (containerCenter >= pageTop && containerCenter <= pageBottom) {
                this.currentPage = pageNumber;
            }
        });

        this.updateUI();
    }

    handleKeydown(e) {
        switch (e.key) {
            case 'ArrowUp':
                e.preventDefault();
                this.pdfContainer.scrollTop -= 50;
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.pdfContainer.scrollTop += 50;
                break;
            case 'PageUp':
                e.preventDefault();
                this.previousPage();
                break;
            case 'PageDown':
                e.preventDefault();
                this.nextPage();
                break;
            case '+':
            case '=':
                e.preventDefault();
                this.zoomIn();
                break;
            case '-':
                e.preventDefault();
                this.zoomOut();
                break;
        }
    }

    // UI 更新
    updateUI() {
        this.container.querySelector('#pdfCurrentPage').textContent = this.currentPage;
        this.container.querySelector('#pdfTotalPages').textContent = this.totalPages;
        this.container.querySelector('#pdfZoomLevel').textContent = Math.round(this.scale * 100) + '%';

        // 更新按钮状态
        this.container.querySelector('#pdfPrevBtn').disabled = this.currentPage <= 1;
        this.container.querySelector('#pdfNextBtn').disabled = this.currentPage >= this.totalPages;
    }

    showLoading() {
        const loading = this.container.querySelector('.pdf-loading');
        if (loading) loading.style.display = 'flex';
    }

    hideLoading() {
        const loading = this.container.querySelector('.pdf-loading');
        if (loading) loading.style.display = 'none';
    }

    showError(message) {
        this.pdfContainer.innerHTML = `
            <div class="pdf-error">
                <div class="error-icon">⚠️</div>
                <div class="error-message">${message}</div>
            </div>
        `;
    }

    // 清理资源
    destroy() {
        this.clearHighlights();
        this.renderedPages.clear();
        this.pageElements.clear();

        if (this.pdf) {
            this.pdf.destroy();
            this.pdf = null;
        }
    }
}

// 全局实例
window.PDFViewer = PDFViewer;