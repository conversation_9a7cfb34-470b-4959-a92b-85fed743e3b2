#!/bin/bash

# Mac应用程序启动器
# 该文件将在用户双击.app文件时执行

# 设置项目目录（使用绝对路径）
PROJECT_DIR="/Users/<USER>/Documents/体验网站项目"
SCRIPT_PATH="$PROJECT_DIR/start_server.sh"

# 检查项目目录是否存在
if [ ! -d "$PROJECT_DIR" ]; then
    osascript -e 'display dialog "错误：找不到项目目录\n'"$PROJECT_DIR"'" buttons {"确定"} default button "确定" with icon stop'
    exit 1
fi

# 检查启动脚本是否存在
if [ ! -f "$SCRIPT_PATH" ]; then
    osascript -e 'display dialog "错误：找不到启动脚本\n'"$SCRIPT_PATH"'" buttons {"确定"} default button "确定" with icon stop'
    exit 1
fi

# 在新的终端窗口中运行启动脚本
osascript << EOF
tell application "Terminal"
    activate
    do script "cd '$PROJECT_DIR' && bash '$SCRIPT_PATH'"
end tell
EOF