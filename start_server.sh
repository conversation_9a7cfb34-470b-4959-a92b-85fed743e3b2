#!/bin/bash

# 体验网站启动脚本
# 该脚本将启动本地HTTP服务器并自动打开首页

PROJECT_DIR="/Users/<USER>/Documents/体验网站项目"
PORT=8000
URL="http://localhost:${PORT}"

echo "========================================"
echo "           体验网站启动器"
echo "========================================"
echo "项目目录: $PROJECT_DIR"
echo "端口: $PORT (标准端口，保持数据一致性)"
echo "访问地址: $URL"
echo "========================================"

# 切换到项目目录
cd "$PROJECT_DIR"

# 检查端口是否被占用
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "端口 $PORT 已被占用，尝试终止占用进程..."
    lsof -ti:$PORT | xargs kill -9 2>/dev/null
    sleep 2
fi

# 启动HTTP服务器（使用Python内置服务器）
if command -v python3 &> /dev/null; then
    echo "使用 Python3 启动服务器..."
    python3 -m http.server $PORT &
elif command -v python &> /dev/null; then
    echo "使用 Python2 启动服务器..."
    python -m SimpleHTTPServer $PORT &
else
    echo "错误: 未找到 Python，无法启动服务器"
    echo "请安装 Python 或使用其他方式启动HTTP服务器"
    exit 1
fi

SERVER_PID=$!
echo "服务器已启动，进程ID: $SERVER_PID"

# 等待服务器启动
sleep 3

# 检查服务器是否成功启动
if curl -s --connect-timeout 3 "$URL" > /dev/null; then
    echo "服务器启动成功！"
    echo "正在打开浏览器..."
    
    # 打开默认浏览器
    open "$URL"
    
    echo "========================================"
    echo "           启动成功！"
    echo "========================================"
    echo "🌐 体验网站已成功启动并在浏览器中打开！"
    echo "🔗 访问地址: $URL"
    echo "💾 您的数据（图片索引、API配置）已保持"
    echo "⚠️  按 Ctrl+C 可以安全停止服务器"
    echo "========================================"
    
    # 等待用户按Ctrl+C
    trap "echo '正在关闭服务器...'; kill $SERVER_PID 2>/dev/null; exit" INT
    wait $SERVER_PID
else
    echo "错误: 服务器启动失败"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi