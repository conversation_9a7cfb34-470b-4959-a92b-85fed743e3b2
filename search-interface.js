/**
 * 搜索界面管理器
 * 提供增强的搜索界面和交互体验，集成AI搜索功能
 */
(function(global) {
    // 防止重复声明
    if (global.SearchInterface) {
        console.warn('SearchInterface already defined, skipping redefinition');
        return;
    }

class SearchInterface {
    constructor() {
        this.isInitialized = false;
        this.currentQuery = '';
        this.searchResults = [];
        this.searchHistory = this.loadSearchHistory();
        this.suggestions = [];
        this.isSearching = false;
        this.searchMode = 'auto'; // auto, traditional, ai_semantic, hybrid

        // UI元素引用
        this.elements = {};

        // 配置选项
        this.config = {
            debounceDelay: 300,
            maxSuggestions: 8,
            maxHistoryItems: 10,
            enableRealTimeSearch: true,
            enableSearchSuggestions: true,
            showSearchStatistics: true
        };

        // 事件监听器
        this.eventListeners = [];
    }

    /**
     * 初始化搜索界面
     * @param {string|HTMLElement} containerSelector - 容器选择器或元素
     */
    async initialize(containerSelector) {
        if (this.isInitialized) return;

        try {
            // 获取容器元素
            this.container = typeof containerSelector === 'string'
                ? document.querySelector(containerSelector)
                : containerSelector;

            if (!this.container) {
                throw new Error('搜索界面容器未找到');
            }

            // 检查依赖
            await this.checkDependencies();

            // 创建界面
            this.createSearchInterface();

            // 绑定事件
            this.bindEvents();

            // 加载用户偏好
            this.loadUserPreferences();

            // 初始化AI搜索引擎
            if (window.aiSearchEngine) {
                try {
                    await window.aiSearchEngine.initialize();
                } catch (error) {
                    console.warn('AI搜索引擎初始化失败:', error);
                    // 禁用AI模式按钮
                    if (this.elements.aiModeBtn) {
                        this.elements.aiModeBtn.disabled = true;
                        this.elements.aiModeBtn.title = 'AI搜索引擎初始化失败';
                    }
                }
            }

            this.isInitialized = true;
            console.log('搜索界面初始化完成');

        } catch (error) {
            console.error('搜索界面初始化失败:', error);
            throw error;
        }
    }

    /**
     * 检查依赖项
     */
    async checkDependencies() {
        const requiredDependencies = [
            'window.semanticSearchEngine',
            'window.imagesMetadata'
        ];

        for (const dep of requiredDependencies) {
            if (!this.getNestedProperty(window, dep)) {
                throw new Error(`缺少必需依赖: ${dep}`);
            }
        }
    }

    /**
     * 创建搜索界面HTML结构
     */
    createSearchInterface() {
        const interfaceHTML = `
            <div class="search-interface">
                <!-- 搜索模式切换 -->
                <div class="search-mode-selector">
                    <button class="mode-btn active" data-mode="auto">
                        <span class="mode-icon">🔍</span>
                        <span class="mode-text">智能搜索</span>
                    </button>
                    <button class="mode-btn" data-mode="traditional">
                        <span class="mode-icon">📝</span>
                        <span class="mode-text">关键词</span>
                    </button>
                    <button class="mode-btn" data-mode="ai_semantic" id="ai-mode-btn" disabled>
                        <span class="mode-icon">🤖</span>
                        <span class="mode-text">AI语义</span>
                    </button>
                </div>

                <!-- 主搜索区域 -->
                <div class="search-main">
                    <div class="search-input-container">
                        <input type="text"
                               class="search-input"
                               placeholder="搜索界面设计案例..."
                               autocomplete="off"
                               id="main-search-input">
                        <button class="search-clear-btn" style="display: none;">×</button>
                        <button class="search-submit-btn">
                            <span class="search-icon">🔍</span>
                        </button>
                    </div>

                    <!-- 搜索建议下拉 -->
                    <div class="search-suggestions" style="display: none;">
                        <div class="suggestions-content">
                            <!-- 动态生成建议内容 -->
                        </div>
                    </div>
                </div>

                <!-- 高级搜索面板 -->
                <div class="advanced-search-panel" style="display: none;">
                    <div class="panel-header">
                        <h3>高级搜索</h3>
                        <button class="panel-close">×</button>
                    </div>
                    <div class="panel-content">
                        <div class="filter-group">
                            <label>应用类型</label>
                            <select class="filter-select" data-filter="app">
                                <option value="">全部</option>
                                <!-- 动态生成选项 -->
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>页面类型</label>
                            <select class="filter-select" data-filter="page">
                                <option value="">全部</option>
                                <!-- 动态生成选项 -->
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>颜色主题</label>
                            <select class="filter-select" data-filter="color">
                                <option value="">全部</option>
                                <option value="深色主题">深色主题</option>
                                <option value="浅色主题">浅色主题</option>
                                <option value="蓝色">蓝色</option>
                                <option value="绿色">绿色</option>
                                <option value="红色">红色</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 搜索结果区域 -->
                <div class="search-results-area">
                    <!-- 搜索状态 -->
                    <div class="search-status" style="display: none;">
                        <div class="status-content">
                            <span class="status-icon">🔍</span>
                            <span class="status-text">搜索中...</span>
                        </div>
                    </div>

                    <!-- 搜索摘要 -->
                    <div class="search-summary" style="display: none;">
                        <div class="summary-content">
                            <!-- 动态生成摘要内容 -->
                        </div>
                    </div>

                    <!-- 搜索结果 -->
                    <div class="search-results">
                        <!-- 动态生成搜索结果 -->
                    </div>

                    <!-- 搜索建议 -->
                    <div class="search-recommendations" style="display: none;">
                        <h4>相关搜索建议</h4>
                        <div class="recommendations-content">
                            <!-- 动态生成推荐内容 -->
                        </div>
                    </div>
                </div>

                <!-- 搜索历史侧边栏 -->
                <div class="search-history-sidebar" style="display: none;">
                    <div class="sidebar-header">
                        <h3>搜索历史</h3>
                        <button class="history-clear">清空</button>
                    </div>
                    <div class="history-content">
                        <!-- 动态生成历史记录 -->
                    </div>
                </div>
            </div>
        `;

        this.container.innerHTML = interfaceHTML;
        this.cacheElements();
    }

    /**
     * 缓存DOM元素引用
     */
    cacheElements() {
        this.elements = {
            searchInput: this.container.querySelector('.search-input'),
            searchSubmit: this.container.querySelector('.search-submit-btn'),
            searchClear: this.container.querySelector('.search-clear-btn'),
            suggestions: this.container.querySelector('.search-suggestions'),
            suggestionsContent: this.container.querySelector('.suggestions-content'),
            modeBtns: this.container.querySelectorAll('.mode-btn'),
            aiModeBtn: this.container.querySelector('#ai-mode-btn'),
            advancedPanel: this.container.querySelector('.advanced-search-panel'),
            searchStatus: this.container.querySelector('.search-status'),
            searchSummary: this.container.querySelector('.search-summary'),
            searchResults: this.container.querySelector('.search-results'),
            recommendations: this.container.querySelector('.search-recommendations'),
            historySidebar: this.container.querySelector('.search-history-sidebar'),
            historyContent: this.container.querySelector('.history-content'),
            historyClear: this.container.querySelector('.history-clear')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 搜索输入事件
        this.addEventListenter(this.elements.searchInput, 'input',
            this.debounce(this.handleSearchInput.bind(this), this.config.debounceDelay)
        );

        this.addEventListenter(this.elements.searchInput, 'focus', this.handleSearchFocus.bind(this));
        this.addEventListenter(this.elements.searchInput, 'blur', this.handleSearchBlur.bind(this));
        this.addEventListenter(this.elements.searchInput, 'keydown', this.handleSearchKeydown.bind(this));

        // 搜索提交
        this.addEventListenter(this.elements.searchSubmit, 'click', this.handleSearchSubmit.bind(this));

        // 清空搜索
        this.addEventListenter(this.elements.searchClear, 'click', this.handleSearchClear.bind(this));

        // 搜索模式切换
        this.elements.modeBtns.forEach(btn => {
            this.addEventListenter(btn, 'click', this.handleModeChange.bind(this));
        });

        // 搜索建议点击
        this.addEventListenter(this.elements.suggestions, 'click', this.handleSuggestionClick.bind(this));

        // 历史记录清空
        this.addEventListenter(this.elements.historyClear, 'click', this.handleHistoryClear.bind(this));

        // 全局点击事件（关闭下拉菜单）
        this.addEventListenter(document, 'click', this.handleDocumentClick.bind(this));

        // 键盘快捷键
        this.addEventListenter(document, 'keydown', this.handleGlobalKeydown.bind(this));

        // 检查AI可用性
        this.checkAIAvailability();
    }

    /**
     * 添加事件监听器并记录
     */
    addEventListenter(element, event, handler) {
        element.addEventListener(event, handler);
        this.eventListeners.push({ element, event, handler });
    }

    /**
     * 处理搜索输入
     */
    async handleSearchInput(event) {
        const query = event.target.value.trim();
        this.currentQuery = query;

        // 显示/隐藏清空按钮
        this.elements.searchClear.style.display = query ? 'block' : 'none';

        if (query.length === 0) {
            this.hideSuggestions();
            this.clearResults();
            return;
        }

        // 实时搜索建议
        if (this.config.enableSearchSuggestions) {
            await this.showSearchSuggestions(query);
        }

        // 实时搜索结果
        if (this.config.enableRealTimeSearch && query.length >= 2) {
            await this.performSearch(query, { isRealTime: true });
        }
    }

    /**
     * 处理搜索焦点
     */
    handleSearchFocus(event) {
        if (this.currentQuery) {
            this.showSearchSuggestions(this.currentQuery);
        } else {
            this.showSearchHistory();
        }
    }

    /**
     * 处理搜索失焦
     */
    handleSearchBlur(event) {
        // 延迟隐藏建议，以便处理点击事件
        setTimeout(() => {
            if (!this.container.contains(document.activeElement)) {
                this.hideSuggestions();
            }
        }, 150);
    }

    /**
     * 处理搜索键盘事件
     */
    handleSearchKeydown(event) {
        switch (event.key) {
            case 'Enter':
                event.preventDefault();
                this.handleSearchSubmit();
                break;
            case 'Escape':
                this.hideSuggestions();
                this.elements.searchInput.blur();
                break;
            case 'ArrowDown':
                event.preventDefault();
                this.navigateSuggestions('down');
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.navigateSuggestions('up');
                break;
        }
    }

    /**
     * 处理搜索提交
     */
    async handleSearchSubmit() {
        const query = this.currentQuery.trim();
        if (!query) return;

        this.hideSuggestions();
        await this.performSearch(query, { isSubmit: true });
    }

    /**
     * 处理搜索清空
     */
    handleSearchClear() {
        this.elements.searchInput.value = '';
        this.currentQuery = '';
        this.elements.searchClear.style.display = 'none';
        this.hideSuggestions();
        this.clearResults();
        this.elements.searchInput.focus();
    }

    /**
     * 处理搜索模式切换
     */
    handleModeChange(event) {
        const btn = event.currentTarget;
        const mode = btn.dataset.mode;

        if (btn.disabled) return;

        // 更新按钮状态
        this.elements.modeBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');

        this.searchMode = mode;

        // 保存用户偏好
        this.saveUserPreferences();

        // 如果有当前查询，重新搜索
        if (this.currentQuery) {
            this.performSearch(this.currentQuery);
        }
    }

    /**
     * 处理建议点击
     */
    handleSuggestionClick(event) {
        const suggestion = event.target.closest('[data-suggestion]');
        if (!suggestion) return;

        const text = suggestion.dataset.suggestion;
        this.elements.searchInput.value = text;
        this.currentQuery = text;
        this.hideSuggestions();
        this.performSearch(text, { isSubmit: true });
    }

    /**
     * 处理历史记录清空
     */
    handleHistoryClear() {
        this.searchHistory = [];
        this.saveSearchHistory();
        this.updateHistoryDisplay();
    }

    /**
     * 处理全局点击
     */
    handleDocumentClick(event) {
        if (!this.container.contains(event.target)) {
            this.hideSuggestions();
        }
    }

    /**
     * 处理全局键盘事件
     */
    handleGlobalKeydown(event) {
        // Ctrl/Cmd + K 打开搜索
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            this.elements.searchInput.focus();
        }
    }

    /**
     * 执行搜索
     */
    async performSearch(query, options = {}) {
        if (this.isSearching) return;

        this.isSearching = true;
        this.showSearchStatus('搜索中...');

        try {
            let searchResults;

            // 根据搜索模式选择搜索方法
            switch (this.searchMode) {
                case 'ai_semantic':
                    if (window.aiSearchEngine && window.aiSearchEngine.isInitialized) {
                        searchResults = await window.aiSearchEngine.search(query, { strategy: 'ai_semantic' });
                    } else {
                        searchResults = await window.semanticSearchEngine.semanticSearch(query, this.getImageData());
                        searchResults = { results: searchResults, strategy: 'semantic' };
                    }
                    break;

                case 'hybrid':
                    if (window.aiSearchEngine && window.aiSearchEngine.isInitialized) {
                        searchResults = await window.aiSearchEngine.search(query, { strategy: 'hybrid' });
                    } else {
                        searchResults = await window.semanticSearchEngine.smartSearch(query, this.getImageData());
                        searchResults = { results: searchResults, strategy: 'smart' };
                    }
                    break;

                case 'traditional':
                    searchResults = await window.semanticSearchEngine.search(query, this.getImageData());
                    searchResults = { results: searchResults, strategy: 'traditional' };
                    break;

                case 'auto':
                default:
                    if (window.aiSearchEngine && window.aiSearchEngine.isInitialized) {
                        searchResults = await window.aiSearchEngine.search(query);
                    } else {
                        searchResults = await window.semanticSearchEngine.smartSearch(query, this.getImageData());
                        searchResults = { results: searchResults, strategy: 'auto' };
                    }
                    break;
            }

            this.searchResults = searchResults.results || searchResults;

            // 显示搜索结果
            this.displaySearchResults(searchResults);

            // 显示搜索摘要
            this.displaySearchSummary(searchResults);

            // 显示搜索建议
            this.displaySearchRecommendations(searchResults);

            // 记录搜索历史
            if (options.isSubmit) {
                this.addToSearchHistory(query);
            }

        } catch (error) {
            console.error('搜索失败:', error);
            this.showSearchStatus('搜索失败，请重试');

            // 降级到基础搜索
            if (this.searchMode !== 'traditional') {
                try {
                    const fallbackResults = await window.semanticSearchEngine.search(query, this.getImageData());
                    this.displaySearchResults({ results: fallbackResults, strategy: 'fallback' });
                } catch (fallbackError) {
                    console.error('降级搜索也失败:', fallbackError);
                }
            }

        } finally {
            this.isSearching = false;
            this.hideSearchStatus();
        }
    }

    /**
     * 显示搜索建议
     */
    async showSearchSuggestions(query) {
        if (!this.config.enableSearchSuggestions) return;

        try {
            let suggestions = [];

            // 获取AI建议
            if (window.openrouterAPI && window.openrouterAPI.apiKey) {
                try {
                    const aiSuggestions = await window.openrouterAPI.generateSearchSuggestions(
                        query,
                        this.getRecentQueries()
                    );
                    suggestions.push(...aiSuggestions);
                } catch (error) {
                    console.warn('AI建议生成失败:', error);
                }
            }

            // 获取语义建议
            const semanticSuggestions = window.semanticSearchEngine.getSearchSuggestions(query);
            suggestions.push(...semanticSuggestions);

            // 添加历史记录匹配
            const historyMatches = this.searchHistory
                .filter(item => item.query.toLowerCase().includes(query.toLowerCase()))
                .slice(0, 3)
                .map(item => ({
                    text: item.query,
                    type: 'history',
                    category: 'history'
                }));

            suggestions.push(...historyMatches);

            // 去重并限制数量
            const uniqueSuggestions = this.deduplicateSuggestions(suggestions);
            this.suggestions = uniqueSuggestions.slice(0, this.config.maxSuggestions);

            this.renderSuggestions(this.suggestions);

        } catch (error) {
            console.error('搜索建议生成失败:', error);
        }
    }

    /**
     * 显示搜索历史
     */
    showSearchHistory() {
        if (this.searchHistory.length === 0) return;

        const historyItems = this.searchHistory.slice(0, this.config.maxHistoryItems).map(item => ({
            text: item.query,
            type: 'history',
            category: 'history',
            timestamp: item.timestamp
        }));

        this.renderSuggestions(historyItems);
    }

    /**
     * 渲染搜索建议
     */
    renderSuggestions(suggestions) {
        if (!suggestions || suggestions.length === 0) {
            this.hideSuggestions();
            return;
        }

        const suggestionsHTML = suggestions.map((suggestion, index) => {
            const iconMap = {
                'history': '🕒',
                'completion': '💡',
                'related': '🔗',
                'popular': '🔥',
                'ai': '🤖'
            };

            const icon = iconMap[suggestion.type] || '🔍';

            return `
                <div class="suggestion-item"
                     data-suggestion="${suggestion.text}"
                     data-index="${index}">
                    <span class="suggestion-icon">${icon}</span>
                    <span class="suggestion-text">${suggestion.text}</span>
                    <span class="suggestion-type">${suggestion.type}</span>
                </div>
            `;
        }).join('');

        this.elements.suggestionsContent.innerHTML = suggestionsHTML;
        this.elements.suggestions.style.display = 'block';
    }

    /**
     * 隐藏搜索建议
     */
    hideSuggestions() {
        this.elements.suggestions.style.display = 'none';
    }

    /**
     * 导航搜索建议
     */
    navigateSuggestions(direction) {
        const suggestions = this.elements.suggestionsContent.querySelectorAll('.suggestion-item');
        if (suggestions.length === 0) return;

        const current = this.elements.suggestionsContent.querySelector('.suggestion-item.highlighted');
        let index = current ? parseInt(current.dataset.index) : -1;

        // 移除当前高亮
        if (current) {
            current.classList.remove('highlighted');
        }

        // 计算新索引
        if (direction === 'down') {
            index = (index + 1) % suggestions.length;
        } else {
            index = index <= 0 ? suggestions.length - 1 : index - 1;
        }

        // 高亮新项目
        const newItem = suggestions[index];
        newItem.classList.add('highlighted');

        // 更新输入框
        this.elements.searchInput.value = newItem.dataset.suggestion;
        this.currentQuery = newItem.dataset.suggestion;
    }

    /**
     * 显示搜索状态
     */
    showSearchStatus(message) {
        this.elements.searchStatus.querySelector('.status-text').textContent = message;
        this.elements.searchStatus.style.display = 'block';
    }

    /**
     * 隐藏搜索状态
     */
    hideSearchStatus() {
        this.elements.searchStatus.style.display = 'none';
    }

    /**
     * 显示搜索结果
     */
    displaySearchResults(searchData) {
        const results = searchData.results || searchData;

        if (!results || results.length === 0) {
            this.elements.searchResults.innerHTML = `
                <div class="no-results">
                    <div class="no-results-icon">🔍</div>
                    <div class="no-results-text">未找到匹配的结果</div>
                    <div class="no-results-tips">
                        <p>建议：</p>
                        <ul>
                            <li>尝试更简单的关键词</li>
                            <li>检查拼写是否正确</li>
                            <li>使用更通用的描述</li>
                        </ul>
                    </div>
                </div>
            `;
            return;
        }

        const resultsHTML = results.map((result, index) => {
            const confidence = Math.round((result.confidence || result.relevanceScore || result.semanticScore || 0) * 100);
            const sources = result.sources ? result.sources.join(', ') : '关键词匹配';

            return `
                <div class="search-result-item" data-index="${index}">
                    <div class="result-image">
                        <img src="${result.path}" alt="${result.description || '图片'}" loading="lazy">
                        <div class="result-confidence">${confidence}%</div>
                    </div>
                    <div class="result-content">
                        <div class="result-title">${this.truncateText(result.description || '未知图片', 60)}</div>
                        <div class="result-tags">
                            ${(result.tags || []).slice(0, 5).map(tag =>
                                `<span class="result-tag">${tag}</span>`
                            ).join('')}
                        </div>
                        <div class="result-meta">
                            <span class="result-source">来源: ${sources}</span>
                            ${result.explanation ? `<span class="result-explanation">${result.explanation}</span>` : ''}
                        </div>
                    </div>
                    <div class="result-actions">
                        <button class="result-action" data-action="view" data-path="${result.path}">查看</button>
                        <button class="result-action" data-action="similar">相似</button>
                    </div>
                </div>
            `;
        }).join('');

        this.elements.searchResults.innerHTML = resultsHTML;

        // 绑定结果操作事件
        this.bindResultActions();
    }

    /**
     * 显示搜索摘要
     */
    displaySearchSummary(searchData) {
        if (!searchData.summary && !searchData.metadata) return;

        const summary = searchData.summary || {};
        const metadata = searchData.metadata || {};

        const summaryHTML = `
            <div class="summary-stats">
                <span class="stat-item">
                    <span class="stat-label">结果数量:</span>
                    <span class="stat-value">${summary.totalResults || (searchData.results ? searchData.results.length : 0)}</span>
                </span>
                ${summary.avgConfidence ? `
                    <span class="stat-item">
                        <span class="stat-label">平均相关度:</span>
                        <span class="stat-value">${Math.round(summary.avgConfidence * 100)}%</span>
                    </span>
                ` : ''}
                ${searchData.strategy ? `
                    <span class="stat-item">
                        <span class="stat-label">搜索策略:</span>
                        <span class="stat-value">${this.translateStrategy(searchData.strategy)}</span>
                    </span>
                ` : ''}
                ${metadata.sources ? `
                    <span class="stat-item">
                        <span class="stat-label">数据源:</span>
                        <span class="stat-value">${metadata.sources.join(', ')}</span>
                    </span>
                ` : ''}
            </div>
            ${summary.topCategories && summary.topCategories.length > 0 ? `
                <div class="summary-categories">
                    <span class="categories-label">主要分类:</span>
                    ${summary.topCategories.map(cat => `<span class="category-tag">${cat}</span>`).join('')}
                </div>
            ` : ''}
        `;

        this.elements.searchSummary.innerHTML = summaryHTML;
        this.elements.searchSummary.style.display = 'block';
    }

    /**
     * 显示搜索推荐
     */
    displaySearchRecommendations(searchData) {
        if (!searchData.suggestions || searchData.suggestions.length === 0) {
            this.elements.recommendations.style.display = 'none';
            return;
        }

        const recommendationsHTML = searchData.suggestions.map(suggestion => `
            <button class="recommendation-item" data-suggestion="${suggestion.text}">
                <span class="recommendation-text">${suggestion.text}</span>
                <span class="recommendation-type">${suggestion.type}</span>
            </button>
        `).join('');

        this.elements.recommendations.querySelector('.recommendations-content').innerHTML = recommendationsHTML;
        this.elements.recommendations.style.display = 'block';

        // 绑定推荐点击事件
        this.bindRecommendationActions();
    }

    /**
     * 绑定搜索结果操作事件
     */
    bindResultActions() {
        const actionBtns = this.elements.searchResults.querySelectorAll('.result-action');
        actionBtns.forEach(btn => {
            btn.addEventListener('click', this.handleResultAction.bind(this));
        });

        // 绑定图片点击事件
        const images = this.elements.searchResults.querySelectorAll('.result-image img');
        images.forEach(img => {
            img.addEventListener('click', this.handleImageClick.bind(this));
        });
    }

    /**
     * 绑定推荐操作事件
     */
    bindRecommendationActions() {
        const recommendationBtns = this.elements.recommendations.querySelectorAll('.recommendation-item');
        recommendationBtns.forEach(btn => {
            btn.addEventListener('click', (event) => {
                const suggestion = event.currentTarget.dataset.suggestion;
                this.elements.searchInput.value = suggestion;
                this.currentQuery = suggestion;
                this.performSearch(suggestion, { isSubmit: true });
            });
        });
    }

    /**
     * 处理搜索结果操作
     */
    handleResultAction(event) {
        const action = event.currentTarget.dataset.action;
        const path = event.currentTarget.dataset.path;

        switch (action) {
            case 'view':
                this.viewImage(path);
                break;
            case 'similar':
                this.findSimilarImages(path);
                break;
        }
    }

    /**
     * 处理图片点击
     */
    handleImageClick(event) {
        const imagePath = event.currentTarget.src;
        this.viewImage(imagePath);
    }

    /**
     * 查看图片
     */
    viewImage(imagePath) {
        // 跳转到图库页面查看具体图片
        const galleryUrl = `gallery.html?view=${encodeURIComponent(imagePath)}`;
        window.open(galleryUrl, '_blank');
    }

    /**
     * 查找相似图片
     */
    async findSimilarImages(imagePath) {
        try {
            // 基于图片的标签和分类查找相似图片
            const imageData = this.getImageData();
            const targetImage = imageData.find(img => img.path === imagePath);

            if (!targetImage) return;

            const similarQuery = (targetImage.tags || []).slice(0, 3).join(' ');
            this.elements.searchInput.value = similarQuery;
            this.currentQuery = similarQuery;
            await this.performSearch(similarQuery, { isSubmit: true });

        } catch (error) {
            console.error('查找相似图片失败:', error);
        }
    }

    /**
     * 清空搜索结果
     */
    clearResults() {
        this.elements.searchResults.innerHTML = '';
        this.elements.searchSummary.style.display = 'none';
        this.elements.recommendations.style.display = 'none';
        this.searchResults = [];
    }

    /**
     * 检查AI可用性
     */
    checkAIAvailability() {
        const isAIAvailable = window.openrouterAPI && window.openrouterAPI.apiKey;
        this.elements.aiModeBtn.disabled = !isAIAvailable;

        if (!isAIAvailable) {
            this.elements.aiModeBtn.title = '需要配置AI API密钥';
        }
    }

    /**
     * 添加到搜索历史
     */
    addToSearchHistory(query) {
        // 移除重复项
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);

        // 添加到开头
        this.searchHistory.unshift({
            query: query,
            timestamp: Date.now(),
            resultCount: this.searchResults.length
        });

        // 限制历史记录数量
        this.searchHistory = this.searchHistory.slice(0, this.config.maxHistoryItems);

        this.saveSearchHistory();
        this.updateHistoryDisplay();
    }

    /**
     * 获取最近查询
     */
    getRecentQueries() {
        return this.searchHistory.slice(0, 5).map(item => item.query);
    }

    /**
     * 更新历史记录显示
     */
    updateHistoryDisplay() {
        const historyHTML = this.searchHistory.map(item => `
            <div class="history-item" data-query="${item.query}">
                <span class="history-query">${item.query}</span>
                <span class="history-meta">${this.formatTimestamp(item.timestamp)} • ${item.resultCount} 结果</span>
            </div>
        `).join('');

        this.elements.historyContent.innerHTML = historyHTML;
    }

    /**
     * 去重建议
     */
    deduplicateSuggestions(suggestions) {
        const seen = new Set();
        return suggestions.filter(suggestion => {
            const key = suggestion.text.toLowerCase();
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    /**
     * 获取图片数据
     */
    getImageData() {
        if (window.imagesMetadata && typeof window.imagesMetadata.getAllImages === 'function') {
            return window.imagesMetadata.getAllImages();
        }
        return [];
    }

    /**
     * 翻译搜索策略
     */
    translateStrategy(strategy) {
        const strategyMap = {
            'ai_semantic': 'AI语义',
            'hybrid': '混合搜索',
            'traditional': '关键词',
            'semantic': '语义搜索',
            'auto': '智能选择',
            'fallback': '基础搜索'
        };
        return strategyMap[strategy] || strategy;
    }

    /**
     * 截断文本
     */
    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.slice(0, maxLength) + '...';
    }

    /**
     * 格式化时间戳
     */
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);

        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`;
        return date.toLocaleDateString();
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 获取嵌套属性
     */
    getNestedProperty(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    /**
     * 加载搜索历史
     */
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('search_interface_history');
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.warn('加载搜索历史失败:', error);
            return [];
        }
    }

    /**
     * 保存搜索历史
     */
    saveSearchHistory() {
        try {
            localStorage.setItem('search_interface_history', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.warn('保存搜索历史失败:', error);
        }
    }

    /**
     * 加载用户偏好
     */
    loadUserPreferences() {
        try {
            const preferences = localStorage.getItem('search_interface_preferences');
            if (preferences) {
                const prefs = JSON.parse(preferences);
                this.config = { ...this.config, ...prefs.config };
                this.searchMode = prefs.searchMode || this.searchMode;

                // 更新界面状态
                this.elements.modeBtns.forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.mode === this.searchMode);
                });
            }
        } catch (error) {
            console.warn('加载用户偏好失败:', error);
        }
    }

    /**
     * 保存用户偏好
     */
    saveUserPreferences() {
        try {
            const preferences = {
                config: this.config,
                searchMode: this.searchMode
            };
            localStorage.setItem('search_interface_preferences', JSON.stringify(preferences));
        } catch (error) {
            console.warn('保存用户偏好失败:', error);
        }
    }

    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.saveUserPreferences();
    }

    /**
     * 获取搜索统计
     */
    getSearchStats() {
        return {
            totalSearches: this.searchHistory.length,
            currentMode: this.searchMode,
            isInitialized: this.isInitialized,
            hasResults: this.searchResults.length > 0
        };
    }

    /**
     * 销毁搜索界面
     */
    destroy() {
        // 移除事件监听器
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        this.eventListeners = [];

        // 清空容器
        if (this.container) {
            this.container.innerHTML = '';
        }

        // 重置状态
        this.isInitialized = false;
        this.currentQuery = '';
        this.searchResults = [];
        this.suggestions = [];
    }
}

// 导出全局实例
global.SearchInterface = SearchInterface;
global.searchInterface = new SearchInterface();
})(window);
