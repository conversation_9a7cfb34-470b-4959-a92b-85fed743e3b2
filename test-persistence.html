<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据持久化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 数据持久化测试工具</h1>
    
    <div class="test-section info">
        <h3>测试说明</h3>
        <p>此工具用于测试图片索引数据的持久化功能。点击下面的按钮来执行各种测试。</p>
    </div>

    <div class="test-section">
        <h3>1. 检查 localStorage 数据</h3>
        <button onclick="checkLocalStorage()">检查当前数据</button>
        <button onclick="clearLocalStorage()">清除数据</button>
        <div id="localStorage-result"></div>
    </div>

    <div class="test-section">
        <h3>2. 测试 ImagesMetadata 初始化</h3>
        <button onclick="testImagesMetadata()">测试初始化</button>
        <div id="metadata-result"></div>
    </div>

    <div class="test-section">
        <h3>3. 模拟索引数据</h3>
        <button onclick="simulateIndexData()">添加测试数据</button>
        <button onclick="saveTestData()">保存测试数据</button>
        <div id="simulate-result"></div>
    </div>

    <div class="test-section">
        <h3>4. 测试页面刷新后数据恢复</h3>
        <button onclick="window.location.reload()">刷新页面</button>
        <p>刷新页面后，检查数据是否仍然存在</p>
    </div>

    <!-- 加载核心脚本 -->
    <script src="images-metadata.js"></script>
    <script src="image-classifier.js"></script>

    <script>
        // 等待脚本加载
        window.addEventListener('DOMContentLoaded', () => {
            console.log('测试页面加载完成');
            checkLocalStorage();
        });

        function checkLocalStorage() {
            const result = document.getElementById('localStorage-result');
            const key = 'images_metadata_v1';
            const data = localStorage.getItem(key);
            
            if (data) {
                try {
                    const parsed = JSON.parse(data);
                    const imageCount = Object.keys(parsed.images || {}).length;
                    result.innerHTML = `
                        <div class="success">
                            <strong>✅ 找到数据</strong><br>
                            图片数量: ${imageCount}<br>
                            数据大小: ${(data.length / 1024).toFixed(2)} KB<br>
                            <details>
                                <summary>查看数据结构</summary>
                                <pre>${JSON.stringify(parsed, null, 2).substring(0, 1000)}...</pre>
                            </details>
                        </div>
                    `;
                } catch (e) {
                    result.innerHTML = `<div class="error">❌ 数据格式错误: ${e.message}</div>`;
                }
            } else {
                result.innerHTML = `<div class="info">ℹ️ 未找到持久化数据</div>`;
            }
        }

        function clearLocalStorage() {
            localStorage.removeItem('images_metadata_v1');
            checkLocalStorage();
            console.log('已清除 localStorage 数据');
        }

        async function testImagesMetadata() {
            const result = document.getElementById('metadata-result');
            result.innerHTML = '<div class="info">⏳ 测试中...</div>';
            
            try {
                if (!window.imagesMetadata) {
                    result.innerHTML = '<div class="error">❌ ImagesMetadata 未加载</div>';
                    return;
                }

                // 等待初始化完成
                await window.imagesMetadata.waitForReady();
                
                const imageCount = window.imagesMetadata.images.size;
                const isReady = window.imagesMetadata.isReady;
                
                result.innerHTML = `
                    <div class="success">
                        <strong>✅ ImagesMetadata 测试通过</strong><br>
                        就绪状态: ${isReady}<br>
                        图片数量: ${imageCount}<br>
                        存储键: ${window.imagesMetadata.storageKey}
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        function simulateIndexData() {
            const result = document.getElementById('simulate-result');
            
            if (!window.imagesMetadata) {
                result.innerHTML = '<div class="error">❌ ImagesMetadata 未加载</div>';
                return;
            }

            // 添加测试数据
            const testImages = [
                {
                    path: 'test/image1.png',
                    description: '测试图片1 - 金融应用界面',
                    tags: ['金融', '支付', '界面设计'],
                    classifications: { app: ['金融应用'], page: ['支付界面'], control: ['按钮'] }
                },
                {
                    path: 'test/image2.png', 
                    description: '测试图片2 - 健康监测应用',
                    tags: ['健康', '监测', '数据可视化'],
                    classifications: { app: ['健康监测'], page: ['数据展示'], control: ['图表'] }
                }
            ];

            testImages.forEach(img => {
                window.imagesMetadata.addImage(img);
            });

            result.innerHTML = `
                <div class="success">
                    ✅ 已添加 ${testImages.length} 条测试数据<br>
                    当前总数: ${window.imagesMetadata.images.size}
                </div>
            `;
        }

        async function saveTestData() {
            const result = document.getElementById('simulate-result');
            
            if (!window.imagesMetadata) {
                result.innerHTML = '<div class="error">❌ ImagesMetadata 未加载</div>';
                return;
            }

            try {
                await window.imagesMetadata.saveToStorage();
                result.innerHTML += `<div class="success">💾 数据已保存到 localStorage</div>`;
                
                // 验证保存
                setTimeout(() => {
                    checkLocalStorage();
                }, 100);
            } catch (error) {
                result.innerHTML += `<div class="error">❌ 保存失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
