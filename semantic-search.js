/**
 * 语义搜索引擎
 * 实现基于自然语言的智能图片搜索功能
 */
class SemanticSearchEngine {
    constructor() {
        this.searchHistory = this.loadSearchHistory();
        this.searchCache = new Map();
        this.semanticCache = new Map(); // AI语义搜索缓存

        // 颜色关键词映射
        this.colorKeywords = {
            '红色': ['红', '红色', 'red'],
            '蓝色': ['蓝', '蓝色', 'blue'],
            '绿色': ['绿', '绿色', 'green'],
            '黄色': ['黄', '黄色', 'yellow'],
            '紫色': ['紫', '紫色', 'purple'],
            '橙色': ['橙', '橙色', 'orange'],
            '黑色': ['黑', '黑色', 'dark', '深色'],
            '白色': ['白', '白色', 'light', '浅色'],
            '灰色': ['灰', '灰色', 'gray', 'grey']
        };

        // 应用类型关键词
        this.appTypeKeywords = {
            '金融': ['金融', '银行', '支付', '钱包', '投资', '理财', '交易'],
            '健康': ['健康', '医疗', '运动', '健身', '习惯', '追踪'],
            '教育': ['教育', '学习', '课程', '培训', '语言'],
            '体育': ['体育', '运动', '比赛', '足球', '篮球'],
            '社交': ['社交', '聊天', '通讯', '朋友'],
            '购物': ['购物', '商城', '电商', '买卖'],
            '工具': ['工具', '实用', '效率', '办公'],
            '娱乐': ['娱乐', '游戏', '音乐', '视频']
        };

        // 页面类型关键词
        this.pageTypeKeywords = {
            '登录': ['登录', '登陆', '注册', '账号'],
            '主页': ['主页', '首页', '主界面', '主屏幕'],
            '详情': ['详情', '详细', '信息', '内容'],
            '设置': ['设置', '配置', '选项', '偏好'],
            '列表': ['列表', '清单', '目录'],
            '搜索': ['搜索', '查找', '检索'],
            '支付': ['支付', '付款', '结算', '订单'],
            '个人': ['个人', '用户', '我的', '账户']
        };

        // 控件关键词
        this.controlKeywords = {
            '按钮': ['按钮', 'button', '点击'],
            '输入框': ['输入框', '输入', 'input', '文本框'],
            '导航': ['导航', '菜单', 'nav', '导航栏'],
            '卡片': ['卡片', 'card', '卡片式'],
            '列表': ['列表', 'list', '清单'],
            '图表': ['图表', 'chart', '数据', '统计'],
            '弹窗': ['弹窗', 'modal', '对话框', '提示'],
            '标签': ['标签', 'tag', '标记']
        };

        // 同义词与相关词映射（用于关键词扩展与召回）
        this.synonymsMap = {
            '运动': ['体育', '健身', '锻炼', '运动类', '赛事', '比赛', '足球', '篮球'],
            '体育': ['运动', '赛事', '比赛', '足球', '篮球'],
            '健身': ['健康', '运动', '锻炼'],
            '钱包': ['金融', '支付', '账户', '资产'],
            '支付': ['付款', '结算', '账单'],
            '登录': ['登陆', '注册', '账号', '登录页', '登录界面'],
            '设置': ['配置', '偏好', '系统设置', '设置页', '设置界面'],
            '深色': ['暗色', '黑色', '夜间', '深色主题'],
            '浅色': ['白色', '亮色', '浅色主题'],
        };

        // 把映射中的所有键也加入到各自关键词词表，增强命中
        this.appTypeKeywords['体育'].push('健身', '锻炼', '赛事');
        this.appTypeKeywords['健康'].push('健身', '锻炼');

    }

    /**
     * 执行语义搜索
     * @param {string} query - 用户输入的搜索查询
     * @param {Array} images - 图片数据数组
     * @returns {Array} 搜索结果数组，按相关度排序
     */
    async search(query, images) {
        if (!query || !query.trim()) {
            return [];
        }

        const normalizedQuery = query.trim().toLowerCase();

        // 检查缓存
        if (this.searchCache.has(normalizedQuery)) {
            console.log('使用缓存的搜索结果');
            return this.searchCache.get(normalizedQuery);
        }

        console.log(`开始语义搜索: "${query}"`);

        // 保存搜索历史
        this.addToSearchHistory(query);

        // 分析查询意图
        const queryAnalysis = this.analyzeQuery(normalizedQuery);
        console.log('查询分析结果:', queryAnalysis);

        // 计算每张图片的相关度
        const results = [];
        for (const image of images) {
            const relevanceScore = this.calculateRelevance(queryAnalysis, image);
            if (relevanceScore > 0) {
                results.push({
                    ...image,
                    relevanceScore,
                    matchedKeywords: this.getMatchedKeywords(queryAnalysis, image)
                });
            }
        }

        // 去重（按图片路径）
        const seen = new Set();
        const uniqueResults = [];
        for (const r of results) {
            const key = String(r.path || '').toLowerCase();
            if (!seen.has(key)) { seen.add(key); uniqueResults.push(r); }
        }

        // 按相关度排序
        uniqueResults.sort((a, b) => b.relevanceScore - a.relevanceScore);

        // 缓存结果
        this.searchCache.set(normalizedQuery, uniqueResults);

        console.log(`搜索完成，找到 ${uniqueResults.length} 个结果`);
        return uniqueResults;
    }

    /**
     * 分析用户查询，提取关键信息
     * @param {string} query - 标准化后的查询字符串
     * @returns {Object} 查询分析结果
     */
    analyzeQuery(query) {
        const analysis = {
            originalQuery: query,
            keywords: [],
            colors: [],
            appTypes: [],
            pageTypes: [],
            controls: [],
            sentiment: 'neutral'
        };

        // 基础关键词（中文通常无空格，先把整句当作一个token，再加入已知字典命中的词）
        const baseTokens = [query, ...query.split(/\s+/).filter(Boolean)];

        // 同义词扩展：如果命中任一键或值，展开所有相关词
        const expanded = new Set();
        for (const token of baseTokens) {
            if (!token) continue;
            expanded.add(token);
            for (const [key, syns] of Object.entries(this.synonymsMap)) {
                if (token.includes(key) || syns.some(s => token.includes(s))) {
                    expanded.add(key);
                    syns.forEach(s => expanded.add(s));
                }
            }
        }
        analysis.keywords = Array.from(expanded).map(t => t.toLowerCase());

        // 颜色
        for (const [color, keywords] of Object.entries(this.colorKeywords)) {
            if (keywords.some(k => query.includes(k))) analysis.colors.push(color);
        }

        // 应用类型
        for (const [type, keywords] of Object.entries(this.appTypeKeywords)) {
            if (keywords.some(k => analysis.keywords.some(w => w.includes(k.toLowerCase())))) {
                analysis.appTypes.push(type);
            }
        }
        // 针对“运动”→体育的增强
        if (analysis.keywords.some(k => k.includes('运动')) && !analysis.appTypes.includes('体育')) {
            analysis.appTypes.push('体育');
        }

        // 页面类型
        for (const [type, keywords] of Object.entries(this.pageTypeKeywords)) {
            if (keywords.some(k => analysis.keywords.some(w => w.includes(k.toLowerCase())))) {
                analysis.pageTypes.push(type);
            }
        }

        // 控件类型
        for (const [type, keywords] of Object.entries(this.controlKeywords)) {
            if (keywords.some(k => analysis.keywords.some(w => w.includes(k.toLowerCase())))) {
                analysis.controls.push(type);
            }
        }

        return analysis;
    }

    /**
     * AI语义搜索 - 使用AI API进行深度语义理解
     * @param {string} query - 用户搜索查询
     * @param {Array} images - 图片数据数组
     * @returns {Promise<Array>} 搜索结果数组，按相关度排序
     */
    async semanticSearch(query, images) {
        if (!query || !query.trim()) {
            return [];
        }

        const normalizedQuery = query.trim();

        // 检查语义搜索缓存
        const cacheKey = `semantic_${normalizedQuery}`;
        if (this.semanticCache.has(cacheKey)) {
            console.log('使用AI语义搜索缓存结果');
            return this.semanticCache.get(cacheKey);
        }

        console.log(`开始AI语义搜索: "${query}"`);

        try {
            // 检查AI API可用性
            const api = window.openrouterAPI;
            if (!api || !api.apiKey) {
                console.warn('AI API未配置，回退到传统搜索');
                return await this.search(query, images);
            }

            // 使用AI分析查询意图
            const queryIntent = await this.analyzeQueryWithAI(normalizedQuery);
            console.log('AI查询意图分析:', queryIntent);

            // 对每张图片进行AI语义匹配
            const semanticResults = await this.performSemanticMatching(queryIntent, images);

            // 混合传统搜索结果增强准确性
            const traditionalResults = await this.search(query, images);

            // 融合两种搜索结果
            const fusedResults = this.fuseSearchResults(semanticResults, traditionalResults, queryIntent);

            // 缓存结果
            this.semanticCache.set(cacheKey, fusedResults);

            console.log(`AI语义搜索完成，找到 ${fusedResults.length} 个结果`);
            return fusedResults;

        } catch (error) {
            console.error('AI语义搜索失败，回退到传统搜索:', error);
            return await this.search(query, images);
        }
    }

    /**
     * 使用AI分析查询意图
     * @param {string} query - 用户查询
     * @returns {Promise<Object>} 查询意图分析结果
     */
    async analyzeQueryWithAI(query) {
        try {
            const api = window.openrouterAPI;
            const prompt = `分析以下图片搜索查询的用户意图，返回JSON格式结果：

查询: "${query}"

请分析并返回：
{
  "intent": "搜索意图描述",
  "keywords": ["关键词1", "关键词2"],
  "colors": ["颜色1", "颜色2"],
  "appTypes": ["应用类型1"],
  "pageTypes": ["页面类型1"],
  "visualFeatures": ["视觉特征1", "视觉特征2"],
  "functionalFeatures": ["功能特征1"],
  "searchScope": "broad|specific|visual|functional",
  "userNeed": "用户真实需求描述"
}

可能的应用类型：金融、健康、教育、体育、社交、购物、工具、娱乐、人工智能、商业、协作、沟通、开发者工具、生活方式、医疗、音乐、地图导航、新闻、效率、旅行等

可能的页面类型：登录、注册、主页、详情、设置、列表、搜索、支付、个人资料、导览、启动画面、验证、账户管理、帮助等

可能的视觉特征：现代化、极简、卡片式、深色主题、浅色主题、高对比度、渐变、阴影、圆角、网格布局等

只返回JSON，不要其他解释。`;

            const response = await api.callAPI(prompt);
            const analysisResult = JSON.parse(response);

            return {
                intent: analysisResult.intent || '',
                keywords: Array.isArray(analysisResult.keywords) ? analysisResult.keywords : [],
                colors: Array.isArray(analysisResult.colors) ? analysisResult.colors : [],
                appTypes: Array.isArray(analysisResult.appTypes) ? analysisResult.appTypes : [],
                pageTypes: Array.isArray(analysisResult.pageTypes) ? analysisResult.pageTypes : [],
                visualFeatures: Array.isArray(analysisResult.visualFeatures) ? analysisResult.visualFeatures : [],
                functionalFeatures: Array.isArray(analysisResult.functionalFeatures) ? analysisResult.functionalFeatures : [],
                searchScope: analysisResult.searchScope || 'broad',
                userNeed: analysisResult.userNeed || query,
                originalQuery: query
            };

        } catch (error) {
            console.warn('AI查询分析失败，使用基础分析:', error);
            // 回退到基础分析
            const basicAnalysis = this.analyzeQuery(query);
            return {
                intent: `搜索包含"${query}"的相关界面`,
                keywords: basicAnalysis.keywords,
                colors: basicAnalysis.colors,
                appTypes: basicAnalysis.appTypes,
                pageTypes: basicAnalysis.pageTypes,
                visualFeatures: [],
                functionalFeatures: [],
                searchScope: 'broad',
                userNeed: query,
                originalQuery: query
            };
        }
    }

    /**
     * 执行AI语义匹配
     * @param {Object} queryIntent - AI分析的查询意图
     * @param {Array} images - 图片数据数组
     * @returns {Promise<Array>} 语义匹配结果
     */
    async performSemanticMatching(queryIntent, images) {
        const semanticResults = [];

        // 对每张图片计算语义相关度
        for (const image of images) {
            const semanticScore = await this.calculateSemanticRelevance(queryIntent, image);

            if (semanticScore > 0.1) { // 设置最低阈值
                semanticResults.push({
                    ...image,
                    semanticScore,
                    semanticReasons: this.generateSemanticReasons(queryIntent, image),
                    matchType: 'semantic'
                });
            }
        }

        // 按语义相关度排序
        return semanticResults.sort((a, b) => b.semanticScore - a.semanticScore);
    }

    /**
     * 计算AI语义相关度
     * @param {Object} queryIntent - 查询意图
     * @param {Object} image - 图片对象
     * @returns {Promise<number>} 语义相关度分数 (0-1)
     */
    async calculateSemanticRelevance(queryIntent, image) {
        let totalScore = 0;
        let totalWeight = 0;

        // 1. 意图匹配 (权重: 30%)
        const intentWeight = 0.3;
        totalWeight += intentWeight;

        const intentScore = this.calculateIntentMatch(queryIntent, image);
        totalScore += intentScore * intentWeight;

        // 2. 关键词语义匹配 (权重: 25%)
        const keywordWeight = 0.25;
        totalWeight += keywordWeight;

        const keywordScore = this.calculateSemanticKeywordMatch(queryIntent.keywords, image);
        totalScore += keywordScore * keywordWeight;

        // 3. 视觉特征匹配 (权重: 20%)
        const visualWeight = 0.2;
        totalWeight += visualWeight;

        const visualScore = this.calculateVisualFeatureMatch(queryIntent, image);
        totalScore += visualScore * visualWeight;

        // 4. 功能特征匹配 (权重: 15%)
        const functionalWeight = 0.15;
        totalWeight += functionalWeight;

        const functionalScore = this.calculateFunctionalMatch(queryIntent, image);
        totalScore += functionalScore * functionalWeight;

        // 5. 分类精确匹配加权 (权重: 10%)
        const categoryWeight = 0.1;
        totalWeight += categoryWeight;

        const categoryScore = this.calculateCategoryMatch(queryIntent, image);
        totalScore += categoryScore * categoryWeight;

        return totalWeight > 0 ? Math.min(totalScore / totalWeight, 1) : 0;
    }

    /**
     * 计算意图匹配度
     */
    calculateIntentMatch(queryIntent, image) {
        if (!queryIntent.intent || !image.description) return 0;

        // 使用简化的语义相似度算法
        const intentWords = queryIntent.intent.toLowerCase().split(/\s+/);
        const descWords = image.description.toLowerCase().split(/\s+/);

        let matches = 0;
        for (const word of intentWords) {
            if (word.length < 2) continue;
            if (descWords.some(dw => dw.includes(word) || word.includes(dw))) {
                matches++;
            }
        }

        return intentWords.length > 0 ? matches / intentWords.length : 0;
    }

    /**
     * 计算语义关键词匹配
     */
    calculateSemanticKeywordMatch(keywords, image) {
        if (!keywords || keywords.length === 0) return 0;

        let score = 0;
        const imageText = `${image.description || ''} ${(image.tags || []).join(' ')}`.toLowerCase();

        for (const keyword of keywords) {
            if (keyword.length < 2) continue;

            // 直接匹配
            if (imageText.includes(keyword.toLowerCase())) {
                score += 1;
                continue;
            }

            // 同义词匹配
            let synonymFound = false;
            for (const [key, syns] of Object.entries(this.synonymsMap)) {
                if (keyword.toLowerCase().includes(key) || syns.some(s => keyword.toLowerCase().includes(s))) {
                    if (imageText.includes(key) || syns.some(s => imageText.includes(s))) {
                        score += 0.8;
                        synonymFound = true;
                        break;
                    }
                }
            }

            // 部分匹配
            if (!synonymFound) {
                const partialMatch = imageText.split(/\s+/).some(word =>
                    word.includes(keyword.toLowerCase()) || keyword.toLowerCase().includes(word)
                );
                if (partialMatch) score += 0.5;
            }
        }

        return keywords.length > 0 ? Math.min(score / keywords.length, 1) : 0;
    }

    /**
     * 计算视觉特征匹配
     */
    calculateVisualFeatureMatch(queryIntent, image) {
        if (!queryIntent.visualFeatures || queryIntent.visualFeatures.length === 0) return 0;

        let matches = 0;
        const imageText = `${image.description || ''} ${(image.tags || []).join(' ')}`.toLowerCase();

        for (const feature of queryIntent.visualFeatures) {
            if (imageText.includes(feature.toLowerCase())) {
                matches++;
            }
        }

        return queryIntent.visualFeatures.length > 0 ? matches / queryIntent.visualFeatures.length : 0;
    }

    /**
     * 计算功能特征匹配
     */
    calculateFunctionalMatch(queryIntent, image) {
        if (!queryIntent.functionalFeatures || queryIntent.functionalFeatures.length === 0) return 0;

        let matches = 0;
        const imageText = `${image.description || ''} ${(image.tags || []).join(' ')}`.toLowerCase();

        for (const feature of queryIntent.functionalFeatures) {
            if (imageText.includes(feature.toLowerCase())) {
                matches++;
            }
        }

        return queryIntent.functionalFeatures.length > 0 ? matches / queryIntent.functionalFeatures.length : 0;
    }

    /**
     * 计算分类匹配
     */
    calculateCategoryMatch(queryIntent, image) {
        if (!image.classifications) return 0;

        let score = 0;
        let totalCategories = 0;

        // 应用类型匹配
        if (queryIntent.appTypes && queryIntent.appTypes.length > 0) {
            totalCategories += queryIntent.appTypes.length;
            for (const appType of queryIntent.appTypes) {
                if (image.classifications.app && image.classifications.app.includes(appType)) {
                    score += 1;
                }
            }
        }

        // 页面类型匹配
        if (queryIntent.pageTypes && queryIntent.pageTypes.length > 0) {
            totalCategories += queryIntent.pageTypes.length;
            for (const pageType of queryIntent.pageTypes) {
                if (image.classifications.page && image.classifications.page.includes(pageType)) {
                    score += 1;
                }
            }
        }

        return totalCategories > 0 ? score / totalCategories : 0;
    }

    /**
     * 生成语义匹配原因说明
     */
    generateSemanticReasons(queryIntent, image) {
        const reasons = [];

        // 检查各种匹配类型
        if (queryIntent.intent && image.description &&
            image.description.toLowerCase().includes(queryIntent.intent.toLowerCase().split(' ')[0])) {
            reasons.push(`意图匹配: ${queryIntent.intent}`);
        }

        if (queryIntent.keywords) {
            const matchedKeywords = queryIntent.keywords.filter(kw =>
                `${image.description || ''} ${(image.tags || []).join(' ')}`.toLowerCase().includes(kw.toLowerCase())
            );
            if (matchedKeywords.length > 0) {
                reasons.push(`关键词匹配: ${matchedKeywords.slice(0, 3).join(', ')}`);
            }
        }

        if (queryIntent.visualFeatures) {
            const matchedFeatures = queryIntent.visualFeatures.filter(vf =>
                `${image.description || ''} ${(image.tags || []).join(' ')}`.toLowerCase().includes(vf.toLowerCase())
            );
            if (matchedFeatures.length > 0) {
                reasons.push(`视觉特征: ${matchedFeatures.slice(0, 2).join(', ')}`);
            }
        }

        return reasons.length > 0 ? reasons : ['语义相似度匹配'];
    }

    /**
     * 融合语义搜索和传统搜索结果
     * @param {Array} semanticResults - AI语义搜索结果
     * @param {Array} traditionalResults - 传统搜索结果
     * @param {Object} queryIntent - 查询意图
     * @returns {Array} 融合后的搜索结果
     */
    fuseSearchResults(semanticResults, traditionalResults, queryIntent) {
        const fusedMap = new Map();

        // 添加语义搜索结果 (权重: 0.7)
        for (const result of semanticResults) {
            const key = result.path || result.id;
            if (key) {
                fusedMap.set(key, {
                    ...result,
                    fusedScore: (result.semanticScore || 0) * 0.7,
                    sources: ['semantic']
                });
            }
        }

        // 添加传统搜索结果 (权重: 0.3，但提供补充覆盖)
        for (const result of traditionalResults) {
            const key = result.path || result.id;
            if (key) {
                if (fusedMap.has(key)) {
                    // 已存在，增强分数
                    const existing = fusedMap.get(key);
                    existing.fusedScore = Math.max(existing.fusedScore,
                        existing.fusedScore + (result.relevanceScore || 0) * 0.3);
                    existing.sources.push('traditional');
                    existing.matchedKeywords = [
                        ...(existing.matchedKeywords || []),
                        ...(result.matchedKeywords || [])
                    ];
                } else {
                    // 新结果，添加进来
                    fusedMap.set(key, {
                        ...result,
                        fusedScore: (result.relevanceScore || 0) * 0.3,
                        sources: ['traditional']
                    });
                }
            }
        }

        // 转换为数组并排序
        const fusedResults = Array.from(fusedMap.values());

        // 根据查询类型调整排序策略
        if (queryIntent.searchScope === 'specific') {
            // 精确搜索，优先高分数结果
            fusedResults.sort((a, b) => b.fusedScore - a.fusedScore);
        } else {
            // 广泛搜索，平衡多样性
            fusedResults.sort((a, b) => {
                const scoreDiff = b.fusedScore - a.fusedScore;
                if (Math.abs(scoreDiff) < 0.1) {
                    // 分数接近时，优先多源匹配的结果
                    return b.sources.length - a.sources.length;
                }
                return scoreDiff;
            });
        }

        return fusedResults;
    }

    /**
     * 计算图片与查询的相关度
     * @param {Object} queryAnalysis - 查询分析结果
     * @param {Object} image - 图片对象
     * @returns {number} 相关度分数 (0-1)
     */
    calculateRelevance(queryAnalysis, image) {
        let score = 0;
        let maxScore = 0;

        // 描述匹配 (权重: 40%)
        const descriptionWeight = 0.4;
        maxScore += descriptionWeight;
        if (image.description) {
            const descriptionScore = this.calculateTextSimilarity(
                queryAnalysis.originalQuery,
                image.description.toLowerCase()
            );
            score += descriptionScore * descriptionWeight;
        }

        // 标签匹配 (权重: 30%)
        const tagsWeight = 0.3;
        maxScore += tagsWeight;
        if (image.tags && image.tags.length > 0) {
            const tagsText = image.tags.join(' ').toLowerCase();
            const tagsScore = this.calculateTextSimilarity(
                queryAnalysis.originalQuery,
                tagsText
            );
            score += tagsScore * tagsWeight;
        }

        // 分类匹配 (权重: 20%)
        const categoryWeight = 0.2;
        maxScore += categoryWeight;
        if (image.classifications) {
            let categoryScore = 0;

            // 检查应用类型匹配
            if (queryAnalysis.appTypes.length > 0 && image.classifications.app) {
                for (const appType of queryAnalysis.appTypes) {
                    if (image.classifications.app.includes(appType)) {
                        categoryScore += 0.5;
                    }
                }
            }

            // 检查页面类型匹配
            if (queryAnalysis.pageTypes.length > 0 && image.classifications.page) {
                for (const pageType of queryAnalysis.pageTypes) {
                    if (image.classifications.page.includes(pageType)) {
                        categoryScore += 0.3;
                    }
                }
            }

            // 检查控件匹配
            if (queryAnalysis.controls.length > 0 && image.classifications.control) {
                for (const control of queryAnalysis.controls) {
                    if (image.classifications.control.includes(control)) {
                        categoryScore += 0.2;
                    }
                }
            }

            score += Math.min(categoryScore, 1) * categoryWeight;
        }

        // 关键词精确匹配 (权重: 10%)
        const keywordWeight = 0.1;
        maxScore += keywordWeight;
        let keywordScore = 0;
        for (const keyword of queryAnalysis.keywords) {
            if (image.description && image.description.toLowerCase().includes(keyword)) {
                keywordScore += 0.3;
            }
            if (image.tags) {
                for (const tag of image.tags) {
                    if (tag.toLowerCase().includes(keyword)) {
                        keywordScore += 0.2;
                    }
                }
            }
        }
        score += Math.min(keywordScore, 1) * keywordWeight;

        // 标准化分数
        return maxScore > 0 ? Math.min(score / maxScore, 1) : 0;
    }

    /**
     * 计算文本相似度
     * @param {string} text1 - 文本1
     * @param {string} text2 - 文本2
     * @returns {number} 相似度分数 (0-1)
     */
    calculateTextSimilarity(text1, text2) {
        // 基于关键词+同义词的粗粒度匹配，兼容中文
        const baseTokens = [text1, ...text1.split(/\s+/).filter(Boolean)];
        const tokens = new Set();
        for (const t of baseTokens) {
            if (!t) continue;
            tokens.add(t);
            for (const [key, syns] of Object.entries(this.synonymsMap)) {
                if (t.includes(key) || syns.some(s => t.includes(s))) {
                    tokens.add(key);
                    syns.forEach(s => tokens.add(s));
                }
            }
        }
        const candidates = Array.from(tokens).filter(x => x && x.length >= 1).map(x => x.toLowerCase());
        const haystack = (text2 || '').toLowerCase();

        let matches = 0;
        for (const token of candidates) {
            if (haystack.includes(token)) {
                matches++;
            }
        }

        return candidates.length > 0 ? Math.min(matches / candidates.length, 1) : 0;
    }

    /**
     * 获取匹配的关键词
     * @param {Object} queryAnalysis - 查询分析结果
     * @param {Object} image - 图片对象
     * @returns {Array} 匹配的关键词数组
     */
    getMatchedKeywords(queryAnalysis, image) {
        const matched = [];

        // 检查描述中的匹配
        if (image.description) {
            for (const keyword of queryAnalysis.keywords) {
                if (image.description.toLowerCase().includes(keyword)) {
                    matched.push(keyword);
                }
            }
        }

        // 检查标签中的匹配
        if (image.tags) {
            for (const tag of image.tags) {
                for (const keyword of queryAnalysis.keywords) {
                    if (tag.toLowerCase().includes(keyword)) {
                        matched.push(keyword);
                    }
                }
            }
        }

        return [...new Set(matched)]; // 去重
    }

    /**
     * 添加到搜索历史
     * @param {string} query - 搜索查询
     */
    addToSearchHistory(query) {
        const trimmedQuery = query.trim();
        if (!trimmedQuery) return;

        // 移除重复项
        this.searchHistory = this.searchHistory.filter(item => item.query !== trimmedQuery);

        // 添加到开头
        this.searchHistory.unshift({
            query: trimmedQuery,
            timestamp: Date.now()
        });

        // 限制历史记录数量
        if (this.searchHistory.length > 20) {
            this.searchHistory = this.searchHistory.slice(0, 20);
        }

        // 保存到本地存储
        this.saveSearchHistory();
    }

    /**
     * 获取搜索建议
     * @param {string} input - 用户输入
     * @returns {Array} 搜索建议数组
     */
    getSearchSuggestions(input) {
        // 当输入较短时，优先给出基于实际图片库的热门建议
        if (!input || input.trim().length < 2) {
            return this.getPopularSearches();
        }

        const suggestions = [];
        const lowerInput = input.toLowerCase();

        // 1) 历史记录
        for (const item of this.searchHistory) {
            if (item.query.toLowerCase().includes(lowerInput)) {
                suggestions.push({ text: item.query, type: 'history' });
            }
        }

        // 2) 基于图片元数据的动态建议（仅加入能命中的）
        try {
            if (window.imagesMetadata && typeof window.imagesMetadata.getAllImages === 'function') {
                const all = window.imagesMetadata.getAllImages();
                const tagFreq = new Map();

                for (const img of all) {
                    // 标签
                    (img.tags || []).forEach(tag => {
                        const t = String(tag).toLowerCase();
                        if (!t) return;
                        tagFreq.set(t, (tagFreq.get(t) || 0) + 1);
                    });
                    // 从描述中抽取简单候选（分隔符切分，过滤过短词）
                    if (img.description) {
                        const tokens = String(img.description).toLowerCase().split(/[^a-z0-9\u4e00-\u9fa5]+/);
                        tokens.filter(w => w && w.length >= 2).forEach(w => tagFreq.set(w, (tagFreq.get(w) || 0) + 0));
                    }
                }

                // 选择包含输入片段的高频标签
                const dynamic = Array.from(tagFreq.entries())
                    .filter(([t]) => t.includes(lowerInput))
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 6)
                    .map(([t]) => ({ text: t, type: 'suggestion' }));

                for (const s of dynamic) {
                    if (!suggestions.some(x => x.text === s.text)) suggestions.push(s);
                }
            }
        } catch (e) {
            console.warn('生成动态建议失败:', e);
        }

        // 3) 同义词补全提示（例如输入“运动”提示“体育/健身”）
        for (const [key, syns] of Object.entries(this.synonymsMap)) {
            if (key.includes(lowerInput) || lowerInput.includes(key) || syns.some(s => s.includes(lowerInput))) {
                [key, ...syns].slice(0, 3).forEach(t => {
                    if (!suggestions.some(x => x.text === t)) suggestions.push({ text: t, type: 'suggestion' });
                });
            }
        }

        return suggestions.slice(0, 8);
    }

    /**
     * 获取热门搜索
     * @returns {Array} 热门搜索数组
     */
    getPopularSearches() {
        try {
            if (window.imagesMetadata && typeof window.imagesMetadata.getAllImages === 'function') {
                const all = window.imagesMetadata.getAllImages();
                const freq = new Map();
                for (const img of all) {
                    (img.tags || []).forEach(tag => {
                        const t = String(tag).trim();
                        if (!t) return;
                        freq.set(t, (freq.get(t) || 0) + 1);
                    });
                }
                const topTags = Array.from(freq.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 6)
                    .map(([t]) => ({ text: t, type: 'popular' }));
                if (topTags.length > 0) return topTags;
            }
        } catch (e) {
            console.warn('生成热门搜索失败，使用预设:', e);
        }
        // 退化为预设
        return [
            { text: '金融应用', type: 'popular' },
            { text: '体育应用', type: 'popular' },
            { text: '登录页面', type: 'popular' },
            { text: '设置界面', type: 'popular' },
            { text: '深色主题', type: 'popular' },
            { text: '卡片布局', type: 'popular' }
        ];
    }

    /**
     * 加载搜索历史
     * @returns {Array} 搜索历史数组
     */
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('image_search_history');
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.warn('加载搜索历史失败:', error);
            return [];
        }
    }

    /**
     * 保存搜索历史
     */
    saveSearchHistory() {
        try {
            localStorage.setItem('image_search_history', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.warn('保存搜索历史失败:', error);
        }
    }

    /**
     * 清除搜索历史
     */
    clearSearchHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
    }

    /**
     * 清除搜索缓存
     */
    clearSearchCache() {
        this.searchCache.clear();
        this.semanticCache.clear();
    }

    /**
     * 检查AI语义搜索是否可用
     * @returns {boolean} 是否可用
     */
    isAISearchAvailable() {
        const api = window.openrouterAPI;
        return !!(api && api.apiKey);
    }

    /**
     * 获取搜索模式建议
     * @param {string} query - 用户查询
     * @returns {string} 建议的搜索模式 ('semantic' | 'traditional')
     */
    getRecommendedSearchMode(query) {
        if (!this.isAISearchAvailable()) {
            return 'traditional';
        }

        // 复杂查询更适合语义搜索
        const complexPatterns = [
            /\w+的\w+/,  // "蓝色的金融"
            /\w+和\w+/,  // "支付和登录"
            /\w+(界面|页面|流程)/,  // "支付流程"
            /(现代|极简|商务|时尚)/,  // 风格描述
            /(深色|浅色|明亮|暗色)/  // 主题描述
        ];

        const isComplex = complexPatterns.some(pattern => pattern.test(query));
        return isComplex ? 'semantic' : 'traditional';
    }

    /**
     * 智能搜索 - 自动选择最佳搜索模式
     * @param {string} query - 搜索查询
     * @param {Array} images - 图片数据
     * @returns {Promise<Array>} 搜索结果
     */
    async smartSearch(query, images) {
        const recommendedMode = this.getRecommendedSearchMode(query);

        if (recommendedMode === 'semantic') {
            return await this.semanticSearch(query, images);
        } else {
            return await this.search(query, images);
        }
    }
}

// 创建全局实例
window.semanticSearchEngine = new SemanticSearchEngine();
