/**
 * 图片分类引擎
 * 负责将图片的标签和描述映射到三大分类体系：APP分类、页面类型、控件
 */
class ImageClassifier {
    constructor() {
        this.confidenceThreshold = 0.3; // 分类置信度阈值
        this.maxCategoriesPerType = 3; // 每种类型最多返回的分类数量

        // APP分类映射关键词（增强版）
        this.appCategories = {
            '人工智能': ['AI', '人工智能', '智能', '机器学习', '深度学习'],
            '商业': ['商业', '企业', '公司', '商务', '业务', '办公'],
            '协作': ['协作', '团队', '合作', '共享', '工作流'],
            '沟通': ['沟通', '聊天', '消息', '通信', '交流', '对话'],
            '客户关系管理': ['CRM', '客户', '关系管理', '销售', '客服'],
            '加密与Web3': ['加密', 'Web3', '区块链', '数字货币', '币'],
            '开发者工具': ['开发', '代码', '编程', '工具', 'API', 'SDK'],
            '教育': ['教育', '学习', '课程', '培训', '知识', '语言学习', '在线学习', 'Duolingo', '教学', '学习平台', '知识分享', '学科', '考试', '作业'],
            '娱乐': ['娱乐', '游戏', '音乐', '视频', '直播', '电影', '电视', '综艺', '小说', '动漫', '短视频'],
            '金融': ['金融', '投资', '钱包', '支付', '交易', '银行', '资产', '财务', '理财', 'Fuse', '股票', '基金', '保险', '贷款', '信用卡', '转账'],
            '美食与饮品': ['美食', '饮品', '餐厅', '订餐', '外卖'],
            '图形与设计': ['设计', '图形', '创意', '艺术', '绘图'],
            '健康与健身': ['健康', '健身', '运动', '医疗', '监测', '生物指标', '习惯追踪', 'Ultrahuman', '体重', '心率', '睡眠', '步数', '卡路里', '锻炼', '瑜伽'],
            '招聘与求职': ['招聘', '求职', '工作', '职业', '简历'],
            '生活方式': ['生活', '日常', '习惯', '生活方式'],
            '医疗': ['医疗', '健康', '医院', '医生', '药物'],
            '音乐与音频': ['音乐', '音频', '播放', '歌曲', '声音'],
            '地图与导航': ['地图', '导航', '位置', '路线', 'GPS'],
            '新闻': ['新闻', '资讯', '信息', '媒体', '报道'],
            '照片与视频': ['照片', '视频', '图片', '相机', '拍摄'],
            '效率': ['效率', '工具', '实用', '管理', '组织'],
            '房地产': ['房地产', '房屋', '物业', '租房', '买房'],
            '参考': ['参考', '查询', '搜索', '百科', '词典'],
            '购物': ['购物', '商城', '电商', '消费', '商品'],
            '社交网络': ['社交', '社区', '朋友', '分享', '网络'],
            '体育': ['体育', '运动', '比赛', '足球', 'MLS', '赛事', '篮球', '网球', '游泳', '跑步', '健身', '竞技', '球队', '联赛'],
            '旅行与交通': ['旅行', '交通', '出行', '酒店', '机票', '车辆', '汽车', '电动车', 'Rivian', '导航', '地图', '公交', '地铁', '打车', '租车', '景点'],
            '实用工具': ['工具', '实用', '便民', '服务', '助手']
        };

        // 页面类型映射关键词
        this.pageTypes = {
            '新用户体验': ['新用户', '首次', '欢迎', '入门'],
            '账户设置': ['账户', '设置', '个人资料', '配置'],
            '导览与教程': ['导览', '教程', '指导', '帮助'],
            '启动画面': ['启动', '加载', '欢迎页', '开屏'],
            '注册': ['注册', '注册流程', '用户注册', '创建账户'],
            '验证': ['验证', '认证', '确认', '校验'],
            '欢迎与入门': ['欢迎', '入门', '开始', '介绍'],
            '账户管理': ['账户管理', '用户管理', '个人中心'],
            '删除与停用账户': ['删除', '停用', '注销', '关闭账户'],
            '忘记密码': ['忘记密码', '找回密码', '重置密码'],
            '登录': ['登录', '登录页', '用户登录', '身份验证'],
            '我的账户与个人资料': ['个人资料', '我的账户', '用户信息'],
            '设置与偏好': ['设置', '偏好', '配置', '参数'],
            '沟通': ['消息', '聊天', '通知', '交流'],
            '关于': ['关于', '信息', '版本', '介绍'],
            '确认与成功': ['确认', '成功', '完成', '提交'],
            '操作选项': ['操作', '选项', '菜单', '功能'],
            '确认': ['确认', '验证', '核实'],
            '空状态': ['空状态', '无数据', '暂无内容'],
            '错误': ['错误', '异常', '失败', '问题'],
            '功能信息': ['功能', '特性', '说明', '介绍'],
            '反馈': ['反馈', '评价', '建议', '意见'],
            '帮助与支持': ['帮助', '支持', '客服', '问题'],
            '加载中': ['加载', '等待', '处理中', '请稍候'],
            '权限': ['权限', '授权', '许可', '访问'],
            '隐私政策': ['隐私', '政策', '条款', '协议'],
            '下拉刷新': ['刷新', '更新', '重新加载'],
            '建议和相似项目': ['建议', '推荐', '相似', '相关'],
            '条款与条件': ['条款', '条件', '协议', '规则'],
            '账单': ['账单', '费用', '消费', '花费'],
            '购物车与包裹': ['购物车', '包裹', '订单', '商品'],
            '结账': ['结账', '付款', '支付', '购买'],
            '订单确认': ['订单确认', '下单成功', '购买确认'],
            '订单详情': ['订单详情', '订单信息', '详情页'],
            '订单历史': ['订单历史', '历史记录', '过往订单'],
            '支付方式': ['支付', '付款', '支付方式', '收银'],
            '定价': ['定价', '价格', '费用', '收费'],
            '促销与奖励': ['促销', '奖励', '优惠', '折扣', '广告']
        };

        // 控件映射关键词
        this.controlTypes = {
            '手风琴': ['手风琴', '折叠', '展开', '分组'],
            '按钮': ['按钮', 'Button', '点击', '操作'],
            '复选框': ['复选框', 'Checkbox', '选择', '勾选'],
            '颜色选择器': ['颜色', '色彩', '调色板'],
            '日期选择器': ['日期', '时间', '日历', '选择器'],
            '浮动操作按钮': ['浮动', 'FAB', '悬浮按钮'],
            '单选按钮': ['单选', 'Radio', '选项'],
            '搜索栏': ['搜索', '查找', '输入框'],
            '分段控件': ['分段', 'Segment', '切换'],
            '滑块': ['滑块', 'Slider', '拖拽', '调节'],
            '步进器': ['步进', 'Stepper', '数量'],
            '开关': ['开关', 'Switch', '切换', '状态'],
            '标签': ['标签', 'Tag', 'Label', '分类'],
            '文本字段': ['输入', '文本', '表单', '字段'],
            '图块': ['图块', 'Tile', '瓦片', '网格'],
            '时间选择器': ['时间', '时钟', '选择器'],
            '视图': ['视图', 'View', '界面', '页面'],
            '徽章': ['徽章', 'Badge', '标记', '数字'],
            '横幅': ['横幅', 'Banner', '通知', '提示'],
            '卡片': ['卡片', 'Card', '卡片式', '卡片布局'],
            '轮播': ['轮播', 'Carousel', '滑动', '切换'],
            '芯片': ['芯片', 'Chip', '标签'],
            '分隔符': ['分隔', '分割线', '边界'],
            '画廊': ['画廊', 'Gallery', '图片', '展示'],
            '加载指示器': ['加载', '等待', '进度', '指示器'],
            '地图图钉': ['地图', '图钉', '标记', '位置'],
            '进度指示器': ['进度', '指示器', '百分比', '完成度'],
            '侧边导航': ['侧边', '导航', '菜单', '抽屉'],
            '骨架屏': ['骨架', '占位', '加载中'],
            '堆叠列表': ['列表', 'List', '堆叠', '排列'],
            '状态点': ['状态', '指示', '圆点', '标识'],
            '标签栏': ['标签栏', 'Tab', '切换', '选项卡'],
            '表格': ['表格', 'Table', '数据', '列表'],
            '工具栏': ['工具栏', 'Toolbar', '操作栏'],
            '顶部导航栏': ['导航', '导航栏', '顶部', '标题栏'],
            '叠加': ['叠加', 'Overlay', '覆盖', '弹出'],
            '操作表': ['操作表', 'Action Sheet', '操作选项']
        };
    }

    /**
     * 对图片进行智能分类
     * @param {Object} imageData - 图片数据，包含 tags 和 description
     * @returns {Object} 分类结果
     */
    classifyImage(imageData) {
        const { tags = [], description = '' } = imageData;

        // 增强文本预处理
        const processedText = this.preprocessText(tags, description);

        // 多策略分类
        const primaryClassification = this.performPrimaryClassification(processedText);
        const semanticClassification = this.performSemanticClassification(processedText);
        const contextualClassification = this.performContextualClassification(processedText);

        // 融合分类结果
        const fusedResults = this.fuseClassificationResults([
            primaryClassification,
            semanticClassification,
            contextualClassification
        ]);

        // 应用置信度过滤和排序
        return this.finalizeClassification(fusedResults);
    }

    /**
     * 文本预处理
     */
    preprocessText(tags, description) {
        // 合并并清理文本
        const combinedText = [...tags, description].join(' ').toLowerCase();

        // 去除特殊字符，保留中文、英文、数字
        const cleanedText = combinedText.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ');

        // 分词处理
        const words = cleanedText.split(/\s+/).filter(word => word.length > 1);

        // 同义词扩展
        const expandedWords = new Set(words);
        words.forEach(word => {
            this.addSynonyms(word, expandedWords);
        });

        return {
            originalText: combinedText,
            cleanedText: cleanedText,
            words: words,
            expandedWords: Array.from(expandedWords),
            tags: tags,
            description: description
        };
    }

    /**
     * 添加同义词
     */
    addSynonyms(word, expandedSet) {
        const synonymMap = {
            '支付': ['付款', '结算', '交易'],
            '登录': ['登陆', '进入', '访问'],
            '设置': ['配置', '选项', '偏好'],
            '健康': ['健身', '医疗', '养生'],
            '运动': ['体育', '锻炼', '健身'],
            '金融': ['理财', '投资', '银行'],
            '教育': ['学习', '培训', '课程'],
            '社交': ['社区', '交友', '互动'],
            '购物': ['电商', '商城', '消费'],
            '娱乐': ['游戏', '音乐', '视频']
        };

        for (const [key, synonyms] of Object.entries(synonymMap)) {
            if (word.includes(key) || synonyms.some(syn => word.includes(syn))) {
                expandedSet.add(key);
                synonyms.forEach(syn => expandedSet.add(syn));
            }
        }
    }

    /**
     * 主要分类策略：基于关键词匹配
     */
    performPrimaryClassification(processedText) {
        return {
            appCategories: this.enhancedMatchCategories(processedText, this.appCategories, 'app'),
            pageTypes: this.enhancedMatchCategories(processedText, this.pageTypes, 'page'),
            controlTypes: this.enhancedMatchCategories(processedText, this.controlTypes, 'control')
        };
    }

    /**
     * 语义分类策略：基于上下文理解
     */
    performSemanticClassification(processedText) {
        const { description, tags } = processedText;

        // 基于描述长度和复杂度的语义推断
        const semanticFeatures = this.extractSemanticFeatures(description);

        return {
            appCategories: this.inferAppTypeFromSemantics(semanticFeatures, tags),
            pageTypes: this.inferPageTypeFromSemantics(semanticFeatures, description),
            controlTypes: this.inferControlTypeFromSemantics(semanticFeatures, tags)
        };
    }

    /**
     * 上下文分类策略：基于组合信息推断
     */
    performContextualClassification(processedText) {
        const { words, expandedWords } = processedText;

        // 分析词汇共现模式
        const cooccurrencePatterns = this.analyzeCooccurrence(words);

        return {
            appCategories: this.inferFromContext(cooccurrencePatterns, this.appCategories, 'app'),
            pageTypes: this.inferFromContext(cooccurrencePatterns, this.pageTypes, 'page'),
            controlTypes: this.inferFromContext(cooccurrencePatterns, this.controlTypes, 'control')
        };
    }

    /**
     * 增强的分类匹配算法
     */
    enhancedMatchCategories(processedText, categories, categoryType) {
        const matches = [];
        const { expandedWords, words, originalText } = processedText;

        for (const [category, keywords] of Object.entries(categories)) {
            const scores = this.calculateEnhancedMatchScore(
                originalText,
                expandedWords,
                words,
                keywords,
                categoryType
            );

            if (scores.totalScore > 0) {
                matches.push({
                    category,
                    score: scores.totalScore,
                    confidence: scores.confidence,
                    matchType: scores.primaryMatchType,
                    matchedKeywords: scores.matchedKeywords,
                    reasoning: scores.reasoning
                });
            }
        }

        return matches.sort((a, b) => b.score - a.score);
    }

    /**
     * 计算增强的匹配分数
     */
    calculateEnhancedMatchScore(originalText, expandedWords, words, keywords, categoryType) {
        let totalScore = 0;
        let confidence = 0;
        let primaryMatchType = 'none';
        const matchedKeywords = [];
        const reasoning = [];

        // 1. 精确匹配 (权重: 3.0)
        const exactMatches = keywords.filter(keyword =>
            words.includes(keyword.toLowerCase())
        );
        if (exactMatches.length > 0) {
            const exactScore = exactMatches.length * 3.0;
            totalScore += exactScore;
            confidence += exactScore * 0.4;
            primaryMatchType = 'exact';
            matchedKeywords.push(...exactMatches);
            reasoning.push(`精确匹配: ${exactMatches.join(', ')}`);
        }

        // 2. 部分匹配 (权重: 2.0)
        const partialMatches = keywords.filter(keyword =>
            originalText.includes(keyword.toLowerCase()) &&
            !exactMatches.includes(keyword)
        );
        if (partialMatches.length > 0) {
            const partialScore = partialMatches.length * 2.0;
            totalScore += partialScore;
            confidence += partialScore * 0.3;
            if (primaryMatchType === 'none') primaryMatchType = 'partial';
            matchedKeywords.push(...partialMatches);
            reasoning.push(`部分匹配: ${partialMatches.join(', ')}`);
        }

        // 3. 语义匹配 (权重: 1.5)
        const semanticMatches = keywords.filter(keyword =>
            expandedWords.some(word =>
                word.includes(keyword.toLowerCase()) ||
                keyword.toLowerCase().includes(word)
            ) &&
            !exactMatches.includes(keyword) &&
            !partialMatches.includes(keyword)
        );
        if (semanticMatches.length > 0) {
            const semanticScore = semanticMatches.length * 1.5;
            totalScore += semanticScore;
            confidence += semanticScore * 0.2;
            if (primaryMatchType === 'none') primaryMatchType = 'semantic';
            reasoning.push(`语义匹配: ${semanticMatches.join(', ')}`);
        }

        // 4. 上下文权重调整
        const contextBonus = this.calculateContextBonus(
            originalText,
            keywords,
            categoryType
        );
        totalScore += contextBonus;
        confidence += contextBonus * 0.1;

        // 标准化置信度 (0-1)
        confidence = Math.min(confidence / 10, 1);

        return {
            totalScore,
            confidence,
            primaryMatchType,
            matchedKeywords: [...new Set(matchedKeywords)],
            reasoning
        };
    }

    /**
     * 计算上下文加权分数
     */
    calculateContextBonus(text, keywords, categoryType) {
        let bonus = 0;

        // 根据分类类型调整权重
        const typeWeights = {
            'app': 1.2,
            'page': 1.0,
            'control': 0.8
        };

        const baseWeight = typeWeights[categoryType] || 1.0;

        // 关键词密度加权
        const totalKeywordOccurrences = keywords.reduce((count, keyword) => {
            const occurrences = (text.match(new RegExp(keyword.toLowerCase(), 'g')) || []).length;
            return count + occurrences;
        }, 0);

        if (totalKeywordOccurrences > 1) {
            bonus += totalKeywordOccurrences * 0.5 * baseWeight;
        }

        // 特定组合模式加权
        const patternBonus = this.detectSpecialPatterns(text, keywords, categoryType);
        bonus += patternBonus * baseWeight;

        return bonus;
    }

    /**
     * 检测特殊模式
     */
    detectSpecialPatterns(text, keywords, categoryType) {
        let bonus = 0;

        // 应用类型特殊模式
        if (categoryType === 'app') {
            if (text.includes('支付') && text.includes('金融')) bonus += 1.0;
            if (text.includes('健康') && text.includes('监测')) bonus += 1.0;
            if (text.includes('学习') && text.includes('教育')) bonus += 1.0;
        }

        // 页面类型特殊模式
        if (categoryType === 'page') {
            if (text.includes('登录') && text.includes('输入')) bonus += 1.0;
            if (text.includes('设置') && text.includes('配置')) bonus += 1.0;
        }

        // 控件类型特殊模式
        if (categoryType === 'control') {
            if (text.includes('按钮') && text.includes('点击')) bonus += 1.0;
            if (text.includes('卡片') && text.includes('布局')) bonus += 1.0;
        }

        return bonus;
    }

    /**
     * 提取语义特征
     */
    extractSemanticFeatures(description) {
        const features = {
            length: description.length,
            complexity: 0,
            sentiment: 'neutral',
            topics: []
        };

        // 计算复杂度
        const sentences = description.split(/[。！？.!?]/).filter(s => s.trim());
        features.complexity = sentences.length;

        // 主题提取
        const topicKeywords = {
            '交互': ['点击', '操作', '触摸', '手势'],
            '视觉': ['颜色', '样式', '布局', '设计'],
            '功能': ['功能', '特性', '服务', '工具'],
            '数据': ['信息', '数据', '统计', '图表']
        };

        for (const [topic, keywords] of Object.entries(topicKeywords)) {
            if (keywords.some(kw => description.includes(kw))) {
                features.topics.push(topic);
            }
        }

        return features;
    }

    /**
     * 从语义特征推断应用类型
     */
    inferAppTypeFromSemantics(semanticFeatures, tags) {
        const inferences = [];

        // 基于复杂度推断
        if (semanticFeatures.complexity > 3) {
            if (semanticFeatures.topics.includes('数据')) {
                inferences.push({
                    category: '商业',
                    score: 2.0,
                    confidence: 0.6,
                    reasoning: ['复杂数据展示推断为商业应用']
                });
            }
        }

        // 基于主题推断
        if (semanticFeatures.topics.includes('交互')) {
            inferences.push({
                category: '效率',
                score: 1.5,
                confidence: 0.5,
                reasoning: ['交互性强推断为效率工具']
            });
        }

        return inferences;
    }

    /**
     * 从语义特征推断页面类型
     */
    inferPageTypeFromSemantics(semanticFeatures, description) {
        const inferences = [];

        if (description.includes('输入') || description.includes('填写')) {
            inferences.push({
                category: '注册',
                score: 2.0,
                confidence: 0.7,
                reasoning: ['包含输入要素推断为注册页面']
            });
        }

        if (description.includes('展示') || description.includes('显示')) {
            inferences.push({
                category: '订单详情',
                score: 1.5,
                confidence: 0.5,
                reasoning: ['信息展示推断为详情页面']
            });
        }

        return inferences;
    }

    /**
     * 从语义特征推断控件类型
     */
    inferControlTypeFromSemantics(semanticFeatures, tags) {
        const inferences = [];

        if (tags.some(tag => tag.includes('按钮') || tag.includes('点击'))) {
            inferences.push({
                category: '按钮',
                score: 3.0,
                confidence: 0.8,
                reasoning: ['明确提到按钮或点击']
            });
        }

        if (tags.some(tag => tag.includes('卡片') || tag.includes('布局'))) {
            inferences.push({
                category: '卡片',
                score: 2.5,
                confidence: 0.7,
                reasoning: ['提到卡片或布局设计']
            });
        }

        return inferences;
    }

    /**
     * 分析词汇共现模式
     */
    analyzeCooccurrence(words) {
        const patterns = {};

        // 分析相邻词汇
        for (let i = 0; i < words.length - 1; i++) {
            const pair = `${words[i]}_${words[i + 1]}`;
            patterns[pair] = (patterns[pair] || 0) + 1;
        }

        return patterns;
    }

    /**
     * 从上下文推断分类
     */
    inferFromContext(cooccurrencePatterns, categories, categoryType) {
        const inferences = [];

        // 定义有意义的词汇组合
        const meaningfulPatterns = {
            'app': {
                '支付_流程': '金融',
                '健康_监测': '健康与健身',
                '学习_课程': '教育',
                '运动_数据': '体育'
            },
            'page': {
                '登录_界面': '登录',
                '设置_选项': '设置与偏好',
                '用户_信息': '我的账户与个人资料'
            },
            'control': {
                '按钮_点击': '按钮',
                '卡片_布局': '卡片',
                '输入_框': '文本字段'
            }
        };

        const relevantPatterns = meaningfulPatterns[categoryType] || {};

        for (const [pattern, category] of Object.entries(relevantPatterns)) {
            if (cooccurrencePatterns[pattern]) {
                inferences.push({
                    category,
                    score: cooccurrencePatterns[pattern] * 1.5,
                    confidence: 0.6,
                    reasoning: [`检测到模式: ${pattern}`]
                });
            }
        }

        return inferences;
    }

    /**
     * 融合多种分类结果
     */
    fuseClassificationResults(classificationResults) {
        const fused = {
            appCategories: {},
            pageTypes: {},
            controlTypes: {}
        };

        // 合并所有分类结果
        classificationResults.forEach((result, index) => {
            const weight = index === 0 ? 1.0 : 0.5; // 主要分类权重更高

            ['appCategories', 'pageTypes', 'controlTypes'].forEach(type => {
                if (result[type]) {
                    result[type].forEach(item => {
                        const category = item.category;
                        if (fused[type][category]) {
                            fused[type][category].score += item.score * weight;
                            fused[type][category].confidence = Math.max(
                                fused[type][category].confidence,
                                item.confidence
                            );
                            if (item.reasoning) {
                                fused[type][category].reasoning = [
                                    ...(fused[type][category].reasoning || []),
                                    ...item.reasoning
                                ];
                            }
                        } else {
                            fused[type][category] = {
                                ...item,
                                score: item.score * weight
                            };
                        }
                    });
                }
            });
        });

        return fused;
    }

    /**
     * 最终分类处理
     */
    finalizeClassification(fusedResults) {
        const final = {};

        ['appCategories', 'pageTypes', 'controlTypes'].forEach(type => {
            // 转换为数组并排序
            let categories = Object.values(fusedResults[type] || {});

            // 应用置信度过滤
            categories = categories.filter(cat =>
                cat.confidence >= this.confidenceThreshold
            );

            // 按分数排序
            categories.sort((a, b) => b.score - a.score);

            // 限制返回数量
            categories = categories.slice(0, this.maxCategoriesPerType);

            final[type] = categories;
        });

        return final;
    }

    /**
     * 匹配分类
     * @param {string} text - 要匹配的文本
     * @param {Object} categories - 分类配置
     * @returns {Array} 匹配的分类列表
     */
    matchCategories(text, categories) {
        const matches = [];

        for (const [category, keywords] of Object.entries(categories)) {
            const score = this.calculateMatchScore(text, keywords);
            if (score > 0) {
                matches.push({
                    category,
                    score,
                    keywords: keywords.filter(keyword =>
                        text.includes(keyword.toLowerCase())
                    )
                });
            }
        }

        // 按得分排序
        return matches.sort((a, b) => b.score - a.score);
    }

    /**
     * 计算匹配得分
     * @param {string} text - 要匹配的文本
     * @param {Array} keywords - 关键词列表
     * @returns {number} 匹配得分
     */
    calculateMatchScore(text, keywords) {
        let score = 0;

        for (const keyword of keywords) {
            const lowerKeyword = keyword.toLowerCase();
            if (text.includes(lowerKeyword)) {
                // 精确匹配得分更高
                const exactMatch = text.split(/\s+/).includes(lowerKeyword);
                score += exactMatch ? 2 : 1;
            }
        }

        return score;
    }

    /**
     * 获取指定类型的所有分类
     * @param {string} type - 分类类型: 'app', 'page', 'control'
     * @returns {Array} 分类列表
     */
    getAllCategories(type) {
        switch (type) {
            case 'app':
                return Object.keys(this.appCategories);
            case 'page':
                return Object.keys(this.pageTypes);
            case 'control':
                return Object.keys(this.controlTypes);
            default:
                return [];
        }
    }

    /**
     * 根据文件名进行基础分类（兼容现有逻辑）
     * @param {string} imageSrc - 图片路径
     * @returns {Object} 分类结果
     */
    classifyByFilename(imageSrc) {
        const fileName = imageSrc.split('/').pop().toLowerCase();

        // 基于文件名的简单分类
        if (fileName.includes('mls')) {
            return {
                appCategories: [{ category: '体育', score: 3 }],
                pageTypes: [{ category: '订单详情', score: 2 }],
                controlTypes: [{ category: '卡片', score: 2 }]
            };
        } else if (fileName.includes('duolingo')) {
            return {
                appCategories: [{ category: '教育', score: 3 }],
                pageTypes: [{ category: '导览与教程', score: 2 }],
                controlTypes: [{ category: '按钮', score: 1 }]
            };
        } else if (fileName.includes('fuse')) {
            return {
                appCategories: [{ category: '金融', score: 3 }],
                pageTypes: [{ category: '支付方式', score: 2 }],
                controlTypes: [{ category: '卡片', score: 2 }]
            };
        } else if (fileName.includes('ultrahuman')) {
            return {
                appCategories: [{ category: '健康与健身', score: 3 }],
                pageTypes: [{ category: '我的账户与个人资料', score: 2 }],
                controlTypes: [{ category: '进度指示器', score: 2 }]
            };
        }

        // 默认分类
        return {
            appCategories: [{ category: '实用工具', score: 1 }],
            pageTypes: [{ category: '视图', score: 1 }],
            controlTypes: [{ category: '卡片', score: 1 }]
        };
    }

    /**
     * 合并多种分类结果（向后兼容版本）
     * @param {Array} classificationResults - 多个分类结果
     * @returns {Object} 合并后的分类结果
     */
    mergeClassifications(...classificationResults) {
        const merged = {
            appCategories: {},
            pageTypes: {},
            controlTypes: {}
        };

        classificationResults.forEach(result => {
            ['appCategories', 'pageTypes', 'controlTypes'].forEach(type => {
                if (result[type]) {
                    result[type].forEach(item => {
                        const category = item.category;
                        if (merged[type][category]) {
                            merged[type][category].score += item.score;
                            // 合并置信度（取最高值）
                            if (item.confidence !== undefined) {
                                merged[type][category].confidence = Math.max(
                                    merged[type][category].confidence || 0,
                                    item.confidence
                                );
                            }
                        } else {
                            merged[type][category] = { ...item };
                        }
                    });
                }
            });
        });

        // 转换为数组并排序
        Object.keys(merged).forEach(type => {
            merged[type] = Object.values(merged[type])
                .sort((a, b) => b.score - a.score);
        });

        return merged;
    }

    /**
     * 获取分类统计信息
     * @returns {Object} 统计信息
     */
    getClassificationStats() {
        return {
            appCategoriesCount: Object.keys(this.appCategories).length,
            pageTypesCount: Object.keys(this.pageTypes).length,
            controlTypesCount: Object.keys(this.controlTypes).length,
            confidenceThreshold: this.confidenceThreshold,
            maxCategoriesPerType: this.maxCategoriesPerType
        };
    }

    /**
     * 更新分类配置
     * @param {Object} config - 配置选项
     */
    updateConfig(config = {}) {
        if (config.confidenceThreshold !== undefined) {
            this.confidenceThreshold = Math.max(0, Math.min(1, config.confidenceThreshold));
        }
        if (config.maxCategoriesPerType !== undefined) {
            this.maxCategoriesPerType = Math.max(1, config.maxCategoriesPerType);
        }
    }

    /**
     * 验证分类结果质量
     * @param {Object} classificationResult - 分类结果
     * @returns {Object} 质量评估
     */
    validateClassificationQuality(classificationResult) {
        const quality = {
            overall: 'good',
            issues: [],
            suggestions: []
        };

        ['appCategories', 'pageTypes', 'controlTypes'].forEach(type => {
            const categories = classificationResult[type] || [];

            // 检查是否有足够的分类
            if (categories.length === 0) {
                quality.issues.push(`${type} 没有匹配的分类`);
                quality.overall = 'poor';
            }

            // 检查置信度
            const lowConfidenceCount = categories.filter(cat =>
                cat.confidence < this.confidenceThreshold
            ).length;

            if (lowConfidenceCount > 0) {
                quality.issues.push(`${type} 有 ${lowConfidenceCount} 个低置信度分类`);
                quality.overall = quality.overall === 'good' ? 'fair' : quality.overall;
            }

            // 检查分数分布
            if (categories.length > 1) {
                const scores = categories.map(cat => cat.score);
                const maxScore = Math.max(...scores);
                const minScore = Math.min(...scores);

                if (maxScore - minScore < 0.5) {
                    quality.suggestions.push(`${type} 分类区分度较低，可能需要更多关键词`);
                }
            }
        });

        return quality;
    }
}

// 导出全局实例
window.ImageClassifier = ImageClassifier;
window.imageClassifier = new ImageClassifier();